{"name": "lending", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "@types/yup": "^0.29.14", "axios": "^1.7.9", "bootstrap": "^5.3.3", "clevertap-web-sdk": "^1.17.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "firebase": "^11.2.0", "formik": "^2.4.6", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "next": "15.1.5", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-datepicker": "^8.1.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-toastify": "^11.0.3", "sass": "^1.83.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/clevertap-web-sdk": "^1.1.13", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.5", "typescript": "^5"}}
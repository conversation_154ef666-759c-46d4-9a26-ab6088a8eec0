{"v": "5.7.0", "ip": 120, "op": 230, "fr": 60, "w": 450, "h": 500, "assets": [{"h": 500, "w": 450, "id": "1", "p": "data:image/webp;base64,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", "u": "", "e": 1}, {"h": 108, "w": 386, "id": "2", "p": "data:image/webp;base64,UklGRkAgAABXRUJQVlA4WAoAAAAQAAAAgQEAawAAQUxQSFkWAAABGTVtGzDKXf6UJ4KI/k+AsG84CNq2zRz+rLcfQ0RMwKDK7onm71MFbmzbjZD39Fe40Hsy711K5kvwrg8qoAdyeqAvr/0zq68vImbDiAlw4DaSIi3zuruX7p5ASdsmRY7zR1aLJXNLZi+ZfQCGA3nlc/gI7KX3zMzMTGJWQ2bG83RFRKZmOmq0jAhHbhs5Us/mKMvVt3mBL0mSHMm2bcssJ17rC+O1OrH634zVAowxXpmhROmqYpY5F/qMCIuObYtu25CAAKoUNWvfZ5uWg34Awj/Qfqbqjz5pffpFVfU3+kn50lcPqL/69TbZD0KoVn/3+9XEAEMdgp6P9OMfJaX1k5+F64fG+vVvnwz9/Ih+dxr/+BQNcOE8FoFlml+khWW6rA+6lXBnYNz8vN26qv3zR1FlBDe0WkfmB+0kfq5CRP9TSe7Q03y5pHU57Bqf9wRO0Ph74J//wevr2NjC4TGfDHk4wnHX1zw7dxktLuNG6q9deRX6q9yIIkk+Iw6EoJo6x3vOyc4IvkHZyRZ8g45bvhKcNUKckAFJ8UeWp+rkor0FYoA5ixFhtJ8Tx3f8GdOX/D0fHh8PsY/jbqt6ZX5pYeVGumLt7nAzGqYgeg6gQLD6MMpRCNx5UOP5AhAujILTMkIyixBmSG9GQFDNF0yhyGQrhJPDkMFZDEbO7Sx8ubl1cLTLw5H7QejNzl681L+8v3rH1at9okCTIyBQMAqR+rVUSSG1Kwhj9N0D0uGV4KjCEkDXD2cmkmAiE9URgU8VtFbb2oOdhMmC5IwEzswMThsb7Rxsru9g39EULE7qXr2RHr66v0poRN9NK9KwZUorj7vh3PReikch9QI7g6Ei0oDVROwhjclpBiY6+F6AAiwfJRpiC0fRiDnI7fc4f/vZf19vre/wVpzWwq9avGHtwatvadq6A3okRBjGZa+EwudgVmn4rBBOgs3XoUsHbYldUWmIISg1BVoHNhQF9IgTIlmNZhW+tSrE6IQaQSXlPLlNmaKYgd83h5txKlZj6eqle1cfW1tDjxr0qCBACi47tlEkmVaowcqAYBjkuapLNoHqNDDSnrfvSSgd3LiKhjbhRKlGynPkXczA5Ej51X9f2f5tcN7+ZcSECYeO7ZhEPfRCI+MyKb+A4UHmr6q7amZte1aSqalZwUQCLo/SzycYeRkMAeirgs0UfJU9MrWdP++ZE6ec/n/p37d+2909x9+9sSEQQjT+UEKGVFLhVD6TZCWmkkADQFljKFztzdJkD7EwOp7e4KE5Yio1SAaZBoJNYhmYFu+JU+Txq5+98cNGPO1qjFp04caMbtsI6gnXoSDikW3BgCkMoKLSlFAUpiyLenBwDE/O0iroqoaxozCASTyqNEJptBRLrIrY0+k95zRO6y+9UP/Rxqgjl050+wo/ApBhrUBUs5+M/GTAybyK3EBQAAaDRgAqbFu9pCaCMydMieBDO+oGZVWsqfeex3H88rMf/x2Lr+y6d25GFBEUebNx2BPUeq5KaZCZCGVNZhbDxjCQGnNs9gGYMtcvIDnZ/Dw/hM4AVfEiFkQRHlPrnWMaPvfkrwNjTXl2J4oEqCJgqgTKIB3SooHJRVomqJy3XHDBT4XyZSWAYzc/Nuw5FDdDDSkCw3rKWk5BBti0e0ceDZ548V+xhrx4Migo8+kl0UmhZVIRGIsNIIlrY5r+SkR2MpjM81CBBUDwD95WjtH5qgi9OhuLxIv81UkrNpXIg/c4eOrxE4B575aYf0QtTWVVASZlMlpIKdHIGik7jNGCSKERzIYNDglgwQp0FZ71L4kBPBewdUJMavUKpr2TpGLBN/3e8w/3AbxZ5uCoJDtIX80aizU86V8STIY+WwZT0avmlGjcmwMmDkPkw3RwkFlRTGPBZoSEAnATrBxGhXDinX/rNgKyqTI0yjygjmIACpK1IE3wmQJFCAPDMGQAMFU5lmEN6rSpwhKoAg7wiRopQPRFjFWhBg9Wje0gSSapJYni8uA9v3fvlTVlCDlHchCEM+/BrBIAQQR9vWXO7aJvJAT08QWMnBiTZZ/VFAno1asYiJHFH7QSAvM6+gBB0JNXMYA0TgyoH2v5H4NEwQARQH/dVbQ9GjO3ACQw2icqEwLl+SjfhBH0y11lyXEYmcF6z4EkQVL8cZ4LbTYtAAGJiAYInPX+RTaZx+OYM7iOjFRS3oikKqAfRkIhBJqjeU1z1mo85VGKnFhgEBOrrtW3ZNeq5fxoBCOwt6UyjDyUTBQQmpkw2wOdZWaA9RqnAUdOyG3PqkoWPasiYXbdSkyFDdzKbAhUcCSsMSighkPT9JrZGestOhPUzUgcTxBzW7jYOqJuY0H3V+pqwpJnDLNavoLuKcTrBZTQ7scmzGK2F3AhnQM2AVKO45hTzpknHGKvsNo8VNGnBrF4WIxDUsFVpUBsARXRCnzqNL0mzGGWZov/Qt3ec5FghGFMGIuTw2woqm2kYpURxMtuiWvKbGHiwBVpyhwSxgoQENphCJhBezo8Zklh2cTUNRVXMaSImGLCCDkjc6Z8bryr7xEzKay2wlM0p/vq19pMaSGpNeNbqbHE5d0uhaoP6vye+aE+/LDq+fn+qOdLoHe398L01l9OLeFp2L7ONABSQlZnCCNPmXfAtocDUA8MAM0TKJCKg4dcyKhhGZ5jcCOoFaqzVtXtWD44uR8ezAcf1NNVn8btww+J/dHHL9/+9S/ROH/z7e5+fd8vu9a7ZEerA8wd4P3RpTREiKlgjVxALrNXOnI3uhpIcilJKqQZa4qqUDsxo73AV3BU5IU4GpMRtgpaVpApNDKP71c1eI/av3ddFSFRh4PAlVJZgEWeYADrcbHDJoPS6NwsALF1RkUX+EZd4YiBDJ9JnWQKb+6EEVBHVhBRTvbvHR6o8Ruo4ZXcEzhkKFqIZcXolSkIWgUQ1B+tfx1FL/LpN/ZkKyNODlhDBBNYwMB1E//R0AHeH9s/ZhWvtJrndcpKvNWqX9S2Rpl6HGUnevsKxP6929/Fk6qbv3lju3rZoZ6zn2RytJKlXnGfMOTGDOtNzfTyjAtCi1GDhUZ2h3vvdiZ/c1G/EjsmAeL0zucQKKC7DiStfVELDF2jQ9wFHPU4y44Ell2yf++6BJOAqzUQzis9hVADyMhgCYJiBhkhTaPwyYqF0jz7HFBKzTyhoQJoHs8EL7ySDUFsddC8ey8HuJkhc5/QR0IfX/AbgsKWYNMGuDrAQchbVaQFONITXYXgjo/el6jIcjbKp4wauwIQikD59667G4BZhQQc4IAdEbCaLyLPpqKoi4CDim+QzkqDIaEi0o4+OEQKj1kpsg4Bmtxp0s5zTYKTczUj+fdel1uDNqx4gASJWEqZL2mi5MZgz45OniqN0ZV7BbGGqiHNUTYcsG0PrCACFh8nLxFQ/96vh9nh3lfv3+nc1Hr5/Y/DlrgdCHboN/CqXjC9AHtXhX/vusPqR9Fl+pZ5YZnBPl8/rbByYIpeSOYf9qpHaws80wgDVB2zvDC1butybPPvXVdMNXLnoyjWWLWXva0V1hU2rtF7WWsWcAZI7PNt075lC8C/d10isCcS4+Qp0k2eRp61XWavlJRyqlvb7Aj0HlBXh46yp7oDrvRVSuTfwLqClEyMB7cLn7wihRH8e9c9aPTIB4C+hHuSQxpW4H1CeW4YD0FliNRBn5NSgdOxpIbQIkPGtd9ADuY+4WUmgZF7/Xv3/1YvUSEwPiBUgAg/iLogEMmgmhQVyaanLA9IgdfddAzpSjD8Gc2Ni0Fk22Y1AJMXioPTuZF/73pQMfpxirDiPBDkAlLKHjmi/JZ9CS2ihEeFGdzinw/LuIo4K4qISPqsG6YFnbmce9/8FwLqvm4KVfXDjTnJEDyArJQgIxQ15F769sp60RWf51+x73w7fDrv7MK7HgvX4/TuE4Acl1oI0LK/d1yRG94WrVsJPWqIXE8Krvx8X4Cbtnr1Tt68X49qnY9pa223rFXTdldArrVjW5W241Gzw7itNdhGBmjYHhB10tRG8u9dj0HHN54tqmYKYheEVlxt9LxSCwae5LuTO1C3Eh/Xma+ergCOMjhbe4QKp/n3rkfrWMh22P5KQXZtua0dJQ+z5wU8S2LahAQDtHRc0UUrJcNCjsPxmUJecESQXOj8yMYklU+Umzq+g+/fuy5Lqkzw5mitNIMjzKFLC60FWBBldGdQJIGt06njJ0xVmumeFri6g+YLDaWiPPc4Bb8uQv1714MQWZDcYe9I8ppq5ZQGCLQ5espPTQjSnpnq1KQ2nofzmDWQFdE4SXSX5QEuaKmNlIHrUv/e48tamGg0GSK7A8eQ1rvfychDwwNVc/b4q6yHn7SpI2dgIp8offX0jmrv9e/db60ZqYaCfePW04K81mLn5Y9KOTKh/2b31kuWDvD+mCtanMGedGQXQBfOUdRCApQxsDiwZkIOeBxhWda9NkJLbSFqyLc9/Hu/HgGbPMDrKmUvoyITdPLnhaTLDAlPrX2daCQAZMNQ1mJAqiZtcXY4x/HychkYcvfZ1L93XUpx7gK1T6krro4YI+YrYYBT8WP0tji/K6xmCJ1nZEzwQOwNosKm8UJ8HEmtCr89ArInZKIRKcO/9+sReXG3XHfqdgXnKSikgWCBnUQW13LRgF7wiNWaSqZRmLSpjEeVK3gSlS336gDvD9yqYc+n+FH3/KNqmBNOpKeOgCRKDTFAKNSoMQiPKKSGL7CnTM3mPCPTVO5OwzCguF0YVO+dAR/e8SeCh6WppepfxFg1tu+SgC3RTS34/HA+uzcxg2egZq4mF5oG4uKVVu04pXd24D1uJvACxIG1SzZMbQOHU4jcyIAUj5kZXiUSuChV6iAkM+pBmxrIdDWa3GfWgdvKLv/er4dDdn/XQjtK59Z6qo2jTtlg3ByDIn/RsG6bzmajjPzYIG2LESZsZ0UHeL+OWMdR+V+OWcoJcU+gThp9vmQyVB1DtJmni2bVzRhmVqqcVIE3eZV3JCCgHhV9sIAlWoGjnerRZocC0AxtBILK2FLAm/d4YKjDzhBDmDRCEFMuc2LoiqaocKRAFuNQF4EiqJUaeaLnQUWmVyfZcTZraI/+OuuWjOImMJd/gzB1yCPGv/fr4RnVV7vyJlRWJQDfz6kB5Oin+ZDTY81nlENE+qKEnzfVIGM2t6J9uveVobR+KG6XxcxY+c2/dz0gM9mEHXOqIzy3VRRW2MYuBc7MlkAsVKhBAtkH0rnIpB1OrgumaFYOPPg6dTJ8KkO7944vtLw+1qgtqy/Xvka+aap/tXehetsuM0P9Ctq/dz1api57pSS0/ZjqfH5q0/El6a0/FC1l0mzuEGlmQh6qgLFryyTAIQ9Jc2itd3bk/dw6rwN3raIc5RzjjQdhMi3GjsjAmKLyecMhOGk4b3cNdce3W2/uNRf+XTpOa3je2doDvHuHf5/OAJqiMiWru6iE609J9ATTI0KUah1BF41gJeNJGXh3nKgtGiprB9t8YpMKoFDs37su61SGZ82zL2pghEBlGQSXmFfOBVQlINA+gzvy1i4LbRdXVrPbayknDQDxicCsjtZRafUO6gDvjxCRpMHnVXNG0242R8DdBFDDAfV83iZ7SlPsyLQFwm5mAiSYxOfiX2dJ4eNOz67sj68dChPh3/v1CJ41KxsAk2nUZ+5BWigbKgbKjuqDvOE9UF/NUpQpEfn52d5SD0b1GNYB3h8kak0tLHLzoGr5CmPum35m1gJt6yWd4VoDpmCGd//rNFoZ4tx7PAXWbY292xE3mjmL7eGA6Xb89x3hi6pSPK9qqFUDezerf+/Xgy+uKoMO+3VYhBObsMXClqnzlhoMSHQ6p0EACa2k0s0rHTW1dBkY5JvR0YS1sJB/77pcAjvErb3MpNaZ+lwFuIa0u1HjeZDYQQ6Fssq9OiBnq9xsZPonlXQfyUlzhz8qFxkgDUmxProNYwXKuf691x20pglpK3cPdVuY5x0K4QLrknQZNA7UcUfgdY62R94F+oSWazoViTVlxgUMRo/i9gXKYRFBoE6iKY/Gv/frguWSGmq8nkaYBzRivNk5jiAPZDaA3RgIh6Uz2bYWVoYi5nJKG97Y02DRMQ+VrRnkj/x7191+pnszht1a/r4TfphgAvMe7SEcWvtCXlXuC07WtcY/D3SrsvzrxD51gc5Y5ceoUgYest7wx6/LUjqcHZSlrmMphn/vdRnznjw3W+FNxmkNEwcs4omiTghOnsyBP9EODmPb20C+OerYnttucTAaqnS7egj2xKRxLlDrHS68X/EUN9HXPfJqaFvh9TemrdyiNU0ZJtJN4FGxRW0Ar4gJMxxUyTjwvKBMmCK5F++6G2IhOpcNjWP94XBDV0rAZQ7Zx+CZGdTwqNxj+uuWvKAtNvAMRnVRSszgjOQNSyc34sqMrjSGf+/D/9dv5BQLS853WixFKvIkLiwnwkAV8jKyAbKR9Wb3R1HidNyk0le8Hn+DwJQ/Ik3EONIR8NG/d+XPo2RGJx/9iQlEqCzGjBOVSCgvpKHC692QbHWkAP3YER9mYEBDJ2hepNQTDQnZqnaFf++6G7w6ZT0UdRjdCQioZwA7QBktu7vKhdGCnDFgM573wwyYz/5jKjsJyAMYVfr3ruswfB9tsH31+l9Xk4vFSFEjbLlMKxlo60VXSRUyuN0qFqLkz+UHWWoX+feux+f//5XecIM3ktRLBvXL/BGmzldSGUHVFmWMBbG1mFm9EGzOjcm/9+fNttTyam2Nlln3tPgbdn4lpq4X9465ISmzSk9UWmimQaIXrqcEwqGMRv/elx/aFjuDlPJIrNIkZl48z8pOERoFyzqoToIHuUz4CArTaX/nBQepnG8aDB+KjGDCcevVsfr3rusgwoRXSeirpgXCBAXOO5zgR5/ngTNqBsSpCn4KiJZNwSuHVAREvEeFL1D2qHKA6PG0ct9NE96/9+shcBsUTB1Yr/GWcCuqkwDxVcEY0OqwhM+/gaCiSJpx8l8FEOcUv6WtvCLYFuCxQYSWKtrWDF4it/n3rrtjquffKWmqzvVS5+gBmkDWrBRqWzXoEQYtBbroiBykqw4GR01SqC1F4HGMcmZiAJX9e9cVIlDphbp1m+322puUkYHmPBWaFHTYOvu7rrb58OW4EucYQjKvrzhRi/bvfXrK+D6u5Y6RtX+v39pHZ8ag000kba3X2ja3AOg/X14A54bWYteuM5R/73lbJiJlJAy8zN67XJZVzFud8nLl1R4gC95UOlxTrx5RB3h/zOa8LB3b6nJ6RWG5JJwVnDJjfpybDWFGdDGRKvrYsqSKGVFZNELAsTiwqzGREA3O9O9dD4us0+M2iIxH6ap205NoINeVUX0LIvtHExsIP99ye1IB7US35m9BXmxxySoLOhIHewsN6SrTeo8hA+a5R9FAdu+9XRYcX5eTeYPzK7gR7lEKh1tdk2w3gafKRYEFlMYROsyVBvs8J7ixvBfpKk9IbGiPPwBnlbxBhoJMBoRmV8RksXmBRUID9bsm2W4CT5WLAgsoWQ2uK7QmDY8ReR9XhEbhTlSkwRIgYmKFuxzurjQR64Co2gGABV8ebtJTiDqRcvNKsZGD/XvX3bn1udznaeQBLegC0BvwPt+qieAU5cTkBONJ9+cRk497iAqhqV17h/LrPPr//Q4AVlA4IMAJAAAQOQCdASqCAWwAPm02lUi/oySsJPVZk/ANiWNuluG97l6EwXCrXf3T/39N0mJ858gGzUZrnp2VXZY/336ze5Pbxf1T0Afyb+tf6f/S+7r/lf2H9y366ewB5TPsefzf1AP1v9NX/he6d/Zf+t6R2qrHfd2b9LdC9gMySJUyUJQn+98Ab6v/nema/wPI99VewR5WXrQ/WX//+5X+x4HOHikxiRSXxk9lPXO0BSJ9Ujt0IOm/HC9YEvjJwjzlRKDbGi5q7ez0kKAn1rCRAvwzJ5Db41pM73tCCqrLXDR6CpIIlnZw/l9mJaHPnDD37/OGYwuTnwKg07exLRKwW9PdZCTSP4mItj2cmzbMCn/OAWEPG8zny7od4WFKMc8WErh5TbAW0XBoo69iMz/d6zvvuMU4qiFz+bA/2tKzlHiEf/axas4tVdiTv/wk84b0rUQA/NZAzJQL97AXWVp93TObWD2UYgf7vOL3lOINDo9a1oMPrwv2wgWGY2iA2p8ywusSpZLex24cNM92szf5YUXtZlRo9UEro/Hv3eac4MiYy0XN83V7Upbr081GUWCMWJhWx3Li71H1icgJZ2QkNQyl9TOeEWeKRt9lXrA9efEfuc7gNUgA/vW7n/ukXOl9b3yE1wDQ//4/a+77bLyNXm4kf115L9svFd18SVuz8BYigQ61pPW/1U5RFDp5xK2a/GaAV6ccNQ/95rusP8ADb0ZaV/gEulo2juY5PjK+0rm0RBenYjO1PY7YiggolDGB92hHT3lrEAbtbp9KNLWhk7tYqninmAEh9olRPvDGNxxSXeKQUym4v5//FJm+UONEb/pt/dkv2QtmcUgMk0rJeCs11Pd8VcDVfmNRWI40YTe1vYa3byyPIF1AsKlxZ5ZlMszFVrR08lSTH2BM1x5FoUpj3bU2PCA+xHk1CUf9GVCLVWOM/XAAtM056t2qLJCMmbyHHIB6+wof8coR6y9O0JeOoAGk1jTh9t8YGZPQHV34Cc8/H3nvJ45Cl9mn+n3/7tp/9k+f/7wmGylQuo1msDvTgG//NxE+P+qT/Q46/1RWTLFVkOU0wkOkdEQfvOaUT6G//hLVgrjDFErCjYfBJamWOSDQgAkfcM2NITD8mWarz/A9xfx9LEGh3WeocA6QpPe15/j5YfR6EEJXVsZj7l45DUBHogBEhJ0ZJ4YbNHNK12SPGzCmJRPg0KFQUPuSl8HrTZwrVC628m0hFMbm+bGy7g6eQu5tq8/RGTMqfKEoDkGcfrQdjVe/TnZTfW4BIEg/Lj8tHJdprmHC8NCm16VQ85CWPeb1kQJu1kUFT6QT9R/CTbzj9FJK+fn/qCzRROFVL6wPs6dGDpr42vWe6HsukPkqgmQFjeU3h1kxdqMZpCf2Y5FTXQo8YJh57kmwXMwzCrWlFIeN6/06orSHKYf3bbowWJjlQfUSw5jNkAYNHkds6svGwUAAAAE9PdNG4POn/aMJsH9S7KnrsKMYN1cANbgEbqnyFymy5lJjG00yLGpfUfv5gEdiBoMw2oteBkBVYLzWWACxraC01+y5YlClUJ+ePyGEnJbEi/kjbh/RQACbvHstuZPJzzeSUsiP1xsgFazM1kAMS4cXkrsMVZQgXi80uC6IoNLDd8nQB6pK7Xr0pABp22iD7SXeH49ROXayVhmwFWD2xDKIFreIRRvWsnOZ1t8AbXcSBK6ihlaI2QRYvWUjTOjvthy2unKPmD4kqzlC931uO6nCehBcDBnGhh/tCwZyV8JrQi9Ew2Eswtp6xSxWUzWi6eTIfx7qSCOPjxwrzP3EE7C5htyu5mgs9/yD/MH33f/krspKeOs9zqCwY+0ca1lNXKMlRXd9AQit3//As2Y5eMvTxMhJw/3VFpTQy4LtgkooqFxmlYGc58u8fNu00rk7d6/32AUIhDSfN7DkHijF2zShBGnOAQyNjLWwANS87Y0zrMN4RRrcLKjVxQsACpSe6O2A6pV0+NtionU3MvptGdiJIVM/Oj9uCUzlSNY3EC002zo2gAUznvYD2jyAzPJ6z345Fl6oye4J9cqkKx0c0ctGL6GxfcOROKaH16LpYKDVqrBCShHjzYs+ZYDDE2kp9FeHHX5uw1ObxwTcTYx0CHjj2j2aEeurW5ZJ632IgqGNBhMnZHQTP4JLhcAYk92pKk1V00q4Dnjklg+d9I2FtyZ8BwYqKoqtHNblYJozNKRQM9Fu03Yq3s8RJtdwQH85JyRqbpjWzQsG///15/cfvcbsNq9MGLZxnSrB3Y1u6OYd3kPuICWs3LyOy+gihTr6CZjEQ3x87ueknPuxdK25wQMoQHCtQboMJpTVqJzlt2rkSeUn+KA+57fuu96SzOb3fKdidxEbG65X9pUUKhoAPFosM01ukvmAJH/lc99ekecoVyV7YnFedBSSrPR1eVd+QFfluAnrsazXA+Ff+jjCCjq4hH1P5bMWOyGYJ0yovW7yo2ENHXII2xWXlegrDtUtRkWPPjKRCfAjaC+ACBteMy1gEWEBloui3W9NZuQskU63+PpVH8aRHlZgryRhN0UYg1CHXWnIopKZZ+Pty6ubUi/fUfV6LchXyLbZmvpCIdOHYfUfxClEYwpd/oWQub5YmBnyMt3esnMYkcUKY3m2+J+XcbdVlk2ZzoJfIDTT/bGuYu7/rEZspwSUcOEmNHlzhnap/RDWNRBv/SGnLwacqPmeevaIq4L+s9wURtsf/kcbgAXYEe9g+Q4FN+5+yxBqVZLzUAClk8HgzCJr7IW/GeXWNkuJ+2ZMSbPQym1Ms8Mg+h+8fQ1y/0WMj3iGS9GXYBFfQ+Av28zwOr5ZyFWBOVN8FOr+aAsQ/w0WMXbGpXmZ+R59wY61BWe/UdskZJgmCyE4vd5yrppePp8zHYwYs22KYa7zUC/cHo6bD6sByR+1kxon5e5UvgmhKXkA6dnfr6URYW86Ba1AQ1w8sI1CR+IwcWFvfIe7oRKKBMTmstnESFmV+LHx5th0pKqNveZUWUswzCAHOscbL3N1swgv7MTyMH53r64xBa4GO8NGT3KlemHslw9HRzXV53ZzpLypQrTXeEc1aAbu3aXpa3ORc7w7Gw++NOCpKGvDjqI3WjkN3mzHCWxsHb4jZGuDXLvAsews3RntMKszwGlp+qvL/pM35iiQrwluMWK/gqVVBfXLczTvrgqKJMjIfdlMFkKFbOkvxLNGAnYZLqIJoxmTGA5U+EQ6V6GiCsB/3ZRmyMpnAmFKVxO+LO7RhEH6r/oyRoaOuKs0GUIL9/SQzOlnCVx7bCnRIxulU3QA+PrLnpJ1KgQYRd8hxzzUJ9eSgyj3oXOM6SJyh6YAAAA=", "u": "", "e": 1}], "layers": [{"ind": 1, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [226, 198.477, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}, "hasMask": false, "ln": "847", "ip": 0, "op": 1800, "st": 0}, {"ind": 2, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [379.497, 577.346, 0]}, "a": {"a": 0, "k": [50, 50, 50]}, "s": {"a": 0, "k": [85.11, 77.856, 100]}}, "hasMask": false, "ln": "757", "ip": 0, "op": 1800, "st": 0, "parent": 10}, {"ind": 3, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 127, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 139, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 143, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 147, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 155, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 159, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 167, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 171, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 175, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 179, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 183, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-8]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 191, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 196, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 200, "s": [-12]}, {"t": 204, "s": [0]}]}, "p": {"a": 0, "k": [37.518, 70.605, 0]}, "a": {"a": 0, "k": [18.023, 8.721, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [1.084, 4.924]], "o": [[-21.27, 7.799], [0, 0]], "v": [[14.919, 0.868], [-14.919, -4.154]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 5}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 21.633}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "756", "ip": 0, "op": 1800, "st": 0, "parent": 2}, {"ind": 4, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 127, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 139, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 143, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 147, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 155, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 159, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 167, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 171, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 175, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 179, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 183, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-8]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 191, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 196, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 200, "s": [-12]}, {"t": 204, "s": [0]}]}, "p": {"a": 0, "k": [51.714, 67.175, 0]}, "a": {"a": 0, "k": [-21.221, 3.198, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [10.348, 0.265], [2.123, -4.157]], "o": [[-1.858, 3.715], [-8.278, -0.212], [0, 0]], "v": [[14.328, -8.358], [2.388, -3.847], [-14.328, 8.358]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 4.452}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 21.633}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "755", "ip": 0, "op": 1800, "st": 0, "parent": 2}, {"ind": 5, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 127, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 139, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 143, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 147, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 155, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 159, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 167, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 171, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 175, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 179, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 183, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-8]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 191, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 197, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 201, "s": [-12]}, {"t": 205, "s": [0]}]}, "p": {"a": 0, "k": [41.656, 68.241, 0]}, "a": {"a": 0, "k": [-6.686, 11.919, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0.341, -7.627], [-5.924, 0.344]], "o": [[-0.274, 6.102], [0, -11.131]], "v": [[-8.712, -1.231], [8.717, 5.365]], "c": true}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 4.452}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 87.609}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "754", "ip": 0, "op": 1800, "st": 0, "parent": 2}, {"ind": 6, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 127, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 139, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 143, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 147, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 155, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 159, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 167, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 171, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 175, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 179, "s": [-8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 183, "s": [8]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 187, "s": [-8]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 191, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 198, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 202, "s": [12]}, {"t": 206, "s": [0]}]}, "p": {"a": 0, "k": [40.042, 66.567, 0]}, "a": {"a": 0, "k": [11.337, 14.535, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0.399, -14.771], [-6.919, 0.666]], "o": [[-0.319, 11.816], [0, -21.557]], "v": [[-10.177, -2.384], [10.182, 10.39]], "c": true}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 4.45}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 6.99}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "753", "ip": 0, "op": 1800, "st": 0, "parent": 2}, {"ind": 7, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [378.15, 606.238, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [95, 17.018]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.949, 0.8251, 0.0196]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": -0.216}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}], "hasMask": false, "ln": "752", "ip": 0, "op": 1800, "st": 0, "parent": 10}, {"ind": 8, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 186, "s": [-3.538, 182, 0]}, {"t": 193, "s": [-3.538, 208, 0]}], "a": 1}, "a": {"a": 0, "k": [50, 50, 50]}, "s": {"k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.996, 1.004, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 189, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 190, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 191, "s": [105.185, 95.074, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 192, "s": [114.815, 85.926, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.997, 0.997, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.001, -0.001, 0]}, "t": 193, "s": [120, 81, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 194, "s": [117.004, 83.846, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 195, "s": [109.942, 90.555, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 196, "s": [101.797, 98.293, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 197, "s": [97.671, 102.213, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 198, "s": [94.27, 105.444, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 199, "s": [91.533, 108.044, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 200, "s": [89.397, 110.073, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 201, "s": [87.802, 111.588, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 202, "s": [86.684, 112.65, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.003, 0.003, 0]}, "t": 203, "s": [85.985, 113.314, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.005, 1.005, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.01, 0.01, 0]}, "t": 204, "s": [85.647, 113.635, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 205, "s": [85.617, 113.664, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 206, "s": [85.843, 113.449, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 207, "s": [86.28, 113.034, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 208, "s": [86.884, 112.46, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 209, "s": [87.619, 111.762, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 210, "s": [88.448, 110.975, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 211, "s": [89.342, 110.125, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 212, "s": [90.274, 109.239, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 213, "s": [91.223, 108.338, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 214, "s": [92.167, 107.441, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 215, "s": [93.093, 106.562, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 216, "s": [93.986, 105.714, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 217, "s": [94.836, 104.906, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 218, "s": [95.636, 104.146, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 219, "s": [96.379, 103.44, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 220, "s": [97.063, 102.79, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 221, "s": [97.685, 102.199, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 222, "s": [98.244, 101.668, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 223, "s": [98.742, 101.195, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 224, "s": [99.179, 100.78, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 225, "s": [99.558, 100.42, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 226, "s": [99.881, 100.113, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 227, "s": [100.154, 99.854, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 228, "s": [100.378, 99.641, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 229, "s": [100.559, 99.469, 100]}], "a": 1}}, "hasMask": false, "ln": "751", "ip": 0, "op": 1800, "st": 0, "parent": 1}, {"ind": 9, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [50, -11, 0]}, {"t": 191, "s": [50, -37.618, 0]}], "a": 1}, "a": {"a": 0, "k": [50, 50, 50]}, "s": {"a": 0, "k": [100, 100, 100]}}, "hasMask": false, "ln": "750", "ip": 0, "op": 1800, "st": 0, "parent": 8}, {"ind": 10, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [0]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 127, "s": [4]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 131, "s": [-4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [4]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.195, "y": 0}, "t": 139, "s": [-4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 143, "s": [4]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 147, "s": [-4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [4]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.229, "y": 0}, "t": 155, "s": [-4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 159, "s": [4]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 163, "s": [-4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 167, "s": [4]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.195, "y": 0}, "t": 171, "s": [-4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 175, "s": [4]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 179, "s": [-4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 183, "s": [4]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 187, "s": [-4]}, {"t": 191, "s": [0]}]}, "p": {"a": 0, "k": [50, 50, 0]}, "a": {"a": 0, "k": [376.495, 667.224, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [16.197, 59.556]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.484}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [378.496, 667.223]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [6.777, 18.643], [0, 0]], "o": [[-49.631, -3.072], [0, 0], [0, 0]], "v": [[40.185, 28.76], [-40.685, -29.197], [-40.935, 29.01]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.925, 0.925, 0.925]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [376.336, 667.974]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [84.906, 59.556]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.484}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [1.25, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [376.495, 667.223]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "749", "ip": 0, "op": 1800, "st": 0, "parent": 9}, {"ind": 11, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [96.5, 174.5, 0]}, "a": {"a": 0, "k": [94.5, 67.5, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[98.25, 69.5], [118.5, 55]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[90.75, 65.5], [139.5, 16]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 197, "s": [0]}, {"t": 209, "s": [100]}]}, "e": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 190, "s": [0]}, {"t": 202, "s": [100]}]}, "o": {"a": 0, "k": 0}, "m": 1}], "hasMask": false, "ln": "748", "ip": 190, "op": 210, "st": -12, "parent": 1}, {"ind": 12, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-100.5, 174.5, 0]}, "a": {"a": 0, "k": [94.5, 67.5, 0]}, "s": {"a": 0, "k": [-100, 100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[98.25, 69.5], [118.5, 55]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[90.75, 65.5], [139.5, 16]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 197, "s": [0]}, {"t": 209, "s": [100]}]}, "e": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 190, "s": [0]}, {"t": 202, "s": [100]}]}, "o": {"a": 0, "k": 0}, "m": 1}], "hasMask": false, "ln": "747", "ip": 190, "op": 210, "st": -12, "parent": 1}, {"ind": 13, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-0.284, 171.25, 0]}, "a": {"a": 0, "k": [0.75, -93.25, 0]}, "s": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [100, 100, 100]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [50, 70, 100]}, {"t": 201, "s": [100, 100, 100]}], "a": 1}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [131.5, 12.5]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0.75, -93.25]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "746", "ip": 0, "op": 1800, "st": 0, "parent": 1}, {"ind": 14, "ty": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [225, 410.723, 0]}, "a": {"a": 0, "k": [193, 54]}, "s": {"a": 0, "k": [100, 100]}}, "hasMask": false, "ln": "845", "ip": 0, "op": 1800, "st": 0, "refId": "2"}], "markers": [{"cm": "start", "tm": 120, "dr": 0}, {"cm": "end", "tm": 229, "dr": 0}]}
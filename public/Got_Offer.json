{"v": "5.7.0", "ip": 117, "op": 287, "fr": 60, "w": 450, "h": 500, "assets": [{"h": 108, "w": 386, "id": "1", "p": "data:image/webp;base64,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", "u": "", "e": 1}], "layers": [{"ind": 1, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [180, 270, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}, "hasMask": false, "ln": "842", "ip": 0, "op": 1800, "st": 0}, {"ind": 2, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 251, "s": [0]}, {"t": 281, "s": [87]}]}, "p": {"a": 0, "k": [50.871, -80.898, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 251, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 266, "s": [70, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 281, "s": [0, 0, 100]}], "a": 1}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-3.149, 0.791], [0.32, 2.699], [2.434, -0.639], [-0.419, -2.972]], "o": [[-6.966, -0.084], [-1.091, 7.385], [7.682, 1.811], [0.924, -5.86]], "v": [[8.51, 0.363], [0.246, -8.542], [-8.51, -0.089], [-0.287, 8.542]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8745, 0.3137]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": -61.208}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}], "hasMask": false, "ln": "817", "ip": 251, "op": 282, "st": 251, "parent": 1}, {"ind": 3, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 228, "s": [0]}, {"t": 258, "s": [87]}]}, "p": {"a": 0, "k": [-30.129, -59.898, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 228, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 243, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 258, "s": [0, 0, 100]}], "a": 1}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-3.149, 0.791], [0.32, 2.699], [2.434, -0.639], [-0.419, -2.972]], "o": [[-6.966, -0.084], [-1.091, 7.385], [5.791, -0.755], [0.924, -5.86]], "v": [[8.51, 0.363], [0.246, -8.542], [-8.51, -0.089], [-0.287, 8.542]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.9961, 0.7412, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": -61.208}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}], "hasMask": false, "ln": "816", "ip": 228, "op": 259, "st": 228, "parent": 1}, {"ind": 4, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 242, "s": [0]}, {"t": 272, "s": [87]}]}, "p": {"a": 0, "k": [116.871, -47.898, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 242, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 257, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 272, "s": [0, 0, 100]}], "a": 1}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-3.149, 0.791], [0.32, 2.699], [2.434, -0.639], [-0.42, -2.972]], "o": [[-6.966, -0.084], [-1.091, 7.385], [6.869, 1.636], [0.923, -5.861]], "v": [[8.51, 0.363], [0.246, -8.542], [-8.51, -0.089], [-1.14, 8.473]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8621, 0.7983]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": -61.208}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}], "hasMask": false, "ln": "815", "ip": 242, "op": 273, "st": 242, "parent": 1}, {"ind": 5, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-52.5, 103.5, 0]}, "a": {"a": 0, "k": [94.5, 67.5, 0]}, "s": {"a": 0, "k": [-100, 100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[98.25, 69.5], [118.5, 55]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[90.75, 65.5], [139.5, 16]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 170, "s": [0]}, {"t": 182, "s": [100]}]}, "e": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [0]}, {"t": 175, "s": [100]}]}, "o": {"a": 0, "k": 0}, "m": 1}], "hasMask": false, "ln": "814", "ip": 163, "op": 183, "st": -39, "parent": 1}, {"ind": 6, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [144.5, 103.5, 0]}, "a": {"a": 0, "k": [94.5, 67.5, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[98.25, 69.5], [118.5, 55]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[90.75, 65.5], [139.5, 16]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 170, "s": [0]}, {"t": 182, "s": [100]}]}, "e": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 163, "s": [0]}, {"t": 175, "s": [100]}]}, "o": {"a": 0, "k": 0}, "m": 1}], "hasMask": false, "ln": "813", "ip": 163, "op": 183, "st": -39, "parent": 1}, {"ind": 7, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0, "y": 0}, "t": 167, "s": [0]}, {"t": 172, "s": [-12]}]}, "p": {"k": [{"i": {"x": 0.2, "y": 1}, "o": {"x": 0.8, "y": 0}, "t": 164, "s": [376.37, 583.033, 0]}, {"t": 177, "s": [376.37, 447.176, 0]}], "a": 1}, "a": {"a": 0, "k": [50, 50, 50]}, "s": {"a": 0, "k": [85.109, 81.106, 100]}}, "hasMask": false, "ln": "812", "ip": 11, "op": 1811, "st": 11, "parent": 13}, {"ind": 8, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 167, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 171, "s": [-6]}, {"t": 175, "s": [0]}]}, "p": {"a": 0, "k": [40.519, 54.187, 0]}, "a": {"a": 0, "k": [18.023, 8.721, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [1.084, 4.924]], "o": [[-21.27, 7.799], [0, 0]], "v": [[14.919, 0.868], [-14.919, -4.154]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 4.452}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 21.633}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "811", "ip": 0, "op": 1800, "st": 0, "parent": 7}, {"ind": 9, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 165, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 169, "s": [-8]}, {"t": 173, "s": [0]}]}, "p": {"a": 0, "k": [51.481, 50.712, 0]}, "a": {"a": 0, "k": [-21.221, 3.198, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [10.348, 0.265], [2.123, -4.157]], "o": [[-1.858, 3.715], [-8.278, -0.212], [0, 0]], "v": [[14.328, -8.358], [2.388, -3.847], [-14.328, 8.358]], "c": false}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 4.452}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 21.633}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "810", "ip": 0, "op": 1800, "st": 0, "parent": 7}, {"ind": 10, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 169, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 173, "s": [-8]}, {"t": 177, "s": [0]}]}, "p": {"a": 0, "k": [42.831, 51.192, 0]}, "a": {"a": 0, "k": [-6.686, 11.919, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0.341, -7.627], [-5.924, 0.344]], "o": [[-0.274, 6.102], [0, -11.131]], "v": [[-8.712, -1.231], [8.717, 5.365]], "c": true}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 4.452}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 87.609}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "809", "ip": 0, "op": 1800, "st": 0, "parent": 7}, {"ind": 11, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 167, "s": [0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 171, "s": [8]}, {"t": 175, "s": [0]}]}, "p": {"a": 0, "k": [41.842, 50, 0]}, "a": {"a": 0, "k": [11.337, 14.535, 0]}, "s": {"a": 0, "k": [172, 172.182, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0.399, -14.771], [-6.919, 0.666]], "o": [[-0.319, 11.816], [0, -21.557]], "v": [[-10.177, -2.384], [10.182, 10.39]], "c": true}, "a": 0}}, {"ty": "st", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 4.452}, "c": {"a": 0, "k": [0.949, 0.7167, 0.0196]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 6.99}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [132.979, 132.979]}}]}], "hasMask": false, "ln": "808", "ip": 0, "op": 1800, "st": 0, "parent": 7}, {"ind": 12, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [49.883, 70.169, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [120.033, 119.402, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [90, 18.018]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.949, 0.8251, 0.0196]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": -0.216}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}], "hasMask": false, "ln": "807", "ip": 0, "op": 1800, "st": 0, "parent": 7}, {"ind": 13, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.196, "y": 1}, "o": {"x": 0.274, "y": 0}, "t": 124, "s": [44.462, 110.055, 0]}, {"i": {"x": 0.947, "y": 1}, "o": {"x": 0.746, "y": 0}, "t": 144, "s": [44.462, -24, 0]}, {"t": 164, "s": [44.462, 110.055, 0]}], "a": 1}, "a": {"a": 0, "k": [376.495, 726.779, 0]}, "s": {"k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 121, "s": [103.125, 95.312, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 122, "s": [110, 85, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.001, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.003, 0.003, 0]}, "t": 123, "s": [116.875, 74.688, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.997, 0.997, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 124, "s": [120, 70, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 125, "s": [112.601, 77.193, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 126, "s": [95.751, 93.874, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.003, 0.003, 0]}, "t": 127, "s": [78.832, 110.658, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 128, "s": [71.225, 118.162, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.986, 1.004, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.063, 0.027, 0]}, "t": 144, "s": [97.475, 102.525, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.997, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 145, "s": [97.434, 102.467, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 146, "s": [96.768, 102.826, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 147, "s": [95.561, 103.548, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 148, "s": [93.899, 104.577, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 149, "s": [91.868, 105.857, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 150, "s": [89.553, 107.33, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 151, "s": [87.044, 108.938, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 152, "s": [84.426, 110.624, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 153, "s": [81.788, 112.328, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 154, "s": [79.22, 113.993, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 155, "s": [76.808, 115.558, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 156, "s": [74.642, 116.965, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 157, "s": [72.811, 118.153, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 158, "s": [71.405, 119.064, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.015, 1.021, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.003, 0.002, 0]}, "t": 159, "s": [70.511, 119.636, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.997, 0.997, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 160, "s": [70.096, 119.936, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 161, "s": [77.86, 112.156, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 162, "s": [95.005, 94.996, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.003, 0.003, 0]}, "t": 163, "s": [112.158, 77.832, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.996, 0.997, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.004, -0.002, 0]}, "t": 164, "s": [119.883, 70.078, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 165, "s": [118.628, 72.466, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 166, "s": [114.477, 79.105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 167, "s": [108.821, 87.92, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 168, "s": [103.064, 96.936, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 169, "s": [100.164, 101.474, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 170, "s": [97.742, 105.234, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 171, "s": [95.761, 108.279, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 172, "s": [94.183, 110.674, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 173, "s": [92.968, 112.485, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 174, "s": [92.077, 113.778, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 175, "s": [91.473, 114.615, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 1.001, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.003, 0.004, 0]}, "t": 176, "s": [91.119, 115.057, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.995, 0.997, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.002, 0, 0]}, "t": 177, "s": [90.978, 115.162, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 178, "s": [91.019, 114.985, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 179, "s": [91.211, 114.574, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.002, 0]}, "t": 180, "s": [91.524, 113.976, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 181, "s": [91.934, 113.233, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 182, "s": [92.417, 112.381, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 183, "s": [92.953, 111.454, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 184, "s": [93.524, 110.479, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 185, "s": [94.113, 109.483, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 186, "s": [94.708, 108.486, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 187, "s": [95.297, 107.505, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 188, "s": [95.871, 106.556, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 189, "s": [96.421, 105.649, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 190, "s": [96.944, 104.793, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 191, "s": [97.433, 103.996, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 192, "s": [97.886, 103.26, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 193, "s": [98.301, 102.59, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 194, "s": [98.677, 101.985, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 195, "s": [99.014, 101.446, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 196, "s": [99.312, 100.97, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 197, "s": [99.573, 100.557, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 198, "s": [99.798, 100.202, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 199, "s": [99.989, 99.903, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 200, "s": [100.149, 99.655, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 201, "s": [100.28, 99.454, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 202, "s": [100.384, 99.296, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 203, "s": [100.464, 99.176, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 204, "s": [100.523, 99.091, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 0.999, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.002, 0.002, 0]}, "t": 205, "s": [100.563, 99.036, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.999, 1.001, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.003, 0.004, 0]}, "t": 206, "s": [100.586, 99.006, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.994, 0.997, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.003, 0, 0]}, "t": 207, "s": [100.595, 98.999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.998, 0.998, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.001, 0.001, 0]}, "t": 208, "s": [100.593, 99.011, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 209, "s": [100.58, 99.038, 100]}], "x": "var $bm_rt;\nvar eff, amp, freq, decay;\ntry {\n    eff = effect('Elastic Controller');\n    amp = $bm_div($bm_div(eff(1), 10), 100);\n    freq = $bm_div(eff(2), 100);\n    decay = $bm_div(eff(3), 100);\n    function elastic(amp, freq, decay) {\n        ;\n        function calc(n) {\n            ;\n            var t = $bm_sub(time, key(n).time);\n            var v = velocityAtTime(key(n).time - thisComp.frameDuration / 10);\n            return $bm_div($bm_mul($bm_mul(v, $bm_div(amp, freq)), Math.sin(freq * t * 2 * Math.PI)), Math.exp(t * (decay * 2) * Math.E));\n        }\n        ;\n        if ($bm_sum(numKeys, 0)) {\n            return 0;\n        }\n        ;\n        var n = nearestKey(time).index;\n        ;\n        if ($bm_sum(key(n).time, time))\n            n--;\n        return n > 1 && time <= key(n).time + 1 / decay ? calc(n) + calc(n - 1) : 0;\n    }\n    ;\n    $bm_rt = $bm_sum(value, elastic(amp, freq, decay));\n} catch (e) {\n    $bm_rt = value = value;\n}", "a": 1}}, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [16.197, 59.556]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.484}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [376.496, 667.223]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [6.777, 18.643], [0, 0]], "o": [[-49.631, -3.072], [0, 0], [0, 0]], "v": [[40.185, 28.76], [-40.685, -29.197], [-40.935, 29.01]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.925, 0.925, 0.925]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [376.961, 667.224]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [82.906, 59.556]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.484}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [376.495, 667.223]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "806", "ip": 0, "op": 1800, "st": 0, "parent": 1}, {"ind": 14, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [47.716, 100.25, 0]}, "a": {"a": 0, "k": [0.75, -93.25, 0]}, "s": {"k": [{"i": {"x": [0, 0, 0], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 124, "s": [100, 100, 100]}, {"i": {"x": [0, 0, 0], "y": [1, 1, 1]}, "o": {"x": [0.549, 0.549, 0.333], "y": [0, 0, 0]}, "t": 144, "s": [50, 70, 100]}, {"i": {"x": [0.517, 0.517, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.549, 0.549, 0.333], "y": [0, 0, 0]}, "t": 154, "s": [50, 70, 100]}, {"i": {"x": [0, 0, 0], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 162, "s": [100, 100, 100]}], "a": 1}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [131.5, 12.5]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0.75, -93.25]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "805", "ip": 0, "op": 1800, "st": 0, "parent": 1}, {"ind": 15, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.621}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [411.889, 672.753, 0], "ti": [0, 0.914, 0], "to": [0, -0.201, 0]}, {"i": {"x": 0.833, "y": 0.749}, "o": {"x": 0.167, "y": 0.107}, "t": 166, "s": [411.889, 671.547, 0], "ti": [0, 2.146, 0], "to": [0, -0.914, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.125}, "t": 167, "s": [411.889, 667.269, 0], "ti": [0, 3.762, 0], "to": [0, -2.146, 0]}, {"i": {"x": 0.833, "y": 0.807}, "o": {"x": 0.167, "y": 0.135}, "t": 168, "s": [411.889, 658.671, 0], "ti": [0, 5.379, 0], "to": [0, -3.762, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.147}, "t": 169, "s": [411.889, 644, 0], "ti": [0, 6.188, 0], "to": [0, -5.379, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.164}, "t": 170, "s": [411.889, 626.397, 0], "ti": [0, 5.869, 0], "to": [0, -6.188, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.179}, "t": 171, "s": [411.889, 607.573, 0], "ti": [0, 4.955, 0], "to": [0, -5.869, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.186}, "t": 172, "s": [411.889, 591.182, 0], "ti": [0, 4.006, 0], "to": [0, -4.955, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 173, "s": [411.889, 577.841, 0], "ti": [0, 3.215, 0], "to": [0, -4.006, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 174, "s": [411.889, 567.143, 0], "ti": [0, 2.582, 0], "to": [0, -3.215, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 175, "s": [411.889, 558.551, 0], "ti": [0, 2.075, 0], "to": [0, -2.582, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.187}, "t": 176, "s": [411.889, 551.65, 0], "ti": [0, 1.667, 0], "to": [0, -2.075, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.187}, "t": 177, "s": [411.889, 546.098, 0], "ti": [0, 1.33, 0], "to": [0, -1.667, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.188}, "t": 178, "s": [411.889, 541.65, 0], "ti": [0, 1.049, 0], "to": [0, -1.33, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.19}, "t": 179, "s": [411.889, 538.12, 0], "ti": [0, 0.811, 0], "to": [0, -1.049, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.193}, "t": 180, "s": [411.889, 535.354, 0], "ti": [0, 0.605, 0], "to": [0, -0.811, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.198}, "t": 181, "s": [411.889, 533.252, 0], "ti": [0, 0.425, 0], "to": [0, -0.605, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.208}, "t": 182, "s": [411.889, 531.725, 0], "ti": [0, 0.267, 0], "to": [0, -0.425, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.23}, "t": 183, "s": [411.889, 530.702, 0], "ti": [0, 0.127, 0], "to": [0, -0.267, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.346}, "t": 184, "s": [411.889, 530.122, 0], "ti": [0, 0.014, 0], "to": [0, -0.127, 0]}, {"i": {"x": 0.833, "y": 0.643}, "o": {"x": 0.167, "y": 0.233}, "t": 185, "s": [411.889, 529.938, 0], "ti": [0, -0.072, 0], "to": [0, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.774}, "o": {"x": 0.167, "y": 0.109}, "t": 186, "s": [411.889, 530.035, 0], "ti": [0, -0.152, 0], "to": [0, 0.072, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.132}, "t": 187, "s": [411.889, 530.372, 0], "ti": [0, -0.238, 0], "to": [0, 0.152, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.14}, "t": 188, "s": [411.889, 530.95, 0], "ti": [0, -0.333, 0], "to": [0, 0.238, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.145}, "t": 189, "s": [411.889, 531.798, 0], "ti": [0, -0.439, 0], "to": [0, 0.333, 0]}, {"i": {"x": 0.833, "y": 0.812}, "o": {"x": 0.167, "y": 0.148}, "t": 190, "s": [411.889, 532.947, 0], "ti": [0, -0.557, 0], "to": [0, 0.439, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 191, "s": [411.889, 534.431, 0], "ti": [0, -0.687, 0], "to": [0, 0.557, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 192, "s": [411.889, 536.287, 0], "ti": [0, -0.835, 0], "to": [0, 0.687, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 193, "s": [411.889, 538.553, 0], "ti": [0, -1.006, 0], "to": [0, 0.835, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.153}, "t": 194, "s": [411.889, 541.298, 0], "ti": [0, -1.203, 0], "to": [0, 1.006, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.153}, "t": 195, "s": [411.889, 544.591, 0], "ti": [0, -1.434, 0], "to": [0, 1.203, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 196, "s": [411.889, 548.518, 0], "ti": [0, -1.703, 0], "to": [0, 1.434, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.154}, "t": 197, "s": [411.889, 553.194, 0], "ti": [0, -2.022, 0], "to": [0, 1.703, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.153}, "t": 198, "s": [411.889, 558.737, 0], "ti": [0, -2.406, 0], "to": [0, 2.022, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 199, "s": [411.889, 565.327, 0], "ti": [0, -2.853, 0], "to": [0, 2.406, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 200, "s": [411.889, 573.171, 0], "ti": [0, -3.328, 0], "to": [0, 2.853, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.156}, "t": 201, "s": [411.889, 582.443, 0], "ti": [0, -3.692, 0], "to": [0, 3.328, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.161}, "t": 202, "s": [411.889, 593.136, 0], "ti": [0, -3.62, 0], "to": [0, 3.692, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.176}, "t": 203, "s": [411.889, 604.598, 0], "ti": [0, -2.833, 0], "to": [0, 3.62, 0]}, {"i": {"x": 0.833, "y": 0.881}, "o": {"x": 0.167, "y": 0.21}, "t": 204, "s": [411.889, 614.858, 0], "ti": [0, -1.602, 0], "to": [0, 2.833, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.279}, "t": 205, "s": [411.889, 621.598, 0], "ti": [0, -0.869, 0], "to": [0, 1.602, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 206, "s": [411.889, 624.469, 0], "ti": [0, -0.702, 0], "to": [0, 0.869, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.188}, "t": 207, "s": [411.889, 626.814, 0], "ti": [0, -0.549, 0], "to": [0, 0.702, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 208, "s": [411.889, 628.679, 0], "ti": [0, -0.413, 0], "to": [0, 0.549, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.198}, "t": 209, "s": [411.889, 630.111, 0], "ti": [0, -0.292, 0], "to": [0, 0.413, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.207}, "t": 210, "s": [411.889, 631.156, 0], "ti": [0, -0.186, 0], "to": [0, 0.292, 0]}, {"i": {"x": 0.833, "y": 0.885}, "o": {"x": 0.167, "y": 0.227}, "t": 211, "s": [411.889, 631.861, 0], "ti": [0, -0.094, 0], "to": [0, 0.186, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.304}, "t": 212, "s": [411.889, 632.27, 0], "ti": [0, -0.016, 0], "to": [0, 0.094, 0]}, {"i": {"x": 0.833, "y": 0.633}, "o": {"x": 0.167, "y": 0.266}, "t": 213, "s": [411.889, 632.424, 0], "ti": [0, 0.05, 0], "to": [0, 0.016, 0]}, {"i": {"x": 0.833, "y": 0.784}, "o": {"x": 0.167, "y": 0.107}, "t": 214, "s": [411.889, 632.363, 0], "ti": [0, 0.104, 0], "to": [0, -0.05, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.136}, "t": 215, "s": [411.889, 632.123, 0], "ti": [0, 0.147, 0], "to": [0, -0.104, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.148}, "t": 216, "s": [411.889, 631.74, 0], "ti": [0, 0.18, 0], "to": [0, -0.147, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.154}, "t": 217, "s": [411.889, 631.242, 0], "ti": [0, 0.205, 0], "to": [0, -0.18, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 218, "s": [411.889, 630.658, 0], "ti": [0, 0.222, 0], "to": [0, -0.205, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 219, "s": [411.889, 630.012, 0], "ti": [0, 0.232, 0], "to": [0, -0.222, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 220, "s": [411.889, 629.326, 0], "ti": [0, 0.237, 0], "to": [0, -0.232, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 221, "s": [411.889, 628.619, 0], "ti": [0, 0.236, 0], "to": [0, -0.237, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 222, "s": [411.889, 627.905, 0], "ti": [0, 0.232, 0], "to": [0, -0.236, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 223, "s": [411.889, 627, 0], "ti": [0, 0.224, 0], "to": [0, -0.232, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 224, "s": [411.889, 626.514, 0], "ti": [0, 0.214, 0], "to": [0, -0.224, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 225, "s": [411.889, 625.855, 0], "ti": [0, 0.201, 0], "to": [0, -0.214, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 226, "s": [411.889, 625.231, 0], "ti": [0, 0.187, 0], "to": [0, -0.201, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.174}, "t": 227, "s": [411.889, 624.647, 0], "ti": [0, 0.173, 0], "to": [0, -0.187, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 228, "s": [411.889, 624.107, 0], "ti": [0, 0.157, 0], "to": [0, -0.173, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 229, "s": [411.889, 623.612, 0], "ti": [0, 0.141, 0], "to": [0, -0.157, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 230, "s": [411.889, 623.164, 0], "ti": [0, 0.126, 0], "to": [0, -0.141, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.177}, "t": 231, "s": [411.889, 622.763, 0], "ti": [0, 0.111, 0], "to": [0, -0.126, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 232, "s": [411.889, 622.408, 0], "ti": [0, 0.096, 0], "to": [0, -0.111, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.18}, "t": 233, "s": [411.889, 622.098, 0], "ti": [0, 0.082, 0], "to": [0, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 234, "s": [411.889, 621.831, 0], "ti": [0, 0.069, 0], "to": [0, -0.082, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.183}, "t": 235, "s": [411.889, 621.604, 0], "ti": [0, 0.057, 0], "to": [0, -0.069, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.185}, "t": 236, "s": [411.889, 621.414, 0], "ti": [0, 0.046, 0], "to": [0, -0.057, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.188}, "t": 237, "s": [411.889, 621.259, 0], "ti": [0, 0.021, 0], "to": [0, -0.046, 0]}, {"t": 238, "s": [411.889, 621.136, 0], "ti": [0, 0.201, 0], "to": [0, -0.021, 0]}], "x": "var $bm_rt;\nvar eff, amp, freq, decay;\ntry {\n    eff = effect('Elastic Controller');\n    amp = $bm_div($bm_div(eff(1), 10), 100);\n    freq = $bm_div(eff(2), 100);\n    decay = $bm_div(eff(3), 100);\n    function elastic(amp, freq, decay) {\n        ;\n        function calc(n) {\n            ;\n            var t = $bm_sub(time, key(n).time);\n            var v = velocityAtTime(key(n).time - thisComp.frameDuration / 10);\n            return $bm_div($bm_mul($bm_mul(v, $bm_div(amp, freq)), Math.sin(freq * t * 2 * Math.PI)), Math.exp(t * (decay * 2) * Math.E));\n        }\n        ;\n        if ($bm_sum(numKeys, 0)) {\n            return 0;\n        }\n        ;\n        var n = nearestKey(time).index;\n        ;\n        if ($bm_sum(key(n).time, time))\n            n--;\n        return n > 1 && time <= key(n).time + 1 / decay ? calc(n) + calc(n - 1) : 0;\n    }\n    ;\n    $bm_rt = $bm_sum(value, elastic(amp, freq, decay));\n} catch (e) {\n    $bm_rt = value = value;\n}", "a": 1}, "a": {"a": 0, "k": [207.754, 352.129, 0]}, "s": {"a": 0, "k": [152.593, 146.214, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.108, -0.038], [-0.114, 0.008], [0, 0], [-0.359, -1.136], [0, 0], [0.108, -0.038], [0.083, -0.078], [0, -0.177], [-0.124, -0.139], [-0.108, -0.039], [-0.114, 0.009], [0, 0], [0.254, -0.307], [0.334, -0.178], [0.4, -0.07], [0.426, 0.001], [0, 0], [0.11, -0.044], [0.084, -0.083], [0.045, -0.11], [-0.002, -0.118], [-0.066, -0.141], [-0.135, -0.113], [0, 0], [-0.165, -0.074], [-0.159, 0.001], [-0.119, 0.045], [-0.091, 0.089], [0.003, 0.242], [0.058, 0.126], [0.103, 0.092], [0, 0], [-0.462, 0.145], [-0.398, 0.288], [-0.268, 0.422], [-0.054, 0.565], [0, 0], [-0.104, 0.036], [-0.081, 0.073], [-0.002, 0.191], [0.133, 0.136], [0.104, 0.038], [0.111, -0.011], [0, 0], [0.287, 0.306], [0.323, 0.145], [0, 0], [-0.202, -0.004], [-0.473, 0], [0, 0], [-0.104, 0.035], [-0.08, 0.075], [0, 0.19], [0.134, 0.135], [0.105, 0.039], [0.111, -0.011], [0, 0], [0.108, -0.038], [0.082, -0.079], [-0.001, -0.177], [-0.123, -0.14]], "o": [[0.107, 0.039], [0, 0], [1.77, 0], [0, 0], [-0.114, -0.009], [-0.107, 0.038], [-0.121, 0.128], [-0.002, 0.186], [0.082, 0.08], [0.108, 0.04], [0, 0], [-0.055, 0.395], [-0.243, 0.291], [-0.362, 0.185], [-0.421, 0.07], [0, 0], [-0.118, -0.003], [-0.11, 0.043], [-0.084, 0.083], [-0.044, 0.109], [0.003, 0.155], [0.087, 0.153], [0, 0], [0.126, 0.13], [0.155, 0.038], [0.127, 0.007], [0.119, -0.045], [0.164, -0.178], [-0.003, -0.138], [-0.057, -0.125], [0, 0], [0.481, -0.044], [0.47, -0.144], [0.41, -0.287], [0.289, -0.488], [0, 0], [0.109, 0.01], [0.104, -0.035], [0.133, -0.138], [-0.002, -0.189], [-0.078, -0.08], [-0.105, -0.039], [0, 0], [-0.124, -0.401], [-0.243, -0.257], [0, 0], [0.201, 0.025], [0.331, 0], [0, 0], [0.109, 0.01], [0.105, -0.035], [0.134, -0.134], [0, -0.19], [-0.077, -0.08], [-0.105, -0.038], [0, 0], [-0.114, -0.01], [-0.108, 0.039], [-0.123, 0.127], [-0.002, 0.186], [0.083, 0.079]], "v": [[-4.183, -5.75], [-3.847, -5.704], [-2.939, -5.657], [0.255, -3.953], [-3.876, -3.953], [-4.212, -3.908], [-4.501, -3.731], [-4.69, -3.257], [-4.501, -2.751], [-4.213, -2.57], [-3.876, -2.524], [0.383, -2.524], [-0.09, -1.45], [-0.965, -0.74], [-2.115, -0.356], [-3.388, -0.252], [-3.658, -0.252], [-4.004, -0.191], [-4.298, 0.001], [-4.493, 0.293], [-4.557, 0.638], [-4.453, 1.087], [-4.117, 1.49], [1.434, 6.767], [1.874, 7.075], [2.347, 7.131], [2.72, 7.074], [3.038, 6.871], [3.289, 6.218], [3.197, 5.818], [2.953, 5.489], [-1.779, 1.087], [-0.36, 0.803], [0.951, 0.15], [1.978, -0.924], [2.499, -2.524], [3.876, -2.524], [4.2, -2.563], [4.481, -2.727], [4.69, -3.239], [4.481, -3.745], [4.204, -3.924], [3.876, -3.967], [2.395, -3.967], [1.77, -5.042], [0.913, -5.652], [0.913, -5.733], [1.519, -5.69], [2.749, -5.69], [3.876, -5.69], [4.2, -5.727], [4.481, -5.894], [4.69, -6.4], [4.481, -6.907], [4.204, -7.088], [3.876, -7.129], [-3.847, -7.129], [-4.184, -7.086], [-4.472, -6.907], [-4.661, -6.433], [-4.472, -5.927]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.754, 352.129]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.108, -0.038], [-0.114, 0.008], [0, 0], [-0.359, -1.136], [0, 0], [0.108, -0.038], [0.083, -0.078], [0, -0.177], [-0.124, -0.139], [-0.108, -0.039], [-0.114, 0.009], [0, 0], [0.254, -0.307], [0.334, -0.178], [0.4, -0.07], [0.426, 0.001], [0, 0], [0.11, -0.044], [0.084, -0.083], [0.045, -0.11], [-0.002, -0.118], [-0.066, -0.141], [-0.135, -0.113], [0, 0], [-0.165, -0.074], [-0.159, 0.001], [-0.119, 0.045], [-0.091, 0.089], [0.003, 0.242], [0.058, 0.126], [0.103, 0.092], [0, 0], [-0.462, 0.145], [-0.398, 0.288], [-0.268, 0.422], [-0.054, 0.565], [0, 0], [-0.104, 0.036], [-0.081, 0.073], [-0.002, 0.191], [0.133, 0.136], [0.104, 0.038], [0.111, -0.011], [0, 0], [0.287, 0.306], [0.323, 0.145], [0, 0], [-0.202, -0.004], [-0.473, 0], [0, 0], [-0.104, 0.035], [-0.08, 0.075], [0, 0.19], [0.134, 0.135], [0.105, 0.039], [0.111, -0.011], [0, 0], [0.108, -0.038], [0.082, -0.079], [-0.001, -0.177], [-0.123, -0.14]], "o": [[0.107, 0.039], [0, 0], [1.77, 0], [0, 0], [-0.114, -0.009], [-0.107, 0.038], [-0.121, 0.128], [-0.002, 0.186], [0.082, 0.08], [0.108, 0.04], [0, 0], [-0.055, 0.395], [-0.243, 0.291], [-0.362, 0.185], [-0.421, 0.07], [0, 0], [-0.118, -0.003], [-0.11, 0.043], [-0.084, 0.083], [-0.044, 0.109], [0.003, 0.155], [0.087, 0.153], [0, 0], [0.126, 0.13], [0.155, 0.038], [0.127, 0.007], [0.119, -0.045], [0.164, -0.178], [-0.003, -0.138], [-0.057, -0.125], [0, 0], [0.481, -0.044], [0.47, -0.144], [0.41, -0.287], [0.289, -0.488], [0, 0], [0.109, 0.01], [0.104, -0.035], [0.133, -0.138], [-0.002, -0.189], [-0.078, -0.08], [-0.105, -0.039], [0, 0], [-0.124, -0.401], [-0.243, -0.257], [0, 0], [0.201, 0.025], [0.331, 0], [0, 0], [0.109, 0.01], [0.105, -0.035], [0.134, -0.134], [0, -0.19], [-0.077, -0.08], [-0.105, -0.038], [0, 0], [-0.114, -0.01], [-0.108, 0.039], [-0.123, 0.127], [-0.002, 0.186], [0.083, 0.079]], "v": [[-4.183, -5.75], [-3.847, -5.704], [-2.939, -5.657], [0.255, -3.953], [-3.876, -3.953], [-4.212, -3.908], [-4.501, -3.731], [-4.69, -3.257], [-4.501, -2.751], [-4.213, -2.57], [-3.876, -2.524], [0.383, -2.524], [-0.09, -1.45], [-0.965, -0.74], [-2.115, -0.356], [-3.388, -0.252], [-3.658, -0.252], [-4.004, -0.191], [-4.298, 0.001], [-4.493, 0.293], [-4.557, 0.638], [-4.453, 1.087], [-4.117, 1.49], [1.434, 6.767], [1.874, 7.075], [2.347, 7.131], [2.72, 7.074], [3.038, 6.871], [3.289, 6.218], [3.197, 5.818], [2.953, 5.489], [-1.779, 1.087], [-0.36, 0.803], [0.951, 0.15], [1.978, -0.924], [2.499, -2.524], [3.876, -2.524], [4.2, -2.563], [4.481, -2.727], [4.69, -3.239], [4.481, -3.745], [4.204, -3.924], [3.876, -3.967], [2.395, -3.967], [1.77, -5.042], [0.913, -5.652], [0.913, -5.733], [1.519, -5.69], [2.749, -5.69], [3.876, -5.69], [4.2, -5.727], [4.481, -5.894], [4.69, -6.4], [4.481, -6.907], [4.204, -7.088], [3.876, -7.129], [-3.847, -7.129], [-4.184, -7.086], [-4.472, -6.907], [-4.661, -6.433], [-4.472, -5.927]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.9686, 0.5569, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.032, 352.798]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [28.027, 28.027]}}, {"ty": "fl", "c": {"a": 0, "k": [0.9961, 0.7412, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.032, 352.799]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [30.044, 30.044]}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.022}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.9686, 0.5569, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.753, 352.129]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [4.75, 57.5]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.949, 0.7216]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-27.375, 95.5]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [150, 100]}}]}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [4.75, 57.5]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.949, 0.7216]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-35.625, 95.5]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 29}, "p": {"a": 0, "k": [208.094, 352.25]}, "a": {"a": 0, "k": [-30.906, 95.5]}, "s": {"a": 0, "k": [100, 81.014]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [40.33, 40.33]}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.022}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8745, 0.3137]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.754, 352.129]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "804", "ip": 165, "op": 1789, "st": -11, "parent": 13}, {"ind": 16, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.623}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [349.834, 649.359, 0], "ti": [0, 0.695, 0], "to": [0, -0.154, 0]}, {"i": {"x": 0.833, "y": 0.748}, "o": {"x": 0.167, "y": 0.107}, "t": 171, "s": [349.834, 648.436, 0], "ti": [0, 1.638, 0], "to": [0, -0.695, 0]}, {"i": {"x": 0.833, "y": 0.774}, "o": {"x": 0.167, "y": 0.124}, "t": 172, "s": [349.834, 645.188, 0], "ti": [0, 2.979, 0], "to": [0, -1.638, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.132}, "t": 173, "s": [349.834, 638.607, 0], "ti": [0, 4.72, 0], "to": [0, -2.979, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.139}, "t": 174, "s": [349.834, 627.316, 0], "ti": [0, 6.307, 0], "to": [0, -4.72, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.152}, "t": 175, "s": [349.834, 610.286, 0], "ti": [0, 6.786, 0], "to": [0, -6.307, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.17}, "t": 176, "s": [349.834, 589.473, 0], "ti": [0, 6.048, 0], "to": [0, -6.786, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.185}, "t": 177, "s": [349.834, 569.568, 0], "ti": [0, 4.873, 0], "to": [0, -6.048, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.19}, "t": 178, "s": [349.834, 553.186, 0], "ti": [0, 3.824, 0], "to": [0, -4.873, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.19}, "t": 179, "s": [349.834, 540.331, 0], "ti": [0, 3.004, 0], "to": [0, -3.824, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.189}, "t": 180, "s": [349.834, 530.244, 0], "ti": [0, 2.368, 0], "to": [0, -3.004, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.189}, "t": 181, "s": [349.834, 522.308, 0], "ti": [0, 1.869, 0], "to": [0, -2.368, 0]}, {"i": {"x": 0.833, "y": 0.851}, "o": {"x": 0.167, "y": 0.189}, "t": 182, "s": [349.834, 516.038, 0], "ti": [0, 1.472, 0], "to": [0, -1.869, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.189}, "t": 183, "s": [349.834, 511.092, 0], "ti": [0, 1.15, 0], "to": [0, -1.472, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.191}, "t": 184, "s": [349.834, 507.207, 0], "ti": [0, 0.881, 0], "to": [0, -1.15, 0]}, {"i": {"x": 0.833, "y": 0.857}, "o": {"x": 0.167, "y": 0.194}, "t": 185, "s": [349.834, 504.191, 0], "ti": [0, 0.651, 0], "to": [0, -0.881, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.199}, "t": 186, "s": [349.834, 501.922, 0], "ti": [0, 0.455, 0], "to": [0, -0.651, 0]}, {"i": {"x": 0.833, "y": 0.87}, "o": {"x": 0.167, "y": 0.208}, "t": 187, "s": [349.834, 500.286, 0], "ti": [0, 0.284, 0], "to": [0, -0.455, 0]}, {"i": {"x": 0.833, "y": 0.89}, "o": {"x": 0.167, "y": 0.231}, "t": 188, "s": [349.834, 499.194, 0], "ti": [0, 0.135, 0], "to": [0, -0.284, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.348}, "t": 189, "s": [349.834, 498.579, 0], "ti": [0, 0.014, 0], "to": [0, -0.135, 0]}, {"i": {"x": 0.833, "y": 0.663}, "o": {"x": 0.167, "y": 0.221}, "t": 190, "s": [349.834, 498.385, 0], "ti": [0, -0.078, 0], "to": [0, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.771}, "o": {"x": 0.167, "y": 0.111}, "t": 191, "s": [349.834, 498.497, 0], "ti": [0, -0.163, 0], "to": [0, 0.078, 0]}, {"i": {"x": 0.833, "y": 0.794}, "o": {"x": 0.167, "y": 0.131}, "t": 192, "s": [349.834, 498.853, 0], "ti": [0, -0.256, 0], "to": [0, 0.163, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.14}, "t": 193, "s": [349.834, 499.476, 0], "ti": [0, -0.359, 0], "to": [0, 0.256, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.145}, "t": 194, "s": [349.834, 500.391, 0], "ti": [0, -0.473, 0], "to": [0, 0.359, 0]}, {"i": {"x": 0.833, "y": 0.813}, "o": {"x": 0.167, "y": 0.148}, "t": 195, "s": [349.834, 501.628, 0], "ti": [0, 0, 0], "to": [0, 0.473, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.15}, "t": 196, "s": [349.834, 503.229, 0], "ti": [0, -0.74, 0], "to": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.152}, "t": 197, "s": [349.834, 505.228, 0], "ti": [0, 0, 0], "to": [0, 0.74, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.152}, "t": 198, "s": [349.834, 507.671, 0], "ti": [0, -1.085, 0], "to": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.153}, "t": 199, "s": [349.834, 510.63, 0], "ti": [0, -1.297, 0], "to": [0, 1.085, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.153}, "t": 200, "s": [349.834, 514.179, 0], "ti": [0, -1.545, 0], "to": [0, 1.297, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 201, "s": [349.834, 518.411, 0], "ti": [0, -1.836, 0], "to": [0, 1.545, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.154}, "t": 202, "s": [349.834, 523.45, 0], "ti": [0, -2.18, 0], "to": [0, 1.836, 0]}, {"i": {"x": 0.833, "y": 0.817}, "o": {"x": 0.167, "y": 0.153}, "t": 203, "s": [349.834, 529.425, 0], "ti": [0, -2.593, 0], "to": [0, 2.18, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.153}, "t": 204, "s": [349.834, 536.528, 0], "ti": [0, -3.075, 0], "to": [0, 2.593, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.154}, "t": 205, "s": [349.834, 544.982, 0], "ti": [0, -3.587, 0], "to": [0, 3.075, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.156}, "t": 206, "s": [349.834, 554.976, 0], "ti": [0, -3.98, 0], "to": [0, 3.587, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.161}, "t": 207, "s": [349.834, 566.501, 0], "ti": [0, -3.902, 0], "to": [0, 3.98, 0]}, {"i": {"x": 0.833, "y": 0.862}, "o": {"x": 0.167, "y": 0.176}, "t": 208, "s": [349.834, 578.854, 0], "ti": [0, -3.054, 0], "to": [0, 3.902, 0]}, {"i": {"x": 0.833, "y": 0.881}, "o": {"x": 0.167, "y": 0.21}, "t": 209, "s": [349.834, 589.912, 0], "ti": [0, -1.727, 0], "to": [0, 3.054, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.279}, "t": 210, "s": [349.834, 597.178, 0], "ti": [0, -0.937, 0], "to": [0, 1.727, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 211, "s": [349.834, 600.272, 0], "ti": [0, -0.756, 0], "to": [0, 0.937, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.188}, "t": 212, "s": [349.834, 602.799, 0], "ti": [0, -0.592, 0], "to": [0, 0.756, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 213, "s": [349.834, 604.809, 0], "ti": [0, -0.445, 0], "to": [0, 0.592, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.197}, "t": 214, "s": [349.834, 606.352, 0], "ti": [0, -0.314, 0], "to": [0, 0.445, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.207}, "t": 215, "s": [349.834, 607.479, 0], "ti": [0, 0, 0], "to": [0, 0.314, 0]}, {"i": {"x": 0.833, "y": 0.885}, "o": {"x": 0.167, "y": 0.227}, "t": 216, "s": [349.834, 608.239, 0], "ti": [0, -0.101, 0], "to": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.304}, "t": 217, "s": [349.834, 608.679, 0], "ti": [0, -0.017, 0], "to": [0, 0.101, 0]}, {"i": {"x": 0.833, "y": 0.633}, "o": {"x": 0.167, "y": 0.266}, "t": 218, "s": [349.834, 608.845, 0], "ti": [0, 0.054, 0], "to": [0, 0.017, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.108}, "t": 219, "s": [349.834, 608.779, 0], "ti": [0, 0.112, 0], "to": [0, -0.054, 0]}, {"i": {"x": 0.833, "y": 0.808}, "o": {"x": 0.167, "y": 0.136}, "t": 220, "s": [349.834, 608.521, 0], "ti": [0, 0.158, 0], "to": [0, -0.112, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.147}, "t": 221, "s": [349.834, 608.108, 0], "ti": [0, 0.194, 0], "to": [0, -0.158, 0]}, {"i": {"x": 0.833, "y": 0.824}, "o": {"x": 0.167, "y": 0.154}, "t": 222, "s": [349.834, 607.571, 0], "ti": [0, 0.221, 0], "to": [0, -0.194, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 223, "s": [349.834, 606.942, 0], "ti": [0, 0.239, 0], "to": [0, -0.221, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 224, "s": [349.834, 606.246, 0], "ti": [0, 0.25, 0], "to": [0, -0.239, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 225, "s": [349.834, 605.506, 0], "ti": [0, 0.255, 0], "to": [0, -0.25, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 226, "s": [349.834, 604.744, 0], "ti": [0, 0.255, 0], "to": [0, -0.255, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 227, "s": [349.834, 603.975, 0], "ti": [0, 0.25, 0], "to": [0, -0.255, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 228, "s": [349.834, 603.215, 0], "ti": [0, 0.242, 0], "to": [0, -0.25, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 229, "s": [349.834, 602.475, 0], "ti": [0, 0.23, 0], "to": [0, -0.242, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 230, "s": [349.834, 601.766, 0], "ti": [0, 0.217, 0], "to": [0, -0.23, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 231, "s": [349.834, 601.093, 0], "ti": [0, 0.202, 0], "to": [0, -0.217, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.173}, "t": 232, "s": [349.834, 600.464, 0], "ti": [0, 0.097, 0], "to": [0, -0.202, 0]}, {"t": 233, "s": [349.834, 599.881, 0], "ti": [0, 0.154, 0], "to": [0, -0.097, 0]}], "x": "var $bm_rt;\nvar eff, amp, freq, decay;\ntry {\n    eff = effect('Elastic Controller');\n    amp = $bm_div($bm_div(eff(1), 10), 100);\n    freq = $bm_div(eff(2), 100);\n    decay = $bm_div(eff(3), 100);\n    function elastic(amp, freq, decay) {\n        ;\n        function calc(n) {\n            ;\n            var t = $bm_sub(time, key(n).time);\n            var v = velocityAtTime(key(n).time - thisComp.frameDuration / 10);\n            return $bm_div($bm_mul($bm_mul(v, $bm_div(amp, freq)), Math.sin(freq * t * 2 * Math.PI)), Math.exp(t * (decay * 2) * Math.E));\n        }\n        ;\n        if ($bm_sum(numKeys, 0)) {\n            return 0;\n        }\n        ;\n        var n = nearestKey(time).index;\n        ;\n        if ($bm_sum(key(n).time, time))\n            n--;\n        return n > 1 && time <= key(n).time + 1 / decay ? calc(n) + calc(n - 1) : 0;\n    }\n    ;\n    $bm_rt = $bm_sum(value, elastic(amp, freq, decay));\n} catch (e) {\n    $bm_rt = value = value;\n}", "a": 1}, "a": {"a": 0, "k": [207.754, 352.129, 0]}, "s": {"a": 0, "k": [152.593, 146.214, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.108, -0.038], [-0.114, 0.008], [0, 0], [-0.359, -1.136], [0, 0], [0.108, -0.038], [0.083, -0.078], [0, -0.177], [-0.124, -0.139], [-0.108, -0.039], [-0.114, 0.009], [0, 0], [0.254, -0.307], [0.334, -0.178], [0.4, -0.07], [0.426, 0.001], [0, 0], [0.11, -0.044], [0.084, -0.083], [0.045, -0.11], [-0.002, -0.118], [-0.066, -0.141], [-0.135, -0.113], [0, 0], [-0.165, -0.074], [-0.159, 0.001], [-0.119, 0.045], [-0.091, 0.089], [0.003, 0.242], [0.058, 0.126], [0.103, 0.092], [0, 0], [-0.462, 0.145], [-0.398, 0.288], [-0.268, 0.422], [-0.054, 0.565], [0, 0], [-0.104, 0.036], [-0.081, 0.073], [-0.002, 0.191], [0.133, 0.136], [0.104, 0.038], [0.111, -0.011], [0, 0], [0.287, 0.306], [0.323, 0.145], [0, 0], [-0.202, -0.004], [-0.473, 0], [0, 0], [-0.104, 0.035], [-0.08, 0.075], [0, 0.19], [0.134, 0.135], [0.105, 0.039], [0.111, -0.011], [0, 0], [0.108, -0.038], [0.082, -0.079], [-0.001, -0.177], [-0.123, -0.14]], "o": [[0.107, 0.039], [0, 0], [1.77, 0], [0, 0], [-0.114, -0.009], [-0.107, 0.038], [-0.121, 0.128], [-0.002, 0.186], [0.082, 0.08], [0.108, 0.04], [0, 0], [-0.055, 0.395], [-0.243, 0.291], [-0.362, 0.185], [-0.421, 0.07], [0, 0], [-0.118, -0.003], [-0.11, 0.043], [-0.084, 0.083], [-0.044, 0.109], [0.003, 0.155], [0.087, 0.153], [0, 0], [0.126, 0.13], [0.155, 0.038], [0.127, 0.007], [0.119, -0.045], [0.164, -0.178], [-0.003, -0.138], [-0.057, -0.125], [0, 0], [0.481, -0.044], [0.47, -0.144], [0.41, -0.287], [0.289, -0.488], [0, 0], [0.109, 0.01], [0.104, -0.035], [0.133, -0.138], [-0.002, -0.189], [-0.078, -0.08], [-0.105, -0.039], [0, 0], [-0.124, -0.401], [-0.243, -0.257], [0, 0], [0.201, 0.025], [0.331, 0], [0, 0], [0.109, 0.01], [0.105, -0.035], [0.134, -0.134], [0, -0.19], [-0.077, -0.08], [-0.105, -0.038], [0, 0], [-0.114, -0.01], [-0.108, 0.039], [-0.123, 0.127], [-0.002, 0.186], [0.083, 0.079]], "v": [[-4.183, -5.75], [-3.847, -5.704], [-2.939, -5.657], [0.255, -3.953], [-3.876, -3.953], [-4.212, -3.908], [-4.501, -3.731], [-4.69, -3.257], [-4.501, -2.751], [-4.213, -2.57], [-3.876, -2.524], [0.383, -2.524], [-0.09, -1.45], [-0.965, -0.74], [-2.115, -0.356], [-3.388, -0.252], [-3.658, -0.252], [-4.004, -0.191], [-4.298, 0.001], [-4.493, 0.293], [-4.557, 0.638], [-4.453, 1.087], [-4.117, 1.49], [1.434, 6.767], [1.874, 7.075], [2.347, 7.131], [2.72, 7.074], [3.038, 6.871], [3.289, 6.218], [3.197, 5.818], [2.953, 5.489], [-1.779, 1.087], [-0.36, 0.803], [0.951, 0.15], [1.978, -0.924], [2.499, -2.524], [3.876, -2.524], [4.2, -2.563], [4.481, -2.727], [4.69, -3.239], [4.481, -3.745], [4.204, -3.924], [3.876, -3.967], [2.395, -3.967], [1.77, -5.042], [0.913, -5.652], [0.913, -5.733], [1.519, -5.69], [2.749, -5.69], [3.876, -5.69], [4.2, -5.727], [4.481, -5.894], [4.69, -6.4], [4.481, -6.907], [4.204, -7.088], [3.876, -7.129], [-3.847, -7.129], [-4.184, -7.086], [-4.472, -6.907], [-4.661, -6.433], [-4.472, -5.927]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.754, 352.129]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.108, -0.038], [-0.114, 0.008], [0, 0], [-0.359, -1.136], [0, 0], [0.108, -0.038], [0.083, -0.078], [0, -0.177], [-0.124, -0.139], [-0.108, -0.039], [-0.114, 0.009], [0, 0], [0.254, -0.307], [0.334, -0.178], [0.4, -0.07], [0.426, 0.001], [0, 0], [0.11, -0.044], [0.084, -0.083], [0.045, -0.11], [-0.002, -0.118], [-0.066, -0.141], [-0.135, -0.113], [0, 0], [-0.165, -0.074], [-0.159, 0.001], [-0.119, 0.045], [-0.091, 0.089], [0.003, 0.242], [0.058, 0.126], [0.103, 0.092], [0, 0], [-0.462, 0.145], [-0.398, 0.288], [-0.268, 0.422], [-0.054, 0.565], [0, 0], [-0.104, 0.036], [-0.081, 0.073], [-0.002, 0.191], [0.133, 0.136], [0.104, 0.038], [0.111, -0.011], [0, 0], [0.287, 0.306], [0.323, 0.145], [0, 0], [-0.202, -0.004], [-0.473, 0], [0, 0], [-0.104, 0.035], [-0.08, 0.075], [0, 0.19], [0.134, 0.135], [0.105, 0.039], [0.111, -0.011], [0, 0], [0.108, -0.038], [0.082, -0.079], [-0.001, -0.177], [-0.123, -0.14]], "o": [[0.107, 0.039], [0, 0], [1.77, 0], [0, 0], [-0.114, -0.009], [-0.107, 0.038], [-0.121, 0.128], [-0.002, 0.186], [0.082, 0.08], [0.108, 0.04], [0, 0], [-0.055, 0.395], [-0.243, 0.291], [-0.362, 0.185], [-0.421, 0.07], [0, 0], [-0.118, -0.003], [-0.11, 0.043], [-0.084, 0.083], [-0.044, 0.109], [0.003, 0.155], [0.087, 0.153], [0, 0], [0.126, 0.13], [0.155, 0.038], [0.127, 0.007], [0.119, -0.045], [0.164, -0.178], [-0.003, -0.138], [-0.057, -0.125], [0, 0], [0.481, -0.044], [0.47, -0.144], [0.41, -0.287], [0.289, -0.488], [0, 0], [0.109, 0.01], [0.104, -0.035], [0.133, -0.138], [-0.002, -0.189], [-0.078, -0.08], [-0.105, -0.039], [0, 0], [-0.124, -0.401], [-0.243, -0.257], [0, 0], [0.201, 0.025], [0.331, 0], [0, 0], [0.109, 0.01], [0.105, -0.035], [0.134, -0.134], [0, -0.19], [-0.077, -0.08], [-0.105, -0.038], [0, 0], [-0.114, -0.01], [-0.108, 0.039], [-0.123, 0.127], [-0.002, 0.186], [0.083, 0.079]], "v": [[-4.183, -5.75], [-3.847, -5.704], [-2.939, -5.657], [0.255, -3.953], [-3.876, -3.953], [-4.212, -3.908], [-4.501, -3.731], [-4.69, -3.257], [-4.501, -2.751], [-4.213, -2.57], [-3.876, -2.524], [0.383, -2.524], [-0.09, -1.45], [-0.965, -0.74], [-2.115, -0.356], [-3.388, -0.252], [-3.658, -0.252], [-4.004, -0.191], [-4.298, 0.001], [-4.493, 0.293], [-4.557, 0.638], [-4.453, 1.087], [-4.117, 1.49], [1.434, 6.767], [1.874, 7.075], [2.347, 7.131], [2.72, 7.074], [3.038, 6.871], [3.289, 6.218], [3.197, 5.818], [2.953, 5.489], [-1.779, 1.087], [-0.36, 0.803], [0.951, 0.15], [1.978, -0.924], [2.499, -2.524], [3.876, -2.524], [4.2, -2.563], [4.481, -2.727], [4.69, -3.239], [4.481, -3.745], [4.204, -3.924], [3.876, -3.967], [2.395, -3.967], [1.77, -5.042], [0.913, -5.652], [0.913, -5.733], [1.519, -5.69], [2.749, -5.69], [3.876, -5.69], [4.2, -5.727], [4.481, -5.894], [4.69, -6.4], [4.481, -6.907], [4.204, -7.088], [3.876, -7.129], [-3.847, -7.129], [-4.184, -7.086], [-4.472, -6.907], [-4.661, -6.433], [-4.472, -5.927]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.9686, 0.5569, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.032, 352.798]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [28.027, 28.027]}}, {"ty": "fl", "c": {"a": 0, "k": [0.9961, 0.7412, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.032, 352.799]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [30.044, 30.044]}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.022}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.9686, 0.5569, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.753, 352.129]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [4.75, 57.5]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.949, 0.7216]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-27.375, 95.5]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [150, 100]}}]}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [4.75, 57.5]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.949, 0.7216]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-35.625, 95.5]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 29}, "p": {"a": 0, "k": [208.094, 352.25]}, "a": {"a": 0, "k": [-30.906, 95.5]}, "s": {"a": 0, "k": [100, 81.014]}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [40.33, 40.33]}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1.022}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8745, 0.3137]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [207.754, 352.129]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "803", "ip": 170, "op": 1794, "st": -6, "parent": 13}, {"ind": 17, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 173, "s": [61.521, 4.25, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "t": 193, "s": [60.021, -167.75, 0]}, {"t": 213, "s": [62.021, 4.25, 0]}], "a": 1}, "a": {"a": 0, "k": [43.75, -64.75, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [25, 25]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8745, 0.3137]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [43.75, -64.75]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "802", "ip": 173, "op": 213, "st": 6, "parent": 1}, {"ind": 18, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 181, "s": [97.521, 4.25, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "t": 201, "s": [96.021, -138.25, 0]}, {"t": 221, "s": [98.021, 4.25, 0]}], "a": 1}, "a": {"a": 0, "k": [43.75, -64.75, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [20, 20]}}, {"ty": "fl", "c": {"a": 0, "k": [0.9961, 0.7412, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [43.75, -64.75]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "801", "ip": 181, "op": 221, "st": 14, "parent": 1}, {"ind": 19, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 164, "s": [-1.75, 51.75, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 1, "y": 0}, "t": 183, "s": [-4.25, -155.75, 0]}, {"t": 203, "s": [-1.75, 51.75, 0]}], "a": 1}, "a": {"a": 0, "k": [43.75, -64.75, 0]}, "s": {"a": 0, "k": [100, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "p": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [35, 35]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8745, 0.3137]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [43.75, -64.75]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}}]}], "hasMask": false, "ln": "800", "ip": 164, "op": 204, "st": -3, "parent": 1}, {"ind": 20, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.678}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [42.012, 6.844, 0], "ti": [-0.101, 0.533, 0], "to": [0.024, -0.139, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.112}, "t": 170, "s": [42.153, 6.013, 0], "ti": [-0.22, 1.012, 0], "to": [0.101, -0.533, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.136}, "t": 171, "s": [42.62, 3.644, 0], "ti": [-0.36, 1.427, 0], "to": [0.22, -1.012, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.146}, "t": 172, "s": [43.473, -0.062, 0], "ti": [-0.524, 1.774, 0], "to": [0.36, -1.427, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.152}, "t": 173, "s": [44.782, -4.916, 0], "ti": [-0.71, 2.049, 0], "to": [0.524, -1.774, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 174, "s": [46.615, -10.705, 0], "ti": [-0.92, 2.249, 0], "to": [0.71, -2.049, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 175, "s": [49.042, -17.209, 0], "ti": [-1.152, 2.367, 0], "to": [0.92, -2.249, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.161}, "t": 176, "s": [52.138, -24.201, 0], "ti": [-1.403, 2.396, 0], "to": [1.152, -2.367, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.163}, "t": 177, "s": [55.956, -31.409, 0], "ti": [-1.663, 2.33, 0], "to": [1.403, -2.396, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 178, "s": [60.553, -38.574, 0], "ti": [-1.915, 2.164, 0], "to": [1.663, -2.33, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [65.933, -45.39, 0], "ti": [-2.134, 1.902, 0], "to": [1.915, -2.164, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.168}, "t": 180, "s": [72.042, -51.556, 0], "ti": [-2.288, 1.564, 0], "to": [2.134, -1.902, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.17}, "t": 181, "s": [78.739, -56.802, 0], "ti": [-2.344, 1.186, 0], "to": [2.288, -1.564, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.172}, "t": 182, "s": [85.77, -60.939, 0], "ti": [-2.284, 0.815, 0], "to": [2.344, -1.186, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 183, "s": [92.802, -63.917, 0], "ti": [-2.107, 0.496, 0], "to": [2.284, -0.815, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 184, "s": [99.473, -65.832, 0], "ti": [-1.827, 0.253, 0], "to": [2.107, -0.496, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.184}, "t": 185, "s": [105.443, -66.891, 0], "ti": [-1.467, 0.096, 0], "to": [1.827, -0.253, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.193}, "t": 186, "s": [110.437, -67.351, 0], "ti": [-1.038, 0.014, 0], "to": [1.467, -0.096, 0]}, {"i": {"x": 0.833, "y": 0.883}, "o": {"x": 0.167, "y": 0.214}, "t": 187, "s": [114.244, -67.464, 0], "ti": [-0.545, -0.01, 0], "to": [1.038, -0.014, 0]}, {"i": {"x": 0.833, "y": 0.28}, "o": {"x": 0.167, "y": 0.288}, "t": 188, "s": [116.665, -67.435, 0], "ti": [-1.386, -0.126, 0], "to": [0.545, 0.01, 0]}, {"i": {"x": 0.833, "y": 0.722}, "o": {"x": 0.167, "y": 0.094}, "t": 189, "s": [117.513, -67.406, 0], "ti": [-4.021, -1.039, 0], "to": [1.386, 0.126, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.119}, "t": 190, "s": [124.983, -66.681, 0], "ti": [-5.763, -3.482, 0], "to": [4.021, 1.039, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.145}, "t": 191, "s": [141.637, -61.172, 0], "ti": [-5.15, -6.629, 0], "to": [5.763, 3.482, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.155}, "t": 192, "s": [159.558, -45.79, 0], "ti": [-3.34, -8.892, 0], "to": [5.15, 6.629, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.161}, "t": 193, "s": [172.534, -21.396, 0], "ti": [-1.514, -9.499, 0], "to": [3.34, 8.892, 0]}, {"i": {"x": 0.833, "y": 0.897}, "o": {"x": 0.167, "y": 0.172}, "t": 194, "s": [179, 7.565, 0], "ti": [-0.342, -5.757, 0], "to": [1.514, 9.499, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.443}, "t": 195, "s": [181.619, 35.598, 0], "ti": [-0.008, -1.807, 0], "to": [0.342, 5.757, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.209}, "t": 196, "s": [181.651, 42.105, 0], "ti": [-0.004, -1.172, 0], "to": [0.008, 1.807, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.217}, "t": 197, "s": [181.668, 46.44, 0], "ti": [-0.001, -0.694, 0], "to": [0.004, 1.172, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.236}, "t": 198, "s": [181.676, 49.135, 0], "ti": [0.001, -0.342, 0], "to": [0.001, 0.694, 0]}, {"i": {"x": 0.833, "y": 0.895}, "o": {"x": 0.167, "y": 0.296}, "t": 199, "s": [181.676, 50.607, 0], "ti": [0.002, -0.086, 0], "to": [-0.001, 0.342, 0]}, {"i": {"x": 0.833, "y": 0.634}, "o": {"x": 0.167, "y": 0.407}, "t": 200, "s": [181.672, 51.185, 0], "ti": [0.003, 0.094, 0], "to": [-0.002, 0.086, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.108}, "t": 201, "s": [181.664, 51.123, 0], "ti": [0.004, 0.217, 0], "to": [-0.003, -0.094, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.136}, "t": 202, "s": [181.654, 50.618, 0], "ti": [0.004, 0.296, 0], "to": [-0.004, -0.217, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.151}, "t": 203, "s": [181.642, 49.819, 0], "ti": [0.004, 0.342, 0], "to": [-0.004, -0.296, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.159}, "t": 204, "s": [181.631, 48.84, 0], "ti": [0.004, 0.364, 0], "to": [-0.004, -0.342, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.164}, "t": 205, "s": [181.619, 47.764, 0], "ti": [0.004, 0.369, 0], "to": [-0.004, -0.364, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [181.608, 46.654, 0], "ti": [0.003, 0.361, 0], "to": [-0.004, -0.369, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 207, "s": [181.597, 45.551, 0], "ti": [0.003, 0.345, 0], "to": [-0.003, -0.361, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 208, "s": [181.587, 44.488, 0], "ti": [0.003, 0.323, 0], "to": [-0.003, -0.345, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 209, "s": [181.578, 43.483, 0], "ti": [0.003, 0.299, 0], "to": [-0.003, -0.323, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 210, "s": [181.569, 42.549, 0], "ti": [0.002, 0.273, 0], "to": [-0.003, -0.299, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 211, "s": [181.562, 41.692, 0], "ti": [0.002, 0.246, 0], "to": [-0.002, -0.273, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.176}, "t": 212, "s": [181.555, 40.914, 0], "ti": [0.002, 0.221, 0], "to": [-0.002, -0.246, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 213, "s": [181.549, 40.214, 0], "ti": [0.002, 0.196, 0], "to": [-0.002, -0.221, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 214, "s": [181.544, 39.59, 0], "ti": [0.001, 0.174, 0], "to": [-0.002, -0.196, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 215, "s": [181.539, 39.036, 0], "ti": [0.001, 0.153, 0], "to": [-0.001, -0.174, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 216, "s": [181.535, 38.548, 0], "ti": [0.001, 0.133, 0], "to": [-0.001, -0.153, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 217, "s": [181.532, 38.121, 0], "ti": [0.001, 0.116, 0], "to": [-0.001, -0.133, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.18}, "t": 218, "s": [181.529, 37.748, 0], "ti": [0.001, 0.101, 0], "to": [-0.001, -0.116, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 219, "s": [181.526, 37.425, 0], "ti": [0.001, 0.087, 0], "to": [-0.001, -0.101, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.18}, "t": 220, "s": [181.524, 37.145, 0], "ti": [0.001, 0.075, 0], "to": [-0.001, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 221, "s": [181.522, 36.904, 0], "ti": [0.001, 0.064, 0], "to": [-0.001, -0.075, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.18}, "t": 222, "s": [181.52, 36.698, 0], "ti": [0, 0.055, 0], "to": [-0.001, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.182}, "t": 223, "s": [181.519, 36.521, 0], "ti": [0, 0.046, 0], "to": [0, -0.055, 0]}, {"i": {"x": 0.833, "y": 0.494}, "o": {"x": 0.167, "y": 0.181}, "t": 224, "s": [181.518, 36.371, 0], "ti": [0.001, 0.129, 0], "to": [0, -0.046, 0]}, {"i": {"x": 0.833, "y": 0.917}, "o": {"x": 0.167, "y": 0.1}, "t": 225, "s": [181.517, 36.243, 0], "ti": [0.001, 0.108, 0], "to": [-0.001, -0.129, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [181.512, 35.594, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [181.512, 35.594, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [181.512, 35.594, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [181.512, 35.594, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [181.512, 35.594, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [181.512, 35.594, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [181.512, 35.594, 0]}, {"t": 233, "s": [181.512, 35.594, 0], "ti": [-0.024, 0.139, 0], "to": [0, 0, 0]}], "x": "var $bm_rt;\nvar eff, amp, freq, decay;\ntry {\n    eff = effect('Elastic Controller');\n    amp = $bm_div($bm_div(eff(1), 10), 100);\n    freq = $bm_div(eff(2), 100);\n    decay = $bm_div(eff(3), 100);\n    function elastic(amp, freq, decay) {\n        ;\n        function calc(n) {\n            ;\n            var t = $bm_sub(time, key(n).time);\n            var v = velocityAtTime(key(n).time - thisComp.frameDuration / 10);\n            return $bm_div($bm_mul($bm_mul(v, $bm_div(amp, freq)), Math.sin(freq * t * 2 * Math.PI)), Math.exp(t * (decay * 2) * Math.E));\n        }\n        ;\n        if ($bm_sum(numKeys, 0)) {\n            return 0;\n        }\n        ;\n        var n = nearestKey(time).index;\n        ;\n        if ($bm_sum(key(n).time, time))\n            n--;\n        return n > 1 && time <= key(n).time + 1 / decay ? calc(n) + calc(n - 1) : 0;\n    }\n    ;\n    $bm_rt = $bm_sum(value, elastic(amp, freq, decay));\n} catch (e) {\n    $bm_rt = value = value;\n}", "a": 1}, "a": {"a": 0, "k": [50, 50, 50]}, "s": {"a": 0, "k": [80, 80, 100]}}, "hasMask": false, "ln": "799", "ip": 169, "op": 1792, "st": -8, "parent": 1}, {"ind": 21, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.514, 48.029, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [0.094, -0.417], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.593, 1.453], [0, 0], [0, 0], [0, 0], [0, 0], [0.012, 0.197], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.01, 0.256], [0, 0], [0, 0], [0, 0], [-0.431, 1.29], [0, 0], [0, 0], [0, 0], [0, 0], [0.002, -0.003], [0, 0], [0, 0], [0, 0], [0.095, -0.455], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.886, -2.74], [0.575, -2.622], [0.577, -2.624], [0.434, -1.617], [1.265, -1.928], [0.8, -0.813], [0.136, -0.56], [-0.708, 1.215], [-0.384, 3.382], [-0.884, 3.856], [-1.35, 0.96], [-0.289, -0.391], [-0.285, -0.4], [-0.813, -0.199], [-0.348, -1.322], [0.047, -1.471], [0.136, -2.456], [-0.727, -2.128], [-0.261, -3.251], [1.35, -3.856]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 15}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "798", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 22, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.037, 47.317, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.086, 0.327], [-0.952, -0.261], [0, 0], [0.353, -2.362], [0.087, -0.326], [0.952, 0.263], [-0.353, 2.363]], "o": [[0.903, -3.423], [0, 0], [0.728, 0.2], [-0.07, 0.317], [-0.901, 3.429], [-0.723, -0.202], [0.069, -0.317]], "v": [[-1.85, 0.202], [1.51, -5.522], [1.512, -5.522], [2.081, -1.167], [1.848, -0.202], [-1.513, 5.522], [-2.082, 1.167]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.6039, 0.1961]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 15}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "797", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 23, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [48.491, 41.683, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.112, 0.396], [-0.941, -0.239], [0, 0], [0.644, -2.83], [-0.023, 0.142], [0.718, 0.184], [0.949, -3.334], [0.073, -0.307], [0, 0], [0, 0]], "o": [[0.949, -3.334], [0, 0], [0.828, 0.211], [0.032, -0.15], [0.382, -2.295], [-0.937, -0.233], [-0.09, 0.319], [0, 0], [0, 0], [0.086, -0.379]], "v": [[-1.87, 2.01], [1.551, -3.596], [1.551, -3.595], [1.828, 1.722], [1.91, 1.283], [1.415, -2.935], [-2.005, 2.671], [-2.25, 3.611], [-2.21, 3.389], [-2.167, 3.172]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.7137, 0.3922, 0.0314]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 15}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "796", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 24, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [44.439, 53.315, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.07, 0.316], [0.026, -0.15], [0, 0], [0.103, -0.402], [0.951, 0.259], [-0.451, 2.575], [-0.03, 0.153], [0, 0], [-0.727, -0.197], [-0.909, 3.414]], "o": [[-0.027, 0.149], [0, 0], [-0.08, 0.39], [-0.911, 3.414], [-0.772, -0.212], [0.027, -0.149], [0, 0], [-0.352, 2.353], [0.948, 0.254], [0.088, -0.325]], "v": [[2.23, -3.684], [2.152, -3.235], [2.151, -3.234], [1.876, -2.047], [-1.495, 3.666], [-1.982, -1.124], [-1.897, -1.577], [-1.934, -1.35], [-1.374, 2.99], [1.995, -2.722]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.7137, 0.3922, 0.0314]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 15}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "795", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 25, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.18, 47.778, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-1.449, -0.394], [1.389, -5.202], [0, 0], [1.449, 0.394], [-1.389, 5.202]], "o": [[1.45, 0.395], [0, 0], [-1.388, 5.201], [-1.45, -0.394], [1.388, -5.201]], "v": [[2.514, -9.417], [2.624, 0.716], [2.624, 0.715], [-2.514, 9.417], [-2.624, -0.715]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.9922, 0.7725, 0.1882]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 15}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "794", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 26, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [59.417, 43.372, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [-0.967, 0.082], [-0.312, -0.504], [1.354, -1.715]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.82, 0.658], [1.193, -1.463], [1.82, -0.879], [-1.338, 1.463]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [1.278, -1.638]}, "e": {"a": 0, "k": [-0.605, -0.553]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 0.718, 0.396, 0.031, 0.5, 0.859, 0.639, 0.395, 1, 1, 0.881, 0.759, 0, 1, 0.5, 0.8, 1, 0.6]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 70.244}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "793", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 27, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [54.889, 49.187, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.968, 0.071], [-0.002, -0.004], [0, 0], [0, 0], [2.63, -1.927], [0, 0]], "o": [[0.142, 0.001], [0, 0], [0.551, 0.695], [0, 0], [0, 0], [0, 0]], "v": [[1.271, -1.968], [1.805, -1.316], [1.803, -1.317], [2.59, -0.151], [-1.413, 1.968], [-2.59, 0.085]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [1.648, -1.092]}, "e": {"a": 0, "k": [-0.236, -0.151]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 0.8, 0.478, 0.137, 0.5, 0.896, 0.556, 0.201, 1, 0.991, 0.633, 0.265]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "792", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 28, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [50.82, 50.222, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [1.413, -5.209], [1.456, 0.39], [0, 0]], "o": [[0, 0], [0, 0], [1.457, 0.39], [-1.413, 5.21], [0, 0], [0, 0]], "v": [[0.33, -9.946], [3.675, -8.951], [3.675, -8.951], [3.754, 1.189], [-1.446, 9.919], [-4.791, 8.924]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [-5.508, 3.179]}, "e": {"a": 0, "k": [5.909, -3.796]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 1, 0.678, 0.396, 0.5, 0.845, 0.524, 0.198, 1, 0.69, 0.369, 0]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 15}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "791", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 29, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [50.82, 50.222, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [1.413, -5.209], [1.456, 0.39], [0, 0]], "o": [[0, 0], [0, 0], [1.457, 0.39], [-1.413, 5.21], [0, 0], [0, 0]], "v": [[0.33, -9.946], [3.675, -8.951], [3.675, -8.951], [3.754, 1.189], [-1.446, 9.919], [-4.791, 8.924]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [-5.508, 3.179]}, "e": {"a": 0, "k": [5.909, -3.796]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 1, 0.678, 0.396, 0.5, 0.845, 0.524, 0.198, 1, 0.69, 0.369, 0]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 15}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "790", "ip": 175, "op": 1975, "st": 175, "parent": 20}, {"ind": 30, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.692}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [20.669, 53.868, 0], "ti": [-0.76, 0.907, 0], "to": [0.206, -0.245, 0]}, {"i": {"x": 0.833, "y": 0.791}, "o": {"x": 0.167, "y": 0.114}, "t": 162, "s": [21.903, 52.396, 0], "ti": [-1.388, 1.656, 0], "to": [0.76, -0.907, 0]}, {"i": {"x": 0.833, "y": 0.811}, "o": {"x": 0.167, "y": 0.139}, "t": 163, "s": [25.23, 48.426, 0], "ti": [-1.895, 2.261, 0], "to": [1.388, -1.656, 0]}, {"i": {"x": 0.833, "y": 0.818}, "o": {"x": 0.167, "y": 0.149}, "t": 164, "s": [30.229, 42.461, 0], "ti": [-2.314, 2.761, 0], "to": [1.895, -2.261, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.154}, "t": 165, "s": [36.598, 34.863, 0], "ti": [-2.666, 3.181, 0], "to": [2.314, -2.761, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.157}, "t": 166, "s": [44.112, 25.898, 0], "ti": [-2.966, 3.539, 0], "to": [2.666, -3.181, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 167, "s": [52.597, 15.774, 0], "ti": [-3.224, 3.846, 0], "to": [2.966, -3.539, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.161}, "t": 168, "s": [61.911, 4.662, 0], "ti": [-3.445, 4.11, 0], "to": [3.224, -3.846, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.162}, "t": 169, "s": [71.94, -7.303, 0], "ti": [-3.634, 4.336, 0], "to": [3.445, -4.11, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.163}, "t": 170, "s": [82.581, -19.999, 0], "ti": [-3.793, 4.526, 0], "to": [3.634, -4.336, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.164}, "t": 171, "s": [93.745, -33.319, 0], "ti": [-3.923, 4.681, 0], "to": [3.793, -4.526, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 172, "s": [105.34, -47.154, 0], "ti": [-4.023, 4, 0], "to": [3.923, -4.681, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 173, "s": [117.286, -61.406, 0], "ti": [-4.084, 4.872, 0], "to": [4.023, -4, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 174, "s": [129.477, -75.951, 0], "ti": [-4.092, 4.883, 0], "to": [4.084, -4.872, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [141.788, -90.64, 0], "ti": [-4.012, 4.787, 0], "to": [4.092, -4.883, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.17}, "t": 176, "s": [154.031, -105.247, 0], "ti": [-3.728, 4.447, 0], "to": [4.012, -4.787, 0]}, {"i": {"x": 0.833, "y": 0.873}, "o": {"x": 0.167, "y": 0.177}, "t": 177, "s": [165.86, -119.361, 0], "ti": [-2.668, 3.183, 0], "to": [3.728, -4.447, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.244}, "t": 178, "s": [176.397, -131.932, 0], "ti": [-1.657, 1.977, 0], "to": [2.668, -3.183, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 179, "s": [181.868, -138.46, 0], "ti": [-1.337, 1.595, 0], "to": [1.657, -1.977, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.188}, "t": 180, "s": [186.337, -143.791, 0], "ti": [-1.047, 1.249, 0], "to": [1.337, -1.595, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 181, "s": [189.891, -148.031, 0], "ti": [-0.787, 0.939, 0], "to": [1.047, -1.249, 0]}, {"i": {"x": 0.833, "y": 0.86}, "o": {"x": 0.167, "y": 0.197}, "t": 182, "s": [192.619, -151.287, 0], "ti": [-0.556, 0.663, 0], "to": [0.787, -0.939, 0]}, {"i": {"x": 0.833, "y": 0.868}, "o": {"x": 0.167, "y": 0.207}, "t": 183, "s": [194.611, -153.664, 0], "ti": [-0.354, 0.422, 0], "to": [0.556, -0.663, 0]}, {"i": {"x": 0.833, "y": 0.885}, "o": {"x": 0.167, "y": 0.227}, "t": 184, "s": [195.954, -155.266, 0], "ti": [-0.178, 0.213, 0], "to": [0.354, -0.422, 0]}, {"i": {"x": 0.833, "y": 0.879}, "o": {"x": 0.167, "y": 0.304}, "t": 185, "s": [196.732, -156.194, 0], "ti": [-0.029, 0.035, 0], "to": [0.178, -0.213, 0]}, {"i": {"x": 0.833, "y": 0.631}, "o": {"x": 0.167, "y": 0.266}, "t": 186, "s": [197.025, -156.544, 0], "ti": [0.096, -0.114, 0], "to": [0.029, -0.035, 0]}, {"i": {"x": 0.833, "y": 0.783}, "o": {"x": 0.167, "y": 0.108}, "t": 187, "s": [196.909, -156.404, 0], "ti": [0.198, -0.236, 0], "to": [-0.096, 0.114, 0]}, {"i": {"x": 0.833, "y": 0.809}, "o": {"x": 0.167, "y": 0.135}, "t": 188, "s": [196.452, -155.859, 0], "ti": [0.28, -0.334, 0], "to": [-0.198, 0.236, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.148}, "t": 189, "s": [195.72, -154.986, 0], "ti": [0.344, -0.41, 0], "to": [-0.28, 0.334, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.154}, "t": 190, "s": [194.771, -153.853, 0], "ti": [0.391, -0.466, 0], "to": [-0.344, 0.41, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.159}, "t": 191, "s": [193.657, -152.525, 0], "ti": [0.423, -0.505, 0], "to": [-0.391, 0.466, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.162}, "t": 192, "s": [192.426, -151.056, 0], "ti": [0.443, -0.528, 0], "to": [-0.423, 0.505, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 193, "s": [191.118, -149.495, 0], "ti": [0.451, -0.539, 0], "to": [-0.443, 0.528, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 194, "s": [189.769, -147.886, 0], "ti": [0.451, -0.538, 0], "to": [-0.451, 0.539, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 195, "s": [188.41, -146.264, 0], "ti": [0.442, -0.528, 0], "to": [-0.451, 0.538, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 196, "s": [187.065, -144.66, 0], "ti": [0.427, -0.51, 0], "to": [-0.442, 0.528, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 197, "s": [185.757, -143.099, 0], "ti": [0.407, -0.486, 0], "to": [-0.427, 0.51, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 198, "s": [184.501, -141.601, 0], "ti": [0.384, -0.458, 0], "to": [-0.407, 0.486, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 199, "s": [183.312, -140.182, 0], "ti": [0.357, -0.426, 0], "to": [-0.384, 0.458, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 200, "s": [182.199, -138.854, 0], "ti": [0.329, -0.392, 0], "to": [-0.357, 0.426, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 201, "s": [181.168, -137.624, 0], "ti": [0.299, -0.357, 0], "to": [-0.329, 0.392, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 202, "s": [180.225, -136.499, 0], "ti": [0.27, -0.322, 0], "to": [-0.299, 0.357, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 203, "s": [179.371, -135.481, 0], "ti": [0.24, -0.287, 0], "to": [-0.27, 0.322, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 204, "s": [178.607, -134.569, 0], "ti": [0.211, -0.252, 0], "to": [-0.24, 0.287, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 205, "s": [177.931, -133.762, 0], "ti": [0.183, -0.219, 0], "to": [-0.211, 0.252, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.18}, "t": 206, "s": [177.339, -133.056, 0], "ti": [0.157, -0.187, 0], "to": [-0.183, 0.219, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.182}, "t": 207, "s": [176.83, -132.448, 0], "ti": [0.132, -0.158, 0], "to": [-0.157, 0.187, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.183}, "t": 208, "s": [176.397, -131.932, 0], "ti": [0.109, -0.13, 0], "to": [-0.132, 0.158, 0]}, {"i": {"x": 0.833, "y": 0.85}, "o": {"x": 0.167, "y": 0.185}, "t": 209, "s": [176.036, -131.501, 0], "ti": [0.088, -0.105, 0], "to": [-0.109, 0.13, 0]}, {"i": {"x": 0.833, "y": 0.852}, "o": {"x": 0.167, "y": 0.189}, "t": 210, "s": [175.741, -131.149, 0], "ti": [0.069, -0.082, 0], "to": [-0.088, 0.105, 0]}, {"i": {"x": 0.833, "y": 0.856}, "o": {"x": 0.167, "y": 0.192}, "t": 211, "s": [175.507, -130.87, 0], "ti": [0.052, -0.062, 0], "to": [-0.069, 0.082, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.197}, "t": 212, "s": [175.327, -130.655, 0], "ti": [0.037, -0.044, 0], "to": [-0.052, 0.062, 0]}, {"i": {"x": 0.833, "y": 0.869}, "o": {"x": 0.167, "y": 0.206}, "t": 213, "s": [175.195, -130.498, 0], "ti": [0.023, -0.028, 0], "to": [-0.037, 0.044, 0]}, {"i": {"x": 0.833, "y": 0.885}, "o": {"x": 0.167, "y": 0.228}, "t": 214, "s": [175.106, -130.392, 0], "ti": [0.012, -0.014, 0], "to": [-0.023, 0.028, 0]}, {"i": {"x": 0.833, "y": 0.88}, "o": {"x": 0.167, "y": 0.307}, "t": 215, "s": [175.055, -130.331, 0], "ti": [0.002, -0.002, 0], "to": [-0.012, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.292}, "t": 216, "s": [175.036, -130.308, 0], "ti": [-0.001, 0.002, 0], "to": [-0.002, 0.002, 0]}, {"t": 217, "s": [175.043, -130.317, 0], "ti": [-0.206, 0.245, 0], "to": [0.001, -0.002, 0]}], "x": "var $bm_rt;\nvar eff, amp, freq, decay;\ntry {\n    eff = effect('Elastic Controller');\n    amp = $bm_div($bm_div(eff(1), 10), 100);\n    freq = $bm_div(eff(2), 100);\n    decay = $bm_div(eff(3), 100);\n    function elastic(amp, freq, decay) {\n        ;\n        function calc(n) {\n            ;\n            var t = $bm_sub(time, key(n).time);\n            var v = velocityAtTime(key(n).time - thisComp.frameDuration / 10);\n            return $bm_div($bm_mul($bm_mul(v, $bm_div(amp, freq)), Math.sin(freq * t * 2 * Math.PI)), Math.exp(t * (decay * 2) * Math.E));\n        }\n        ;\n        if ($bm_sum(numKeys, 0)) {\n            return 0;\n        }\n        ;\n        var n = nearestKey(time).index;\n        ;\n        if ($bm_sum(key(n).time, time))\n            n--;\n        return n > 1 && time <= key(n).time + 1 / decay ? calc(n) + calc(n - 1) : 0;\n    }\n    ;\n    $bm_rt = $bm_sum(value, elastic(amp, freq, decay));\n} catch (e) {\n    $bm_rt = value = value;\n}", "a": 1}, "a": {"a": 0, "k": [50, 50, 50]}, "s": {"a": 0, "k": [100, 100]}}, "hasMask": false, "ln": "789", "ip": 161, "op": 1787, "st": -13, "parent": 1}, {"ind": 31, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.41, 46.003, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.092, -0.058], [-0.139, 0.052], [0, 0], [0, 0], [-0.415, -0.562], [0, 0], [0.037, -0.108], [-0.04, -0.105], [-0.093, -0.058], [-0.138, 0.052], [0, 0], [0.058, -0.202], [0.132, -0.169], [0.196, -0.126], [0.212, -0.08], [0, 0], [0.046, -0.129], [-0.052, -0.141], [-0.064, -0.07], [-0.104, -0.046], [0, 0], [-0.08, -0.003], [-0.074, 0.028], [-0.046, 0.129], [0.053, 0.141], [0.157, 0.079], [0, 0], [-0.203, 0.169], [-0.132, 0.231], [-0.049, 0.292], [0.095, 0.359], [0, 0], [-0.034, 0.098], [0.04, 0.105], [0.102, 0.055], [0.139, -0.052], [0, 0], [0.197, 0.104], [0.169, 0.012], [0, 0], [-0.169, 0.056], [-0.239, 0.089], [0, 0], [-0.034, 0.098], [0.04, 0.105], [0.102, 0.055], [0.139, -0.052], [0, 0], [0.037, -0.108], [-0.04, -0.105]], "o": [[0.099, 0.059], [0, 0], [0, 0], [0.872, -0.329], [0, 0], [-0.138, 0.053], [-0.034, 0.098], [0.04, 0.104], [0.101, 0.059], [0, 0], [0.055, 0.251], [-0.059, 0.2], [-0.129, 0.16], [-0.193, 0.113], [0, 0], [-0.129, 0.049], [-0.049, 0.123], [0.031, 0.083], [0.062, 0.062], [0, 0], [0.105, 0.053], [0.083, -0.006], [0.144, -0.052], [0.043, -0.129], [-0.055, -0.151], [0, 0], [0.218, -0.108], [0.209, -0.175], [0.141, -0.233], [0.043, -0.298], [0, 0], [0.138, -0.052], [0.04, -0.108], [-0.04, -0.104], [-0.095, -0.062], [0, 0], [-0.16, -0.212], [-0.196, -0.105], [0, 0], [0.037, -0.006], [0.169, -0.055], [0, 0], [0.138, -0.053], [0.04, -0.108], [-0.04, -0.104], [-0.095, -0.062], [0, 0], [-0.138, 0.052], [-0.034, 0.098], [0.037, 0.104]], "v": [[-3.085, -1.878], [-2.728, -1.868], [-2.722, -1.862], [-2.259, -2.037], [-0.327, -1.684], [-2.369, -0.917], [-2.633, -0.677], [-2.627, -0.373], [-2.427, -0.128], [-2.071, -0.118], [0.023, -0.904], [0.02, -0.223], [-0.266, 0.333], [-0.754, 0.76], [-1.362, 1.052], [-1.497, 1.101], [-1.761, 1.368], [-1.755, 1.764], [-1.611, 1.991], [-1.362, 2.154], [2.479, 4.048], [2.759, 4.134], [2.998, 4.082], [3.284, 3.809], [3.271, 3.404], [2.955, 3.06], [-0.29, 1.494], [0.342, 1.076], [0.855, 0.471], [1.137, -0.315], [1.061, -1.3], [1.742, -1.555], [2, -1.779], [2, -2.099], [1.791, -2.341], [1.444, -2.354], [0.711, -2.08], [0.176, -2.559], [-0.373, -2.737], [-0.392, -2.784], [-0.085, -2.873], [0.529, -3.091], [1.088, -3.299], [1.346, -3.523], [1.346, -3.843], [1.137, -4.085], [0.79, -4.098], [-3.026, -2.667], [-3.29, -2.427], [-3.281, -2.123]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "788", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 32, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [49.16, 40.334, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-2.623, 1.905], [0, 0], [0, 0], [-3.064, -3.15], [0.178, 0.14], [2.007, -1.455], [-3.157, -4.12], [-0.325, -0.349], [0.089, 0.077], [0.086, 0.076], [0.362, 0.483]], "o": [[0, 0], [0, 0], [2.311, -1.678], [-0.171, -0.157], [-2.812, -2.261], [-2.622, 1.904], [0.303, 0.387], [-0.086, -0.074], [-0.084, -0.074], [-0.419, -0.437], [-3.16, -4.118]], "v": [[-3.792, -5.986], [-3.779, -5.986], [-3.781, -5.984], [5.507, -3.315], [4.986, -3.758], [-3.042, -5.265], [-2.073, 5.64], [-1.131, 6.743], [-1.395, 6.52], [-1.649, 6.298], [-2.821, 4.917]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.7137, 0.3922, 0.0314]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "787", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 33, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [44.212, 54.318, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0.321, 0.349], [-0.17, -0.153], [0, 0], [-0.36, -0.472], [2.578, -1.905], [2.879, 2.604], [0.164, 0.164], [-0.085, -0.072], [-1.971, 1.457], [3.107, 4.131]], "o": [[0.174, 0.146], [0, 0], [0.413, 0.428], [3.107, 4.131], [-2.093, 1.549], [-0.166, -0.153], [0.085, 0.079], [2.768, 2.266], [2.578, -1.907], [-0.297, -0.386]], "v": [[0.976, -6.744], [1.489, -6.298], [1.487, -6.298], [2.649, -4.948], [3.608, 5.983], [-4.798, 4.03], [-5.292, 3.558], [-5.032, 3.781], [2.861, 5.293], [1.904, -5.639]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.7137, 0.3922, 0.0314]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "786", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 34, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.646, 47.105, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-3.174, -4.253], [2.634, -1.965], [0, 0], [3.173, 4.253], [-2.636, 1.965]], "o": [[3.173, 4.254], [0, 0], [-2.634, 1.965], [-3.173, -4.255], [2.634, -1.965]], "v": [[4.771, -3.559], [5.747, 7.702], [5.747, 7.703], [-4.77, 3.559], [-5.746, -7.703]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.9961, 0.7412, 0]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "785", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 35, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [47.221, 46.887, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-4.616, -6.219], [3.832, -2.873], [0, 0], [4.616, 6.219], [-3.834, 2.873]], "o": [[4.616, 6.22], [0, 0], [-3.832, 2.873], [-4.616, -6.222], [3.832, -2.873]], "v": [[6.94, -5.204], [8.36, 11.262], [8.36, 11.264], [-6.939, 5.204], [-8.359, -11.264]], "c": true}, "a": 0}}, {"ty": "st", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 1}, "c": {"a": 0, "k": [0, 0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8745, 0.3137]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "784", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 36, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [65.27, 58.062, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-1.396, 0.103], [-0.002, -0.005], [0, 0], [0, 0], [3.793, -2.779], [0, 0]], "o": [[0.204, 0.001], [0, 0], [0.795, 1.004], [0, 0], [0, 0], [0, 0]], "v": [[1.833, -2.838], [2.602, -1.898], [2.6, -1.9], [3.734, -0.217], [-2.037, 2.838], [-3.734, 0.122]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [2.376, -1.575]}, "e": {"a": 0, "k": [-0.34, -0.217]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 0.8, 0.478, 0.137, 0.5, 0.896, 0.556, 0.201, 1, 0.991, 0.633, 0.265]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "783", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 37, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [40.468, 58.613, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[2.856, 3.856], [1.722, 7.451], [0, 0], [2.898, -2.902], [0, 0], [0, 0]], "o": [[-9.804, -13.244], [0, 0], [1.357, 3.055], [-1.734, 1.737], [0, 0], [-3.327, -0.634]], "v": [[-7.557, -1.249], [4.968, -5.517], [8.703, -7.214], [7.934, 4.514], [3.449, 7.214], [2.357, 5.753]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [-14.309, -8.6]}, "e": {"a": 0, "k": [10.807, -3.459]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 1, 0.678, 0.396, 0.5, 0.719, 0.458, 0.202, 1, 0.438, 0.238, 0.009]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "782", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 38, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [52.032, 50.244, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [-4.389, -5.927], [0, 0], [3.451, -3.028], [0, 0], [0, 0], [2.855, 3.856], [-0.288, 3.369], [0, 0]], "o": [[3.88, -2.283], [0, 0], [4.45, 6.01], [-1.735, 1.737], [0, 0], [-3.327, -0.634], [-2.821, -3.81], [0, 0], [1.606, -1.973]], "v": [[-7.011, -12.189], [7.676, -5.832], [7.674, -5.835], [9.398, 10.294], [4.913, 12.995], [3.82, 11.533], [-6.093, 4.531], [-9.947, -6.892], [-11.57, -9.061]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [-12.846, -2.822]}, "e": {"a": 0, "k": [12.27, 2.318]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 1, 0.678, 0.396, 0.5, 0.845, 0.524, 0.198, 1, 0.69, 0.369, 0]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 68.301}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "781", "ip": 175, "op": 1800, "st": 0, "parent": 30}, {"ind": 39, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.677}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [42.012, 6.844, 0], "ti": [0.27, 0.704, 0], "to": [-0.068, -0.182, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.112}, "t": 164, "s": [41.602, 5.75, 0], "ti": [0.532, 1.339, 0], "to": [-0.27, -0.704, 0]}, {"i": {"x": 0.833, "y": 0.806}, "o": {"x": 0.167, "y": 0.136}, "t": 165, "s": [40.39, 2.623, 0], "ti": [0.786, 1.896, 0], "to": [-0.532, -1.339, 0]}, {"i": {"x": 0.833, "y": 0.816}, "o": {"x": 0.167, "y": 0.146}, "t": 166, "s": [38.411, -2.282, 0], "ti": [1.031, 2.374, 0], "to": [-0.786, -1.896, 0]}, {"i": {"x": 0.833, "y": 0.821}, "o": {"x": 0.167, "y": 0.152}, "t": 167, "s": [35.677, -8.753, 0], "ti": [1.268, 2.77, 0], "to": [-1.031, -2.374, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.156}, "t": 168, "s": [32.226, -16.524, 0], "ti": [1.497, 3.085, 0], "to": [-1.268, -2.77, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.159}, "t": 169, "s": [28.072, -25.375, 0], "ti": [1.718, 3.317, 0], "to": [-1.497, -3.085, 0]}, {"i": {"x": 0.833, "y": 0.829}, "o": {"x": 0.167, "y": 0.161}, "t": 170, "s": [23.246, -35.036, 0], "ti": [1.932, 3.463, 0], "to": [-1.718, -3.317, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 171, "s": [17.761, -45.278, 0], "ti": [2.137, 3.517, 0], "to": [-1.932, -3.463, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.164}, "t": 172, "s": [11.654, -55.811, 0], "ti": [2.336, 3.481, 0], "to": [-2.137, -3.517, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.166}, "t": 173, "s": [4.94, -66.377, 0], "ti": [2.524, 3.347, 0], "to": [-2.336, -3.481, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [-2.36, -76.699, 0], "ti": [2.697, 3.105, 0], "to": [-2.524, -3.347, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 175, "s": [-10.206, -86.46, 0], "ti": [2.846, 2.75, 0], "to": [-2.697, -3.105, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 176, "s": [-18.544, -95.33, 0], "ti": [2.95, 2.276, 0], "to": [-2.846, -2.75, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.173}, "t": 177, "s": [-27.284, -102.961, 0], "ti": [2.966, 1.694, 0], "to": [-2.95, -2.276, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.175}, "t": 178, "s": [-36.241, -108.988, 0], "ti": [2.842, 1.054, 0], "to": [-2.966, -1.694, 0]}, {"i": {"x": 0.833, "y": 0.848}, "o": {"x": 0.167, "y": 0.179}, "t": 179, "s": [-45.081, -113.125, 0], "ti": [2.532, 0.461, 0], "to": [-2.842, -1.054, 0]}, {"i": {"x": 0.833, "y": 0.854}, "o": {"x": 0.167, "y": 0.185}, "t": 180, "s": [-53.296, -115.314, 0], "ti": [2.042, 0.034, 0], "to": [-2.532, -0.461, 0]}, {"i": {"x": 0.833, "y": 0.864}, "o": {"x": 0.167, "y": 0.194}, "t": 181, "s": [-60.27, -115.889, 0], "ti": [1.429, -0.16, 0], "to": [-2.042, -0.034, 0]}, {"i": {"x": 0.833, "y": 0.882}, "o": {"x": 0.167, "y": 0.215}, "t": 182, "s": [-65.546, -115.518, 0], "ti": [0.74, -0.144, 0], "to": [-1.429, 0.16, 0]}, {"i": {"x": 0.833, "y": 0.284}, "o": {"x": 0.167, "y": 0.283}, "t": 183, "s": [-68.847, -114.926, 0], "ti": [1.824, -0.676, 0], "to": [-0.74, 0.144, 0]}, {"i": {"x": 0.833, "y": 0.724}, "o": {"x": 0.167, "y": 0.094}, "t": 184, "s": [-69.987, -114.656, 0], "ti": [4.611, -3.41, 0], "to": [-1.824, 0.676, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.119}, "t": 185, "s": [-79.789, -110.868, 0], "ti": [5.239, -7.813, 0], "to": [-4.611, 3.41, 0]}, {"i": {"x": 0.833, "y": 0.819}, "o": {"x": 0.167, "y": 0.145}, "t": 186, "s": [-97.651, -94.195, 0], "ti": [3.477, -11.371, 0], "to": [-5.239, 7.813, 0]}, {"i": {"x": 0.833, "y": 0.827}, "o": {"x": 0.167, "y": 0.155}, "t": 187, "s": [-111.223, -63.99, 0], "ti": [1.595, -13.274, 0], "to": [-3.477, 11.371, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.161}, "t": 188, "s": [-118.512, -25.969, 0], "ti": [0.127, -13.481, 0], "to": [-1.595, 13.274, 0]}, {"i": {"x": 0.833, "y": 0.878}, "o": {"x": 0.167, "y": 0.172}, "t": 189, "s": [-120.791, 15.655, 0], "ti": [-0.487, -9.564, 0], "to": [-0.127, 13.481, 0]}, {"i": {"x": 0.833, "y": 0.861}, "o": {"x": 0.167, "y": 0.263}, "t": 190, "s": [-119.277, 54.916, 0], "ti": [-0.393, -5.033, 0], "to": [0.487, 9.564, 0]}, {"i": {"x": 0.833, "y": 0.865}, "o": {"x": 0.167, "y": 0.208}, "t": 191, "s": [-117.866, 73.04, 0], "ti": [-0.258, -3.262, 0], "to": [0.393, 5.033, 0]}, {"i": {"x": 0.833, "y": 0.871}, "o": {"x": 0.167, "y": 0.217}, "t": 192, "s": [-116.918, 85.112, 0], "ti": [-0.156, -1.933, 0], "to": [0.258, 3.262, 0]}, {"i": {"x": 0.833, "y": 0.884}, "o": {"x": 0.167, "y": 0.236}, "t": 193, "s": [-116.32, 92.613, 0], "ti": [-0.08, -0.95, 0], "to": [0.156, 1.933, 0]}, {"i": {"x": 0.833, "y": 0.895}, "o": {"x": 0.167, "y": 0.296}, "t": 194, "s": [-115.983, 96.71, 0], "ti": [-0.025, -0.238, 0], "to": [0.08, 0.95, 0]}, {"i": {"x": 0.833, "y": 0.634}, "o": {"x": 0.167, "y": 0.406}, "t": 195, "s": [-115.838, 98.315, 0], "ti": [0.014, 0.265, 0], "to": [0.025, 0.238, 0]}, {"i": {"x": 0.833, "y": 0.785}, "o": {"x": 0.167, "y": 0.108}, "t": 196, "s": [-115.83, 98.139, 0], "ti": [0.04, 0.607, 0], "to": [-0.014, -0.265, 0]}, {"i": {"x": 0.833, "y": 0.815}, "o": {"x": 0.167, "y": 0.136}, "t": 197, "s": [-115.919, 96.728, 0], "ti": [0.058, 0.827, 0], "to": [-0.04, -0.607, 0]}, {"i": {"x": 0.833, "y": 0.825}, "o": {"x": 0.167, "y": 0.151}, "t": 198, "s": [-116.073, 94.499, 0], "ti": [0.068, 0.955, 0], "to": [-0.058, -0.827, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.159}, "t": 199, "s": [-116.266, 91.768, 0], "ti": [0.073, 1.016, 0], "to": [-0.068, -0.955, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.164}, "t": 200, "s": [-116.482, 88.768, 0], "ti": [0.075, 1.028, 0], "to": [-0.073, -1.016, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [-116.706, 85.671, 0], "ti": [0.074, 1.006, 0], "to": [-0.075, -1.028, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 202, "s": [-116.931, 82.598, 0], "ti": [0.071, 0.961, 0], "to": [-0.074, -1.006, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.172}, "t": 203, "s": [-117.148, 79.634, 0], "ti": [0.066, 0.901, 0], "to": [-0.071, -0.961, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.173}, "t": 204, "s": [-117.354, 76.833, 0], "ti": [0.061, 0.832, 0], "to": [-0.066, -0.901, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 205, "s": [-117.546, 74.229, 0], "ti": [0.056, 0.76, 0], "to": [-0.061, -0.832, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 206, "s": [-117.723, 71.84, 0], "ti": [0.051, 0.687, 0], "to": [-0.056, -0.76, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.176}, "t": 207, "s": [-117.883, 69.671, 0], "ti": [0.046, 0.615, 0], "to": [-0.051, -0.687, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 208, "s": [-118.028, 67.721, 0], "ti": [0.041, 0.547, 0], "to": [-0.046, -0.615, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.177}, "t": 209, "s": [-118.157, 65.98, 0], "ti": [0.036, 0.484, 0], "to": [-0.041, -0.547, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 210, "s": [-118.272, 64.437, 0], "ti": [0.032, 0.425, 0], "to": [-0.036, -0.484, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.178}, "t": 211, "s": [-118.373, 63.078, 0], "ti": [0.028, 0.372, 0], "to": [-0.032, -0.425, 0]}, {"i": {"x": 0.833, "y": 0.844}, "o": {"x": 0.167, "y": 0.179}, "t": 212, "s": [-118.462, 61.887, 0], "ti": [0.024, 0.323, 0], "to": [-0.028, -0.372, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.179}, "t": 213, "s": [-118.539, 60.848, 0], "ti": [0.021, 0.28, 0], "to": [-0.024, -0.323, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.18}, "t": 214, "s": [-118.606, 59.947, 0], "ti": [0.018, 0.242, 0], "to": [-0.021, -0.28, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.18}, "t": 215, "s": [-118.664, 59.167, 0], "ti": [0.016, 0.208, 0], "to": [-0.018, -0.242, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.181}, "t": 216, "s": [-118.715, 58.496, 0], "ti": [0.013, 0.178, 0], "to": [-0.016, -0.208, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 217, "s": [-118.758, 57.921, 0], "ti": [0.011, 0.152, 0], "to": [-0.013, -0.178, 0]}, {"i": {"x": 0.833, "y": 0.846}, "o": {"x": 0.167, "y": 0.181}, "t": 218, "s": [-118.794, 57.428, 0], "ti": [0.01, 0.129, 0], "to": [-0.011, -0.152, 0]}, {"i": {"x": 0.833, "y": 0.495}, "o": {"x": 0.167, "y": 0.181}, "t": 219, "s": [-118.826, 57.009, 0], "ti": [0.027, 0.361, 0], "to": [-0.01, -0.129, 0]}, {"i": {"x": 0.833, "y": 0.917}, "o": {"x": 0.167, "y": 0.1}, "t": 220, "s": [-118.853, 56.652, 0], "ti": [0.023, 0.301, 0], "to": [-0.027, -0.361, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [-118.988, 54.844, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [-118.988, 54.844, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [-118.988, 54.844, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [-118.988, 54.844, 0]}, {"t": 225, "s": [-118.988, 54.844, 0], "ti": [0.068, 0.182, 0], "to": [0, 0, 0]}], "x": "var $bm_rt;\nvar eff, amp, freq, decay;\ntry {\n    eff = effect('Elastic Controller');\n    amp = $bm_div($bm_div(eff(1), 10), 100);\n    freq = $bm_div(eff(2), 100);\n    decay = $bm_div(eff(3), 100);\n    function elastic(amp, freq, decay) {\n        ;\n        function calc(n) {\n            ;\n            var t = $bm_sub(time, key(n).time);\n            var v = velocityAtTime(key(n).time - thisComp.frameDuration / 10);\n            return $bm_div($bm_mul($bm_mul(v, $bm_div(amp, freq)), Math.sin(freq * t * 2 * Math.PI)), Math.exp(t * (decay * 2) * Math.E));\n        }\n        ;\n        if ($bm_sum(numKeys, 0)) {\n            return 0;\n        }\n        ;\n        var n = nearestKey(time).index;\n        ;\n        if ($bm_sum(key(n).time, time))\n            n--;\n        return n > 1 && time <= key(n).time + 1 / decay ? calc(n) + calc(n - 1) : 0;\n    }\n    ;\n    $bm_rt = $bm_sum(value, elastic(amp, freq, decay));\n} catch (e) {\n    $bm_rt = value = value;\n}", "a": 1}, "a": {"a": 0, "k": [50, 50, 50]}, "s": {"a": 0, "k": [80, 80, 100]}}, "hasMask": false, "ln": "780", "ip": 163, "op": 1792, "st": -8, "parent": 1}, {"ind": 40, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.925, 52.853, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-0.089, -0.059], [-0.134, 0.052], [0, 0], [0, 0], [-0.4, -0.567], [0, 0], [0.036, -0.109], [-0.039, -0.105], [-0.089, -0.058], [-0.133, 0.052], [0, 0], [0.057, -0.205], [0.128, -0.171], [0.19, -0.127], [0.204, -0.081], [0, 0], [0.045, -0.13], [-0.05, -0.143], [-0.062, -0.071], [-0.101, -0.047], [0, 0], [-0.077, -0.003], [-0.071, 0.028], [-0.044, 0.13], [0.05, 0.143], [0.151, 0.08], [0, 0], [-0.196, 0.17], [-0.128, 0.232], [-0.047, 0.294], [0.092, 0.362], [0, 0], [-0.033, 0.099], [0.038, 0.105], [0.098, 0.056], [0.134, -0.053], [0, 0], [0.19, 0.105], [0.163, 0.012], [0, 0], [-0.163, 0.056], [-0.232, 0.09], [0, 0], [-0.032, 0.099], [0.039, 0.105], [0.098, 0.055], [0.133, -0.052], [0, 0], [0.036, -0.109], [-0.038, -0.105]], "o": [[0.095, 0.058], [0, 0], [0, 0], [0.844, -0.331], [0, 0], [-0.133, 0.053], [-0.033, 0.099], [0.038, 0.105], [0.098, 0.059], [0, 0], [0.054, 0.253], [-0.056, 0.201], [-0.125, 0.161], [-0.188, 0.114], [0, 0], [-0.125, 0.05], [-0.047, 0.123], [0.03, 0.083], [0.059, 0.062], [0, 0], [0.101, 0.053], [0.081, -0.006], [0.14, -0.052], [0.042, -0.13], [-0.054, -0.151], [0, 0], [0.211, -0.108], [0.202, -0.177], [0.137, -0.235], [0.042, -0.3], [0, 0], [0.134, -0.052], [0.038, -0.109], [-0.039, -0.105], [-0.092, -0.062], [0, 0], [-0.154, -0.213], [-0.191, -0.106], [0, 0], [0.036, -0.007], [0.163, -0.056], [0, 0], [0.133, -0.053], [0.039, -0.108], [-0.038, -0.105], [-0.092, -0.062], [0, 0], [-0.134, 0.053], [-0.032, 0.099], [0.036, 0.105]], "v": [[-2.984, -1.892], [-2.639, -1.883], [-2.633, -1.877], [-2.185, -2.053], [-0.317, -1.697], [-2.292, -0.924], [-2.547, -0.682], [-2.541, -0.376], [-2.348, -0.129], [-2.004, -0.119], [0.022, -0.911], [0.019, -0.224], [-0.257, 0.336], [-0.729, 0.766], [-1.317, 1.06], [-1.448, 1.109], [-1.704, 1.379], [-1.698, 1.778], [-1.558, 2.007], [-1.317, 2.171], [2.398, 4.08], [2.668, 4.167], [2.9, 4.114], [3.176, 3.839], [3.165, 3.43], [2.859, 3.084], [-0.281, 1.505], [0.331, 1.085], [0.827, 0.475], [1.1, -0.317], [1.026, -1.311], [1.685, -1.568], [1.935, -1.793], [1.935, -2.115], [1.733, -2.36], [1.397, -2.372], [0.687, -2.097], [0.171, -2.579], [-0.361, -2.759], [-0.379, -2.805], [-0.082, -2.895], [0.512, -3.115], [1.053, -3.325], [1.302, -3.551], [1.302, -3.873], [1.1, -4.117], [0.765, -4.13], [-2.927, -2.688], [-3.183, -2.446], [-3.174, -2.14]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.0141, 0.0141, 0.0141]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "779", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 41, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [42.35, 48.282, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-2.378, 1.798], [0, 0], [0, 0], [-2.778, -2.974], [0.161, 0.132], [1.819, -1.374], [-2.863, -3.89], [-0.295, -0.33], [0.081, 0.073], [0.079, 0.072], [0.328, 0.456]], "o": [[0, 0], [0, 0], [2.095, -1.584], [-0.154, -0.148], [-2.549, -2.135], [-2.378, 1.798], [0.274, 0.365], [-0.079, -0.07], [-0.076, -0.07], [-0.379, -0.413], [-2.865, -3.888]], "v": [[-3.439, -5.652], [-3.426, -5.652], [-3.428, -5.65], [4.993, -3.13], [4.521, -3.548], [-2.758, -4.971], [-1.879, 5.325], [-1.025, 6.367], [-1.265, 6.156], [-1.496, 5.946], [-2.558, 4.642]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.7137, 0.3922, 0.0314]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "778", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 42, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [51.186, 55.208, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0.303, 0.33], [-0.161, -0.144], [0, 0], [-0.34, -0.446], [2.433, -1.798], [2.718, 2.459], [0.154, 0.154], [-0.08, -0.068], [-1.86, 1.376], [2.933, 3.901]], "o": [[0.165, 0.138], [0, 0], [0.39, 0.405], [2.933, 3.9], [-1.976, 1.463], [-0.157, -0.144], [0.08, 0.074], [2.613, 2.139], [2.434, -1.8], [-0.281, -0.365]], "v": [[0.921, -6.367], [1.406, -5.947], [1.404, -5.947], [2.501, -4.672], [3.407, 5.649], [-4.53, 3.805], [-4.996, 3.36], [-4.751, 3.57], [2.701, 4.997], [1.798, -5.324]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.9922, 0.6824, 0.1882]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "777", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 43, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.927, 52.854, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-2.996, -4.016], [2.487, -1.855], [0, 0], [2.996, 4.016], [-2.489, 1.855]], "o": [[2.996, 4.016], [0, 0], [-2.487, 1.855], [-2.996, -4.017], [2.487, -1.855]], "v": [[4.504, -3.36], [5.426, 7.272], [5.426, 7.273], [-4.504, 3.36], [-5.425, -7.273]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.6039, 0.1961]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "776", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 44, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [46.929, 52.271, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-4.358, -5.872], [3.618, -2.713], [0, 0], [4.358, 5.872], [-3.62, 2.713]], "o": [[4.359, 5.872], [0, 0], [-3.618, 2.712], [-4.359, -5.874], [3.618, -2.712]], "v": [[6.552, -4.913], [7.893, 10.633], [7.893, 10.635], [-6.551, 4.913], [-7.892, -10.635]], "c": true}, "a": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.9922, 0.7725, 0.1882]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "775", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 45, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [63.038, 40.34, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[-1.317, 0.097], [-0.002, -0.005], [0, 0], [0, 0], [3.581, -2.624], [0, 0]], "o": [[0.193, 0], [0, 0], [0.75, 0.948], [0, 0], [0, 0], [0, 0]], "v": [[1.73, -2.679], [2.457, -1.792], [2.455, -1.794], [3.526, -0.205], [-1.923, 2.679], [-3.526, 0.115]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [2.244, -1.487]}, "e": {"a": 0, "k": [-0.321, -0.205]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 0.8, 0.478, 0.137, 0.5, 0.896, 0.556, 0.201, 1, 0.991, 0.633, 0.265]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "774", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 46, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [54.882, 62.293, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[2.696, 3.64], [1.625, 7.035], [0, 0], [2.737, -2.74], [0, 0], [0, 0]], "o": [[-9.256, -12.505], [0, 0], [1.282, 2.884], [-1.637, 1.64], [0, 0], [-3.141, -0.598]], "v": [[-7.135, -1.179], [4.691, -5.209], [8.217, -6.811], [7.491, 4.262], [3.257, 6.811], [2.225, 5.431]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [-13.51, -8.12]}, "e": {"a": 0, "k": [10.204, -3.266]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 1, 0.678, 0.396, 0.5, 0.719, 0.458, 0.202, 1, 0.438, 0.238, 0.009]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "773", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 47, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [51.571, 49.229, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"k": {"i": [[0, 0], [-4.144, -5.596], [0, 0], [3.259, -2.859], [0, 0], [0, 0], [2.696, 3.641], [-0.273, 3.181], [0, 0]], "o": [[3.663, -2.155], [0, 0], [4.201, 5.675], [-1.638, 1.639], [0, 0], [-3.141, -0.598], [-2.663, -3.597], [0, 0], [1.516, -1.863]], "v": [[-6.619, -11.509], [7.248, -5.507], [7.246, -5.509], [8.873, 9.72], [4.638, 12.269], [3.607, 10.889], [-5.753, 4.278], [-9.391, -6.507], [-10.924, -8.555]], "c": true}, "a": 0}}, {"ty": "gf", "s": {"a": 0, "k": [-12.128, -2.664]}, "e": {"a": 0, "k": [11.585, 2.189]}, "t": 1, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "g": {"k": {"a": 0, "k": [0, 1, 0.678, 0.396, 0.5, 0.845, 0.524, 0.198, 1, 0.69, 0.369, 0]}, "p": 3}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [119.681, 119.681]}}]}], "hasMask": false, "ln": "772", "ip": 175, "op": 1800, "st": 0, "parent": 39}, {"ind": 48, "ty": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [225, 410.723, 0]}, "a": {"a": 0, "k": [193, 54]}, "s": {"a": 0, "k": [100, 100]}}, "hasMask": false, "ln": "840", "ip": 0, "op": 1800, "st": 0, "refId": "1"}]}
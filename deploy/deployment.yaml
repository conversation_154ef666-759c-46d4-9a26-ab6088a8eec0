apiVersion: apps/v1
kind: Deployment
metadata:
  name: lending-journey-web
  namespace: staging
  labels:
    app: lending-journey-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: lending-journey-web
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  template:
    metadata:
      labels:
        app: lending-journey-web
    spec:
      serviceAccountName: aws-s3-access-sa
      containers:
      - name: lending-journey-web
        image: ************.dkr.ecr.ap-south-1.amazonaws.com/staging/lending-journey-web:latest
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: lending-journey-web-config

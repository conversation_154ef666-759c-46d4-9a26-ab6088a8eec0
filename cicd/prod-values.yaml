deployment_name: lending-journey-web
replicaCount: 1  
namespace: prod
Labels:
  env: prod

configMap:
  data:
    SERVICE_NAME: lending-journey-web
   
secret:
  enabled: false
  names:
    - meta-secret

healthcheck:
  enabled: false
  Path: /health
  # livenessPath: /health
  port: 3000

service:
  type: ClusterIP  
  restPort: 3000  # REST API port
serviceAccount:
  create: true  # Set to true if a dedicated service account is required
  #annotations: eks.amazonaws.com/role-arn: arn:aws:iam::************:role/stashfin-dev-eks-role-policy
  name: 'aws-s3-access-sa'

# # Istio settings
istio:
  enabled: true
  gateway: 'istio-system/httpbin-gateway'
  hosts:
    - 'lending-journey.stashfin.com'

# API Gateway Mapping settings
mapping:
  host: 'lending-journey.stashfin.com'  # Existing host reference
  restPrefix: /
  secretName: eqx-staging  # TLS certificate secret name

# Resource limits and requests (update as needed)
resources: 
  limits:
    cpu: 1
    memory: 1Gi
  requests:
    cpu: 300m
    memory: 256Mi

# Horizontal Pod Autoscaler (HPA) settings
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80  # Scale up if CPU usage exceeds 80%
  targetMemoryUtilizationPercentage: 80  # Uncomment to enable memory-based scaling

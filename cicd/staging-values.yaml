deployment_name: lending-journey-web
replicaCount: 1  
namespace: staging
Labels:
  env: dev

configMap:
  data:
    SERVICE_NAME: lending-journey-web
   
secret:
  enabled: false
  names:
    - meta-secret

healthcheck:
  enabled: false
  Path: /health
  # livenessPath: /health
  port: 3000

service:
  type: ClusterIP  
  restPort: 3000  # REST API port
serviceAccount:
  create: true  # Set to true if a dedicated service account is required
  #annotations: eks.amazonaws.com/role-arn: arn:aws:iam::************:role/stashfin-dev-eks-role-policy
  name: 'aws-s3-access-sa'



# Resource limits and requests (update as needed)
resources: 
  limits:
    cpu: 200m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Horizontal Pod Autoscaler (HPA) settings
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80  # Scale up if CPU usage exceeds 80%
  targetMemoryUtilizationPercentage: 80  # Uncomment to enable memory-based scaling
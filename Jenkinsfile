pipeline {
    agent { label 'jenkins-slave' }
    environment {
        NAMESPACE = 'staging'
        AWS_REGION = 'ap-south-1'
        ECR_REPO = '331916247734.dkr.ecr.ap-south-1.amazonaws.com/staging/lending-journey-web'
        DOCKER_IMAGE = 'lending-journey-web' // Name of your Docker image
        // Get the short commit ID using the sh step
        IMAGE_TAG = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim() 
        GOOGLE_CHAT_WEBHOOK_URL = 'https://chat.googleapis.com/v1/spaces/AAAApDxURgA/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=ayajkdVUtdsxtNjaXpplwDeLDPwJ2iAjra9xE3wxjuU' // Replace with your Google Chat webhook URL
    }
    triggers {
        pollSCM('H/5 * * * *') // Adjust the cron for Polling the repo
    }
    stages {
        stage('Build, Push Docker Image & Deploy') {
            steps {
                script {
                    // Run everything inside DIND container
                    container('dind') {
                        // Build Docker Image
                        sh """
                        docker build -t ${ECR_REPO}:${IMAGE_TAG} .
                        """
                        
                        // Tag the image as 'latest'
                        sh """
                        docker tag ${ECR_REPO}:${IMAGE_TAG} ${ECR_REPO}:latest
                        """
                        
                        // Authenticate to AWS ECR
                        sh """
                        aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REPO}
                        """
                        
                        // Push both the commit tag and 'latest' tag to ECR
                        sh """
                        docker push ${ECR_REPO}:${IMAGE_TAG}
                        docker push ${ECR_REPO}:latest
                        """
                        
                        // Deploy with the latest tag using kubectl
                        sh """
                        kubectl apply -f deploy/ -n ${NAMESPACE}
                        kubectl rollout restart deployment/lending-journey-web -n ${NAMESPACE}
                        """
                    }
                }
            }
        }

    stage('Send Notification to Google Chat') {
        steps {
            script {
                // Construct the message to send
                def message = """
                {
                    "text": "Deployment of ${env.DOCKER_IMAGE}:${env.IMAGE_TAG} to ${env.NAMESPACE} namespace was successful!"
                }
                """

                // Use the container to send the message
                container('dind') {
                    sh """
                    curl -X POST -H 'Content-Type: application/json' \
                    -d '${message}' '${env.GOOGLE_CHAT_WEBHOOK_URL}'
                    """
                }
            }
        }
    }

    }

    post {
        failure {
            script {
                // Construct the failure message
                def failureMessage = """
                {
                    "text": "Deployment of ${env.DOCKER_IMAGE}:${env.IMAGE_TAG} to ${env.NAMESPACE} namespace failed!"
                }
                """

                // Use the container to send the failure message
                container('dind') {
                    try {
                        sh """
                        curl -X POST -H 'Content-Type: application/json' \
                        -d '${failureMessage}' '${env.GOOGLE_CHAT_WEBHOOK_URL}'
                        """
                    } catch (Exception e) {
                        echo "Failed to send failure notification to Google Chat: ${e.message}"
                    }
                }
            }
        }
    }

}
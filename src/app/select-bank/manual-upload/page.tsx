"use client"
import React, { useEffect, useRef, useState } from 'react';
import styles from '../../aa-not-possible/aanotpossible.module.scss'
import Image from 'next/image';
import failedIcon from '../../images/failed-icon.svg'
import successIcon from '../../images/success-upload-icon.svg'
import removeIcon from '../../images/remove-uload.svg'
import uploadpdf from '../../images/uploadpdf.svg'
import BottomPopup from '../../components/popups/BottomPopup';
import { ApiResponse } from "../../store/interface/apiInterface";
import { apiRequest, fetchCommonApi } from '../../utils/api'; 
import { ENDPOINTS } from "../../utils/endpoints";
import PageWrapper from '../../components/PageWrapper';
import PageHeader from '../../components/PageHeader';
import { setCommonData } from '../../store/slices/commonSlice';
import store from '../../store/store';
import LoadingComp from '../../component/loader';
import { pushCleverTapEvent } from '@/app/utils/cleverTapClient';

const ManualUpload = () => {
  const [uploadingImage, setUploadingImage] = useState<boolean>(false);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState<boolean>(false);
  const [fileuploaderror, setFileUploadError] = useState<string | null>("");
  const [fileuploadsuccess, setFileUploadSuccess] = useState<string | null>("");
  const [uploadedImage, setUploadedImage] = useState<string>("");
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [fileerror, setFileError] = useState<string | null>("");
  const [loaded, setLoaded] = useState<boolean>(false)
  const uploadInputRef = useRef<HTMLInputElement>(null);
  const [cteventsfromstorage, setCteventsfromstorage] = useState<any>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const eventsData = JSON.parse(window.localStorage.getItem("eventsdata") || "null");
      setCteventsfromstorage(eventsData);
    }
  }, []);

  useEffect(() => {
    pushCleverTapEvent({
      eventName: "Screen View",
      eventData: {
        "Screen Name": "Bank Statement Manual",
        "Product category": localStorage.getItem("product_code"),
        Segment: cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP",
        Source: cteventsfromstorage?.source,
      }
    })
  }, [cteventsfromstorage]);

  const handleUploadBtn = () => {
    pushCleverTapEvent({
      eventName: "Button Clicked",
      eventData: {
        CTA: "Upload",
        "Page Name": "Bank Statement Manual",
        "Product category": localStorage.getItem("product_code"),
        Segment: cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP",
      }
    })
    setIsUploadModalOpen(true);
  }

  const handleUploadStatement = async () => {
    setFileUploadError('');
    setFileUploadSuccess('');
    setIsUploadModalOpen(false);
    setUploadingImage(true);
    //setLoaded(false)
    try {
      const payload = {
        // customer_id: ********,
        file: uploadedImage,
        fileName: "Bank Statement",
        fileType: "pdf"
      };
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.uploadBankStatement, payload, false, (progress) => setUploadProgress(progress));
      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
        setFileUploadSuccess(response.mainData.message || 'Upload succesfull');
        setLoaded(true);
        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
        setLoaded(false);
      } else {
        if (response.mainData && response.mainData.error_message) {
          setFileUploadError(response.mainData.error_message)
        }
        else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
          setFileUploadError(response.mainData.error_message || '')
        } else if (response.error) {
          setFileUploadError(response.error)
        } else {
          setFileUploadError('Unexpected error occurred.')
        }
      }
    } catch (error) {
      setFileUploadError('Error in uploading bank statement')
      // dispatch(setPageError('Error submitting basic details'))
      //console.error("Error submitting basic details:", error);
      //alert("Failed to save basic details. Please try again.");
    } finally {
      //setLoaded(false)
      //dispatch(closeUploadModal())
      setUploadProgress(0);
      setUploadingImage(false);
    }
  };

  const handleBrowseFilesClick = () => {
    uploadInputRef.current?.click();
  };

  const ALLOWED_FILE_TYPES = ['application/pdf'];
  const MAX_FILE_SIZE = 5 * 1024 * 1024;

  const validateAndUploadFile = (file: File) => {
    setFileError('')
    if (!file) return;
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setFileError('Only support PDF format');
      return;
    }
    if (file.size > MAX_FILE_SIZE) {
      setFileError('File size must be less than 5 MB.');
      return;
    }
    const reader = new FileReader();
    reader.onloadend = () => {
      setUploadedImage(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndUploadFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file) {
      validateAndUploadFile(file);
    }
  };

  return (
    <PageWrapper>
      <PageHeader title="Select Bank" />
      <div style={{padding: "0 16px"}}>
      <div style={{border: "1px solid #e6e6e6", borderRadius: "12px", padding: "18px 12px 12px"}}>
        <div style={{display: "flex"}}>
          <div style={{display: "flex", flexDirection: "column", alignItems: "flex-start"}}>
            <p style={{fontSize: "16px", fontWeight: "600", marginBottom: "12px"}}>Upload Bank Statement</p>
            <span style={{fontSize: "14px"}}>Manually upload a PDF of your latest 6-month statement.</span>
            <div style={{display: "flex", border: "1px solid #000", borderRadius: "66px", padding: "4px 10px", fontSize: "14px", fontWeight: "500", marginTop: "14px"}} onClick={handleUploadBtn}>
              {uploadingImage ?
                'File Uploading...' :
                fileuploaderror ? <span className={styles.failedUpload}><Image src={failedIcon} alt="Failed" /> Upload failed</span> :
                  fileuploadsuccess ? <span className={styles.successUpload}><Image src={successIcon} alt="Success" /> filename <Image src={removeIcon} alt="Success" className={styles.remove} onClick={() => {
                    setUploadedImage("")
                    setFileUploadSuccess('')
                  }} /></span> :
                    'Upload PDF'}
            </div>
            {fileuploaderror ? <div className={styles.retry} onClick={() => {
              setIsUploadModalOpen(true);
              setFileUploadError('')
              setUploadedImage("")
            }}>Try again</div> : null}
          </div>
          <Image src={uploadpdf} alt='' />
        </div>
        {uploadingImage ? <div className={styles.loadingProgress}>
          <div className={styles.progressBar}>
            <div style={{ width: `${uploadProgress}%` }}></div>
          </div>
          {uploadProgress > 0 && <div className={styles.progressPercent}>{uploadProgress}%</div>}
        </div> : null}
      </div>

      <BottomPopup
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        title="Upload"
        footerClass="pt-0"
        buttons={[
          { label: 'Upload & Save', onClick: handleUploadStatement, className: uploadedImage ? '' : 'disabled' }
        ]}
      >
        <div>
          <p style={{ textAlign: 'left' }}>Max Size: 5 MB</p>
          <p style={{ textAlign: 'left', marginBottom: '15px' }}>Supported Format: .PDF</p>
          <div className="upload-btn">
            <button
              className="btn btn-primary-outline"
              onClick={handleBrowseFilesClick}
              type="button"
            >
              Browse files
            </button>
            {/* Hidden file input to handle file selection */}
            <input
              type="file"
              ref={uploadInputRef}
              style={{ display: 'none' }}
              accept="application/pdf,image/jpeg,image/png"
              onChange={handleFileInputChange}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            />
          </div>
          {fileerror ? <div className="error">{fileerror}</div> : null}
        </div>
      </BottomPopup>
    </div>
        {loaded ? <LoadingComp /> : null}
    </PageWrapper>
    
    
  )
}

export default ManualUpload
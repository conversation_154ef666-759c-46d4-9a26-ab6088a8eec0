'use client';

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import BottomPopup from '../components/popups/BottomPopup';
import styles from './bank.module.scss'
import { closeModal } from '../store/features/bankSlice';
import { apiRequest, fetchCommonApi } from '../utils/api'; // Common API call file
import store, { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { setPageError } from '../register/register.slice';
import { ENDPOINTS } from '../utils/endpoints';
import CtEvents from '../utils/Ctevents';
import PageWrapper from '../components/PageWrapper';
import LoadingComp from '../component/loader';
import { useRouter } from "next/navigation";
import { ApiResponse } from '../store/interface/apiInterface';
import BankImage from "@/app/components/BankImage";
import { setCommonData } from '../store/slices/commonSlice';


const BankDetailsForm = () => {
    const router = useRouter()
    const dispatch = useDispatch();
    const [popularBanks, setPopularBanks] = useState<any[]>([]);
    const [otherBanks, setOtherBanks] = useState<any[]>([]);
    const [selectedBank, setSelectedBank] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [selectedOccupation, setSelectedOccupation] = useState<string>('');
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    const [loaded, setLoaded] = useState<boolean>(false)
    const [isSentinalUser, setIsSentinalUser] = useState<boolean>(false);
    const [productcode, setproductcode] = useState(false)

    interface SaveBasicDetailsResponse {
        monitoring_reference_id: any;
        underwriting_reference_id: any;
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
        redirection_url: string
    }
    const {
        accountNumber,
        isModalOpen,
        bankDetails,
    } = useSelector((state: any) => state.bank);

    const handleConfirm = async () => {
        try {
        } catch (error) {
        }
    };
    useEffect(() => {
        getbankList()
    }, [])
    const getbankList = async () => {
        try {

            const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.banklist + '?is_aa_enabled=true');

            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                // setBankList(response.mainData.data);

                setPopularBanks(response.mainData.data.filter((bank: any) => bank.is_popular));
                setOtherBanks(response.mainData.data.filter((bank: any) => !bank.is_popular));
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                } else if (response.error) {
                } else {
                }
            }
        } catch (error) {
        }
    }
    const filteredBanks = otherBanks.filter((bank) =>
        bank.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    const handleBankClick = async (bankId: string) => {
        setLoaded(false)

        setSelectedBank(bankId);
        try {
            const payload = {
                // customer_id: ********,
                bank_id: bankId
            };
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.aasupport, payload);

            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                initiateaa(bankId)
                // setLoaded(true)

            } else {
                setLoaded(true)

                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                } else if (response.error) {
                } else {
                }
            }
        } catch (error) {
            // dispatch(setPageError('Error submitting basic details'))
            //console.error("Error submitting basic details:", error);
            //alert("Failed to save basic details. Please try again.");
        } finally {
            setTimeout(() => {
                setLoaded(true)
            }, 2000);
        }
    };
    const initiateaa = async (bankId: string) => {
        setLoaded(false)
        try {
            const payload = {
                "journey_type": "web",
                "bank_ids": [
                    process.env.NODE_ENV === "development" ? 'FIP-ID' : bankId
                ],
                // "customer_id": ********
            }
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.aa_initiate, payload);

            if (response.mainData && response.mainData.redirection_url) {
                // debugger;
                const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                const event_name = 'Screen View'
                const productcode = localStorage.getItem("product_code")

                const event_property = { "Screen Name": "Account Aggregator", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source }
                setCtdata({ event_name, event_property })
                localStorage.setItem('monitoring_reference_id', response.mainData.monitoring_reference_id)
                localStorage.setItem('underwriting_reference_id', response.mainData.underwriting_reference_id)
                localStorage.setItem('ignosisurl', response.mainData.redirection_url)
                localStorage.setItem('currenturl', location.host)
                router.push('/consent')
                // window.location.assign(response.mainData.redirection_url)
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                // debugger;
                else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                    dispatch(setPageError(response.mainData.error_message || ''))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))

                }
            }
        } catch (error) {
            // dispatch(setPageError('Error submitting basic details'))
            //console.error("Error submitting basic details:", error);
            //alert("Failed to save basic details. Please try again.");
        } finally {
            setTimeout(() => {
                setLoaded(true)
            }, 2000);
        }
    };
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "Bank Statement fetch", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })

    }, [])
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    useEffect(() => {
        const getPersoalDetails = async () => {
            setLoaded(false)
            try {
                const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.personalDetail);
                if (response.mainData && response.mainData?.data) {
                    setSelectedOccupation(response.mainData.data.occupation || "");
                    setIsSentinalUser(response.mainData.data.is_sentinel_user);
                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }

                    else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                    } else if (response.error) {
                        dispatch(setPageError(response.error))

                    } else {
                    }
                }
            } catch (error) {
            } finally {
                setTimeout(() => {
                    setLoaded(true)
                }, 2000);
            }
        };
        getPersoalDetails()
        const geproductcode = localStorage.getItem('product_code')

        if (geproductcode == "Sentinel") {
            setproductcode(true)
        }
    }, [])

    const createNextStep = async () => {
        router.push('/select-bank/manual-upload');
    };

    return (
        <PageWrapper>
            {loaded ?

                <div className={`external-wrapper bank-details-page`}>
                    {/* {!productcode ? */}
                    <PageHeader title={`Select your ${(selectedOccupation === 'SALARIED' || isSentinalUser) ? 'salary' : 'primary'} bank`} para="We can offer you the best possible loan terms by analysing your bank statements" />

                    {/* } */}
                    {!isSentinalUser && (
                        <div className={styles.notebox}>
                            <div className={styles.title}>
                                Important Note:
                            </div>
                            <div className={styles.subtitle}>Please ensure you use the same bank account in your UPI app for verification.</div>
                        </div>
                    )}

                    <div className="page-content" style={{ position: "relative", paddingBottom: 100 }}>
                        <ul className={styles.bankList}>
                            {popularBanks.map((bank) => (
                                <li
                                    key={bank.id}
                                    className={selectedBank === bank.id ? styles.selected : ''}
                                    onClick={() => handleBankClick(bank.id)}
                                >
                                    <BankImage bankName={bank.name} ratioHeight={48} />
                                    <p>{bank.name}</p>
                                </li>
                            ))}

                        </ul>
                        <div className={styles.otherBanks}>
                            <h3>Other Banks</h3>
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    name="otherBank"
                                    autoComplete="off"
                                    placeholder=" "
                                    className={`form-control ${styles.search}`}
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                                <label>Search by bank name</label>
                            </div>
                            <ul className={styles.otherBanksList}>
                                {filteredBanks.map((bank) => (
                                    <li
                                        key={bank.id}
                                        className={selectedBank === bank.id ? styles.selected : ''}
                                        onClick={() => handleBankClick(bank.id)}
                                    >

                                        <BankImage bankName={bank.name} ratioHeight={32} />
                                        <span>{bank.name}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                        {isSentinalUser && (
                            <div style={{ position: "fixed", width: "100%", bottom: 0, left: 0, padding: "16px", backgroundColor: "white", boxShadow: "0px -1px 0px white" }}>
                                <button className={`btn btn-primary mb-0`} onClick={createNextStep}>
                                    Manual Upload
                                </button>
                            </div>
                        )}
                    </div>
                    <BottomPopup
                        isOpen={isModalOpen}
                        onClose={() => dispatch(closeModal())}
                        title="Bank Details"
                        buttons={[
                            { label: 'Confirm Details', onClick: handleConfirm },
                            { label: 'Cancel', onClick: () => dispatch(closeModal()) }
                        ]}
                    >
                        {bankDetails && (
                            <ul className="bank-details">
                                <li><span>Bank Name:</span><span>{bankDetails.BANK}</span></li>
                                <li><span>Account Number:</span><span>{accountNumber}</span></li>
                                <li><span>IFSC Code:</span><span>{bankDetails.IFSC}</span></li>
                                <li className="address"><span>Branch Address:</span><span>{bankDetails.ADDRESS}</span></li>
                            </ul>
                        )}
                    </BottomPopup>
                    <CtEvents data={ctData} />

                </div>
                :
                <LoadingComp />
            }
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
};

export default BankDetailsForm;
@import '../scss/variable.scss';

.bankList {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 0;
    // justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 25px;

    li {
        width: 25%;
        text-align: center;
        font-size: 12px;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        padding: 15px;
        padding-top: 0;

        img {
            border: 1px solid transparent;
            border-radius: 50%;
        }

        p {
            margin-top: 5px;
            line-height: 1.2;
        }

        &.selected {
            color: var(--black);

            img {
                border: 1px solid var(--black);
            }
        }
    }
}

.otherBanks {
    h3 {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .search {
        height: 42px;
        background-image: url('../images/search.svg');
        background-repeat: no-repeat;
        background-position: left 10px top 10px;
        padding-left: 35px;

        +label:not(.noabs) {
            top: 20px;
            left: 11px;
        }

        &:focus,
        &:not(:placeholder-shown) {
            +label {
                top: 0;
            }
        }
    }
}

.otherBanksList {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 186px;
    overflow: auto;

    &::-webkit-scrollbar {
        width: 7px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
        background: #888;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    li {
        font-size: 14px;
        padding: 10px 0;
        font-weight: 600;
        position: relative;
        background-image: url('../images/chevron-right.svg');
        background-repeat: no-repeat;
        background-position: right 10px center;
        cursor: pointer;

        img {
            margin-right: 10px;
        }

        &.selected {
            color: var(--black);

        }

        &:not(:last-of-type) {
            &::after {
                content: '';
                position: absolute;
                background-color: $color2;
                height: 1px;
                width: calc(100% - 54px);
                right: 10px;
                bottom: 0;
            }
        }
    }
}
.notebox {
    width: calc(100%  - 32px);
    margin-left: 16px;
    background: #FFF8E0;
    border-radius: 12px;
    padding: 11px 16.5px;
    margin-top: 8px;
    margin-bottom: 20px;

    .title {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        color: var(--black);
    }

    .subtitle {
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        margin-top: 5px;
        color: var(--black);
    }
}
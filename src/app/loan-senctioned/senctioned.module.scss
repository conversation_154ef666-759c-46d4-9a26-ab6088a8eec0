@import '../scss/variable.scss';

.externalWrapper {
    h1 {
        font-size: 20px !important;
    }

    header {
        padding-bottom: 20px;
        margin-bottom: 0;
    }
}

.pageContent {
    min-height: calc(100vh - 360px);
}

.notEligibleContent {
    text-align: left;
    margin-top: 40px;
}

.notEligibleCard {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 12px;
    margin-bottom: 20px;

    p {
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: 600;
    }
}

.steps {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    text-align: left;
    font-size: 12px;
    font-weight: 400;

    .step {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;

        &:not(:last-of-type) {
            margin-bottom: 15px;
        }

        p {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 0;
        }

        img {
            margin-right: 15px;
        }
    }
}

.cta {
    background-color: #D5FAE6;
    padding: 20px 16px;
}
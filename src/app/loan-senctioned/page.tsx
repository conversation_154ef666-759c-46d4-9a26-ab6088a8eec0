'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import SuccessIcon from '../images/success-icon.svg'
import calendarIcon from '../images/calendar-icon.svg'
import fileIcon from '../images/file-icon.svg'
import styles from './senctioned.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import { apiRequest } from "../utils/api"
import { ENDPOINTS } from "../utils/endpoints"
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from "../store/store"
import { setPageError, setPageSuccess } from '../register/register.slice';
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { navigateToPage } from '../utils/bridge';
import CtEvents from '../utils/Ctevents';
function LoanSenctioned() {
    const dispatch = useDispatch();
    const [checked, setchecked] = useState(false)
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        landing_page: string,
        profile_pic_upload_url: string,
        loanStatus: any
    }
    const CheckLoanStatus = async () => {
        try {

            const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.cb_loan_status);
            debugger;
            if (response.mainData && response.mainData.loanStatus == true) {
                debugger;


            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else {
                    dispatch(setPageError(response.mainData?.error_message as string | "null"))
                }
            }
        } catch (error) {
        }
    }
    useEffect(() => {
        CheckLoanStatus()
    }, [])
    const returnBackToApp = () => {
        // debugger;
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_loan_sanctioned", "Product category": 'cb', "CTA": "Go to Homepage", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
        window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
    };
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_loan_sanctioned", "Product category": 'cb', "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    return (
        <div className={`external-wrapper ${styles.externalWrapper}`}>
            <PageHeader title='Loan Sanctioned' para="You will get the first part of Credit Builder Loan transferred in your bank account within 48 hours" centeredHeader={true} call={false} nobg={true} bgcolor="#D5FAE6" icon={<Image src={SuccessIcon} alt='Header' />} />
            <div className={styles.cta}>
                <button type="button" className="btn btn-primary" onClick={() => {
                    navigateToPage({
                        type: "page",
                        landingPage: "pcr_launch"
                    });
                    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                    const event_name = 'Button Clicked'
                    const productcode = localStorage.getItem("product_code")

                    const event_property = { "Screen Name": "cb_loan_sanctioned", "Product category": 'cb', "CTA": "Get Credit Report", "Source": cteventsfromstorage?.source }
                    setCtdata({ event_name, event_property })
                }}>Get Credit Report</button>
            </div>
            <div className={`page-content ${styles.pageContent}`}>
                <div className={styles.notEligibleContent}>
                    <div className={styles.notEligibleCard}>
                        <p>Ready to improve your CIBIL Score?</p>
                        <div className={styles.steps}>
                            <div className={styles.step}>
                                <Image src={calendarIcon} alt="Loan & card approvals" width={32} height={32} />
                                <p><strong>Pay 2nd advance EMI</strong> within 30 days and get your money back same day</p>
                            </div>
                            <div className={styles.step}>
                                <Image src={fileIcon} alt="Low interest rates" width={32} height={32} />
                                <p>Check your score after 30 days on timely loan closure</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="bottom-footer p-0">
                    <button type="button" className="btn btn-primary-outline" onClick={() => returnBackToApp()}>Go to Homepage</button>
                    {/* <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p> */}
                </div>
            </div>
            <CtEvents data={ctData} />
        </div>
    );
}

export default LoanSenctioned;

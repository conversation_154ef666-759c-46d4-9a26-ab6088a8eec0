@import '../scss/variable.scss';

.externalWrapper{
    h1{
        font-size: 20px !important;
    }
    header{
        padding-bottom: 20px;
        margin-bottom: 0;
    }
}
.notEligibleContent{
    text-align: left;
    margin-top: 40px;
}
.notEligibleCard{
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 12px;
    margin-bottom: 20px;
    img{
        margin-bottom: 18px;
    }
    h3{
        margin-bottom: 27px;
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
    }
    p{
        font-size: 12px;
        margin-bottom: 15px;
        font-weight: 600;
    }
}

.note{
    background-color: $light-red;
    text-align: left;
    padding: 15px;
    border-radius: 12px;
    color: $color-black;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    margin-bottom: 20px;
    .notesIcon{
        margin-right: 10px;
        min-width: 36px;
        min-height: 36px;
    }
    p{
        font-size: 12px;
        line-height: 18px;
        font-weight: 500;
        margin-bottom: 0;
    }
    h4{
        font-size: 14px;
        font-weight: 700;
        margin-bottom: 5px;
    }
}
.steps{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    text-align: left;
    font-size: 12px;
    font-weight: 500;
    max-width: 310px;
    margin: 0 auto;
    .step{
        max-width: 90px;
        p{
            font-size: 12px;
        }
        img{
            margin-bottom: 7px;
        }
    }
}
'use client'
import React from 'react';
import '../scss/button.scss';
import placeholderImg from '../images/placeholder.svg'
import HeaderIcon from '../images/register-icon-black.svg'
import cardAprvIcon from '../images/card-approval.svg'
import LowIntrstIcon from '../images/low-interest.svg'
import HigherLmtIcon from '../images/higher-limit.svg'
import BulbIcon from '../images/bulb-icon.svg'
import styles from './noteligible.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
function NotEligible() {
    return (
        <div className={`external-wrapper ${styles.externalWrapper}`}>
            <PageHeader title='Looks like you are currently not eligible for a Stashfin loan' call={false} nobg={true} bgcolor="#FFE7E7" icon={<Image src={HeaderIcon} alt='Header' />} />
            <div className='page-content'>
                <div className={styles.notEligibleContent}>
                    <div className={styles.notEligibleCard}>
                        <Image src={placeholderImg} alt="Congratulations" width={76} height={76} />
                        <h3>Don't worry Sign up for Credit Builder & increase your chance for getting a loan in future!</h3>
                        <p>High CIBIL score benefits</p>
                        <div className={styles.steps}>
                            <div className={styles.step}>
                                <Image src={cardAprvIcon} alt="Loan & card approvals" width={40} height={40} />
                                <p>Loan & card approvals</p>
                            </div>
                            <div className={styles.step}>
                                <Image src={LowIntrstIcon} alt="Low interest rates" width={40} height={40} />
                                <p>Low interest rates</p>
                            </div>
                            <div className={styles.step}>
                                <Image src={HigherLmtIcon} alt="Higher limit" width={40} height={40} />
                                <p>Higher limit</p>
                            </div>
                        </div>
                    </div>
                    <div className={styles.note}>
                        <div className={styles.notesIcon}>
                            <Image src={BulbIcon} alt='Bulb' />
                        </div>
                        <div className={styles.notesContent}>
                            <h4>Did you know?</h4>
                            <p>Timely repayment of loans can help in building your Credit Score!</p>
                        </div>
                    </div>
                </div>
                <div className="bottom-footer p-0">
                    <button type="button" className="btn btn-primary">Improve Credit Score</button>
                    <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
                </div>
            </div>
        </div>
    );
}

export default NotEligible;

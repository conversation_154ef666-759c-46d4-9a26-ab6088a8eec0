.sliderCheckbox {
    display: flex;
    align-items: center;
    gap: 10px;
  
    .label {
      font-size: 14px;
      color: #333;
    }
  
    .slider {
      position: relative;
      width: 50px;
      height: 25px;
      background-color: #ccc;
      border-radius: 25px;
      cursor: pointer;
      transition: background-color 0.3s ease;
  
      
  
      .sliderThumb {
        position: absolute;
        top: 1px;
        left: 2px;
        width: 21px;
        height: 22px;
        background: url(../images/checkslider.png) no-repeat;
        background-size: 100%;
        transition: left 0.3s ease;
      }
      &.checked {
        background-color: var(--black);
        .sliderThumb {
            left: calc(100% - 23px);
        }
      }
    }
  }
  
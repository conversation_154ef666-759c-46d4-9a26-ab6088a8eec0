.backIcon {
    margin-bottom: 18px;
    cursor: pointer;

    &:nth-of-type(2) {
        margin-left: auto;
    }
}

.header {
    position: relative;
    padding: 0 16px;
    margin-bottom: 25px;

    .headerIcons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 40px;
    }

    .headerContentWrapper {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
    }

    .centeredHeaderContentWrapper {
        display: flex;
        align-items: center;
        flex-direction: column;
        text-align: center;

        .headerContent {
            max-width: 300px;
        }

        .icon {
            margin-bottom: 25px;
        }

        p {
            margin-top: 15px;
            font-weight: 500;
        }
    }

    h1 {
        font-size: 24px;
        font-weight: 700;
        width: 240px;
    }

    p {
        font-size: 14px;
        margin-bottom: 0;
        margin-top: 10px;
        font-weight: 500;
    }

    .fullWidth {
        width: 100%;
    }

    .congratulation_text{
        font-size: 24px;
        font-weight: 700;
        color: #2DB166;
    }
}
.samebg {
    background-color: transparent !important;
}


.back_gating_container{
    .call_container{
        background-color: #ededed;
        padding: 4px 16px 8px 16px;
        margin-top: 24px;
        border-radius: 18px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .steps_text{
        font-size: 14px;
        font-weight: 500;
        max-width: 270px;
        margin: 20px auto 18px auto;
    }

    .number_of_people{
        font-size: 14px;
        font-weight: 500;
        margin-top: 6px;
    }

    .btn_container{
        max-width: 300px;
        margin: 0 auto;
        margin-top: 14px;
        .exit_btn{
            background: none;
            font-weight: 600;
            border: none;
            margin-top: 12px;
        }
    }
}

.increase_limit_modal_container{
    .amount_not_enough{
        font-size: 20px !important;
        font-weight: 600;
        color: #7b7b7b;
        margin: 14px 0;
    }
    .want_higher_limit{
        font-size: 24px !important;
        font-weight: 600;
        margin-bottom: 28px;
    }
    .number_of_people{
        font-size: 14px;
        font-weight: 500;
        margin: 10px 0 30px 0;
    }
    .exit_btn{
        background: none;
        font-weight: 600;
        border: none;
        margin-top: 12px;
    }
}

.shimmer {
  width: 100%;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 37%,
    #f0f0f0 63%
  );
  background-size: 400% 100%;
  animation: shimmer 1.2s ease-in-out infinite;
  margin: 16px auto;
}

@keyframes shimmer {
  0% {
    background-position: -400px 0;
  }
  100% {
    background-position: 400px 0;
  }
}

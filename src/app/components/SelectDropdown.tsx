'use client';
import React, { useEffect, useRef, useState } from "react";

type SelectDropdownProps = {
  options: Array<string | { name: string }>; // Options can be strings or objects with a `name` field.
  value: string; // Current selected value.
  onChange: (option: string | { name: string }) => void; // Callback when an option is selected.
  labelText: string; // Label for the dropdown.
  className?: string; // Optional className for custom styling.
  [key: string]: any; // Allow any additional props.
};

const SelectDropdown: React.FC<SelectDropdownProps> = ({
  options,
  value,
  onChange,
  labelText,
  className = '',
  ...rest
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasValue, setHasValue] = useState(false);
  const dropDown = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (value !== '') {
      setHasValue(true);
    } else {
      setHasValue(false);
    }

    const onClickOutside = (event: MouseEvent) => {
      if (dropDown.current && !dropDown.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", onClickOutside);
    return () => {
      document.removeEventListener("mousedown", onClickOutside);
    };
  }, [value]);

  const handleOptionClick = (option: string | { name: string }) => {
    onChange(option);
    setIsOpen(false);
    setHasValue(true);
  };

  return (
    <div
      className={`custom-select ${className} ${isOpen ? 'open-select' : ''} ${hasValue ? 'selected' : ''}`}
      ref={dropDown}
    >
      <div className="select-header form-control" onClick={() => setIsOpen(!isOpen)}>
        <label className="form-label">{labelText}</label>
        {value && <div className="value">{value}</div>}
      </div>
      {isOpen && (
        <ul className="select-options">
          {options.map((option, key) => (
            <li
              key={key}
              onClick={() => handleOptionClick(option)}
            >
              {typeof option === 'string' ? option : option?.name}
              <input
                type="radio"
                className="form-check-input"
                readOnly
                checked={typeof option === 'string' ? value === option : value === option?.name}
              />
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SelectDropdown;

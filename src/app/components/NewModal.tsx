import React, { useEffect, useMemo, useState } from 'react'
import styles from "./header.module.scss";
import paiseKaBag from "../images/paiseKaBag.png";
import bluePhone from "../images/bluePhone.png";
import Image from 'next/image';
import GroupOfPeopleImg from "../images/group_of_people.png";
import { formatCurrency } from '../store/middleware/currencyFormatter';
import { apiRequest } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';
import { getRandomIntInRange } from '../utils/helperFunctions';

interface NewModalProps {
    isHeadPhone: boolean;
    onContinueClick: () => void;
    onExitClick: () => void;
    handleCustomerSupport: () => void;
    amount?: number;
};

interface StepProps {
    step_name: string;
    step_order: number;
    step_status: string;
}

const textArr = [
    "5,000+ people like you got funded yesterday",
    "Thousands completed their loan in minutes this week",
    "You’re almost there—join others who finished today",
    "More than 7,000 approved loans in the last 24 hours",
    "Most users complete this step and get instant approval",
];

const NewModal = ({isHeadPhone, onContinueClick, onExitClick, handleCustomerSupport, amount=0}: NewModalProps) => {
    const [remainingStep, setRemainingStep] = useState<number>(1)
    const [loading, setLoading] = useState<boolean>(true);

    const fetchSteps = async() => {
        try {
            setLoading(true);
            const resp = await apiRequest<ApiResponse>("GET", ENDPOINTS.keepGoing);
            if(resp.mainData?.status){
                const {data} = resp.mainData;
                const remainingSteps: [] = data?.filter((item: StepProps) => item.step_status !== "SUCCESS")
                setRemainingStep(remainingSteps.length);
            }
        } catch (error) {
            console.log("error in fetching steps", error) 
        } finally {
            setLoading(false);
        }
    }
    
    useEffect(() => {
        fetchSteps();
    },[]);
    
    const remainingStepText = useMemo(() => {
        if(remainingStep >= 3) return `Just ${remainingStep} steps away from funds in your bank account`;
        if(remainingStep === 2) return 'Only 2 steps left—almost done!';
        if(remainingStep === 1) return 'One last step away from funds in your bank account';
    }, [remainingStep]);

    const staticText = useMemo(() => textArr[getRandomIntInRange(0, 4)], [])

  return (
    <div className={styles.back_gating_container}>
        {isHeadPhone && (
            <div className={styles.call_container}>
                <div style={{display: "flex", alignItems: "center", columnGap: 20}}>
                    <Image src={paiseKaBag} alt='money bag' width={62} />
                    <p style={{textAlign: "left", fontWeight: 600, color: "#0064E0", marginTop: 8}}>Need Loan Help <br/> Call us</p>
                </div>
                <Image onClick={handleCustomerSupport} src={bluePhone} alt='phone icon' width={24} style={{marginTop: 8}} />
            </div>
        )}

        {loading ? (
            <div className={styles.shimmer} style={{width: '70%', height: 24}} />
        ) : (
            <p className={styles.steps_text}>{remainingStepText}</p>
        )}
        <Image src={GroupOfPeopleImg} alt='people' />
        <p className={styles.number_of_people}>{staticText}</p>

        <div className={styles.btn_container}>
            <button className='btn btn-primary' onClick={onContinueClick}>{amount ? `Transfer ₹${formatCurrency(amount)} Now!` : "Continue Application"}</button>
            <button className={styles.exit_btn} onClick={onExitClick}>Exit Anyway</button>
        </div>
    </div>
  )
}

export default NewModal
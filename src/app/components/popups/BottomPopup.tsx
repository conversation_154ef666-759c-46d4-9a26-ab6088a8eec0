'use client';

import React, { useEffect, useState } from 'react';
import '../../scss/modal.scss';
import CloseIcon from '../../images/cross-icon.svg';
import Image from 'next/image';

interface BottomPopupProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  titleIcon?: React.ReactNode;
  buttons?: { label: string; onClick: () => void; className?: string; disabled?: boolean }[];
  className?: string;
  footerClass?: string;
}

const BottomPopup: React.FC<BottomPopupProps> = ({
  children,
  isOpen,
  onClose,
  title,
  titleIcon,
  buttons,
  className = '',
  footerClass = ''
}) => {
  if (!isOpen) return null;
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const isMobile = /Android/i.test(navigator.userAgent);
    if (!isMobile) return;

    const handleFocusIn = (event: FocusEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        // setKeyboardHeight(250); // Adjust based on testing

      }
    };

    const handleFocusOut = () => {
      setTimeout(() => {
        setKeyboardHeight(0);
      }, 1000);
    };

    window.addEventListener("focusin", handleFocusIn);
    window.addEventListener("focusout", handleFocusOut);

    return () => {
      window.removeEventListener("focusin", handleFocusIn);
      window.removeEventListener("focusout", handleFocusOut);
    };
  }, []);

  return (
    <div className={`modal-overlay bottom-modal ${className}`}>
      <div className="modal-content" style={{ bottom: keyboardHeight, transition: "margin-bottom 0.3s ease" }}>
        <span className="modal-close" onClick={onClose}>
          <Image src={CloseIcon} alt="close" width={24} height={24} />
        </span>
        {title && <div className="modal-header">{titleIcon ? <span className='title-icon'>{titleIcon}</span> : null}{title}</div>}
        <div className="modal-body">{children}</div>
        {buttons && (
          <div className={`modal-footer ${footerClass}`}>
            {buttons.map((button, index) => (
              <button
                key={index}
                className={`btn btn-primary ${button.className || ''} ${button.disabled ? 'disabled' : ''}`}
                onClick={button.onClick}
                disabled={button.disabled}
              >
                {button.label}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BottomPopup;

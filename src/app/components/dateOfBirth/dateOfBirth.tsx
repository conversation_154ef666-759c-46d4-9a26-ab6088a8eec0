"use client";

import React from "react";
import dynamic from "next/dynamic";

const BottomPopup = dynamic(() => import("../popups/BottomPopup"), {
  ssr: false,
});

interface DOBPopupProps {
  isOpen: boolean;
  dob: string;
  onChange: (value: string) => void;
  onClose: () => void;
  onConfirm: () => void;
}

const DOBPopup: React.FC<DOBPopupProps> = ({
  isOpen,
  dob,
  onChange,
  onClose,
  onConfirm,
}) => {
  return (
    <BottomPopup
      isOpen={isOpen}
      onClose={onClose}
      title="Enter your Date of Birth"
      className="hintText"
      buttons={[
        {
          label: "Confirm",
          onClick: onConfirm,
          className: "btn-primary",
          disabled: !dob, // disable if input is empty
        },
      ]}
    >
      <div className="input-wrapper">
        <input
          type="text"
          inputMode="numeric"
          pattern="\d{2}/\d{2}/\d{4}"
          placeholder=" "
          className="form-control"
          value={dob}
          onChange={(e) => onChange(e.target.value)}
        />
        <label>DOB (DD/MM/YYYY)</label>
        <p className="hintText">Please check this carefully. It's important</p>
      </div>
    </BottomPopup>
  );
};

export default DOBPopup;

// @ts-nocheck
"use client";
import Image from "next/image"
import backIcon from '../images/back.svg'
import callIcon from '../images/call-icon.svg'
import infoIcon from '../images/info.svg'
import styles from './header.module.scss'
import React, { useEffect, useState, useRef, useCallback } from "react";
import BottomPopup from "./popups/BottomPopup";
import { apiRequest } from "../utils/api";
import { ApiResponse } from "../store/interface/apiInterface";
import { ENDPOINTS } from "../utils/endpoints";
import CtEvents from "../utils/Ctevents";
import { navigateToPage } from "../utils/bridge";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import LoadingComp from "../component/loader"
import { useSearchParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux"
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { setPageError } from '../register/register.slice';
import crossheader from '../images/crossheader.svg'
import OldModal from "./OldModal";
import NewModal from "./NewModal";
import IncreaseLimitModal from "./IncreaseLimitModal";
import { pushCleverTapEvent } from "../utils/cleverTapClient";



interface headerProps { back?: boolean; call?: boolean; title: string; para?: string, icon?: React.ReactNode, nobg?: boolean, bgcolor?: string; centeredHeader?: boolean, highlightText?: string, gating?: boolean, frompage?: string, cross?: boolean, congrats?: boolean, increaseLimit?: boolean, amount?: number, showOld?: boolean; }

function PageHeader({ back = true, call = true, title, para = "", icon, nobg, bgcolor, centeredHeader, highlightText, gating, frompage, cross, congrats, increaseLimit, amount, showOld=false }: Readonly<headerProps>) {
  const dispatch = useDispatch();

  const router = useRouter();
  const pathname = usePathname();
  const isFirstRun = useRef(true);
  const searchParams = useSearchParams();
  const authToken = searchParams.get("auth_token");
  const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);


  // const highlightText = "₹299 + GST";
  let parts: string[] = [para];
  if (highlightText && para.includes(highlightText)) {
    parts = para.split(highlightText);
  }

  const [isBackModalOpen, setIsBackModalOpen] = useState(false)
  const [isHeadPhone, setIsHeadphone] = useState(false)
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
  const [isExit, setisExit] = useState(false)
  const previousPathRef = useRef<string | null>(null);
  const [cteventsfromstorage, setCteventsfromstorage] = useState<any>(null);


  const returnBackToApp = () => {
    // debugger;
    const event_name = 'Exit_Popup'
    const event_property = { "Page Name": pathname, "Product category": cteventsfromstorage.productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP", "CTA": "Yes" }
    pushCleverTapEvent({eventName: event_name, eventData: event_property})
    setisExit(true)
    if (frompage == "rpdpolling") {
      rpdpolling()
    }
    else {
      setTimeout(() => {
        window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
      }, 600);
    }
  };
  const rpdpolling = async () => {
    try {
      const endpoint = `${ENDPOINTS.rpdpolling}?status=cancel`;
      const response = await apiRequest<SaveBasicDetailsResponse>("GET", endpoint);

      const status = response?.mainData?.status;
      const errorMsg = response?.mainData?.error_message;
      if (status === 'failed') {
        window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
      }
      else {
        debugger
        dispatch(setPageError(errorMsg))
        setisExit(false)
        setIsBackModalOpen(false)
      }
    } catch (error) {
      // Continue polling unless final timeout
    }
  };
  const handleCustomerSupport = async () => {
    const event_name = 'Button Clicked'

    const event_property = { "CTA": "Request a Callback", "Action": "Need help", "message": "Your Request has been received. Our team will call you soon", "Product category": cteventsfromstorage.productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Page shown": `/${location.href.split('/').at(-1)}` }
    setCtdata({ event_name, event_property })
    // const authToken = localStorage.getItem("auth_token")
    // const deviceToken = localStorage.getItem("device_id")
    // window.location.assign(`https://www.stashfin.com/detail?tab=customer-support&auth_token=${authToken}&device_id=${deviceToken}`)
    debugger;
    navigateToPage({
      type: "bottom-sheet",
      landingPage: "help"
    })

  }
  useEffect(() => {
    // Load events data from localStorage (client-side only)
    const eventsData = JSON.parse(localStorage.getItem('eventsdata') || 'null');
    setCteventsfromstorage(eventsData);

    const handleShowHeadphone = async () => {
      try {
        const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.csmIcon);
        if (response.mainData && response.mainData.status) {
          setIsHeadphone(true)
        } else {
          setIsHeadphone(false)
        }
      } catch (error) {
      }
    }
    handleShowHeadphone()
  }, [])
  useEffect(() => {
    if (authToken) {
      sessionStorage.setItem('prevPath', window.location.pathname)
    }
  }, [authToken]);

  const handlebackclick = () => {
    const event_name = 'Back_Button_Clicked'
    const event_property = { "Page Name": pathname, "Product category": cteventsfromstorage.productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP" }
    pushCleverTapEvent({eventName: event_name, eventData: event_property})

    const prevPath = sessionStorage.getItem("prevPath");
    if (gating === undefined) {
      setIsBackModalOpen(true);
    } else {
      const previousRoute = previousPathRef.current;

      if (prevPath === "/loading" && window.location.pathname == '/loandetail-v3') {
        returnBackToApp();
      } else {
        router.back();
      }
    }
  };

  const renderPageHeader = useCallback(() => {
    if(increaseLimit) {
      return <IncreaseLimitModal 
        onExitClick={returnBackToApp}
        amount={amount}
        onIncreaseLimit={() => setIsBackModalOpen(false)}
      />
    }else if(showOld){
      return <OldModal
        onNoClick={() => setIsBackModalOpen(false)}
        onYesClick={returnBackToApp}
      />
    }else{
      return <NewModal 
        onContinueClick={() => setIsBackModalOpen(false)} 
        onExitClick={returnBackToApp} 
        isHeadPhone={isHeadPhone} 
        handleCustomerSupport={handleCustomerSupport}
        amount={amount}
      />
    }
  }, [amount, isHeadPhone, increaseLimit, showOld])
  return (
    <>
      {centeredHeader ?
        <header className={styles.header} style={{ backgroundImage: nobg ? 'none' : '', minHeight: nobg ? 'auto' : '', backgroundColor: bgcolor ? bgcolor : '' }}>
          {back || call ? <div className={styles.headerIcons}>
            {back && <div className={styles.backIcon} onClick={() => handlebackclick()}>
              {!cross ? <>

                <Image src={backIcon} width={24} height={24} alt="Back" />
              </>
                :
                <>
                  <Image src={crossheader} width={24} height={24} alt="Back" />
                </>
              }
            </div>}
            {call || isHeadPhone && <div className={styles.backIcon} onClick={handleCustomerSupport}>
              <Image src={callIcon} width={24} height={24} alt="Call" />
            </div>}
          </div> : null}
          <div className={`${styles.headerContentWrapper} ${styles.centeredHeaderContentWrapper}`}>
            {icon && <div className={styles.icon}>{icon}</div>}
            <div className={styles.headerContent}>
              {title ? <h1 className={styles.fullWidth}>{title}</h1> : null}
              {para ? <p>
                {parts[0]}
                {highlightText && parts.length > 1 && <strong>{highlightText}</strong>}
                {parts[1] ?? ""}
              </p> : null}
            </div>
          </div>
        </header> :
        <header className={styles.header} style={{ backgroundImage: nobg ? 'none' : '', minHeight: nobg ? 'auto' : '', backgroundColor: bgcolor ? bgcolor : '' }}>
          <div className={styles.headerIcons}>
            {back && <div className={styles.backIcon} onClick={() => handlebackclick()}>
              {!cross ? <>

                <Image src={backIcon} width={24} height={24} alt="Back" />
              </>
                :
                <>
                  <Image src={crossheader} width={24} height={24} alt="Back" />
                </>
              }
            </div>}
            <>
              {(isHeadPhone) && (
                <div className={styles.backIcon} onClick={handleCustomerSupport}>

                  <Image src={callIcon} width={24} height={24} alt="Back" />
                </div>
              )}
            </>
          </div>
          <div className={styles.headerContentWrapper}>
            <div className={styles.headerContent}>
              {title ? <h1 className={!icon ? styles.fullWidth : ''}>{title}</h1> : null}
              {congrats ? <p className={styles.congratulation_text}>Congratulations 🎉</p> : null}
              {para ?
                <p>
                  {parts[0]}
                  {highlightText && parts.length > 1 && <strong>{highlightText}</strong>}
                  {parts[1] ?? ""}
                </p>

                : null}
            </div>
            {icon && <div className={styles.icon}>{icon}</div>}
          </div>
        </header>
      }
      <BottomPopup
        isOpen={isBackModalOpen}
        onClose={() => {
          setIsBackModalOpen(false);
        }}
        title=""
        className="close-app-modal">
        {renderPageHeader()}
       
      </BottomPopup>
      <CtEvents data={ctData} />
      {pageerror || pagesuccess ? (
        <ToastMessage color={pagesuccess ? "green" : "red"}>
          {pagesuccess ? pagesuccess : pageerror}
        </ToastMessage>
      ) : null}
      {isExit &&
        <div style={{ zIndex: 999, position: "fixed", top: 0, bottom: 0, left: 0, right: 0 }}>

          <LoadingComp faded={true} />

        </div>
      }

    </>
  )
}

export default PageHeader
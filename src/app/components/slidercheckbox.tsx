"use client";

import { useState } from "react";
import styles from "./SliderCheckbox.module.scss";

interface SliderCheckboxProps {
  label?: string; // Optional label for the slider
  initialChecked?: boolean; // Initial state
  onChange?: (checked: boolean) => void; // Callback when toggled
}

const SliderCheckbox = ({
  label = "",
  initialChecked = false,
  onChange,
}: SliderCheckboxProps) => {
  const [checked, setChecked] = useState(initialChecked);

  const toggleChecked = () => {
    const newChecked = !checked;
    setChecked(newChecked);
    if (onChange) {
      onChange(newChecked);
    }
  };

  return (
    <div className={styles.sliderCheckbox}>
      {label && <span className={styles.label}>{label}</span>}
      <div
        className={`${styles.slider} ${checked ? styles.checked : ""}`}
        onClick={toggleChecked}
      >
        <div className={styles.sliderThumb}></div>
      </div>
    </div>
  );
};

export default SliderCheckbox;

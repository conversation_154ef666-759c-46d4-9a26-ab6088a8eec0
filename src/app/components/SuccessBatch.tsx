'use client'
import React from 'react';
import '../scss/button.scss';
import successIcon from '../images/success-icon-bordered.svg'
import Image from 'next/image';
import styles from './success.module.scss'
function SuccessBatch({children, className} : {children: React.ReactNode, className: string}) {
    return (
        <div className={`${styles.successBatch} ${className}`}>
            <div className={styles.successBatchIcon}>
                <Image src={successIcon} alt="Success" />
            </div>
            <div className={styles.successBatchContent}>
                {children}
            </div>
        </div>
    );
}

export default SuccessBatch;

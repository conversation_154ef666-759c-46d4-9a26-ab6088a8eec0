import React from "react";
import infoIcon from "../images/info.svg";
import Image from "next/image";

interface OldModalProps {
  onYesClick: () => void;
  onNoClick: () => void;
}

const OldModal = ({ onYesClick, onNoClick }: OldModalProps) => {
  return (
    <div>
      <Image src={infoIcon} alt="Info" />
      <p className="close-para">
        Are you sure you want to cancel your loan application?
      </p>
      <div style={{display: "flex", columnGap: 10, marginTop: 24}}>
        <button className="btn btn-primary btn-primary-outline samebg" onClick={onYesClick}>
          Yes
        </button>
        <button className="btn btn-primary btn-primary-outline blackbg" onClick={onNoClick}>
          No
        </button>
      </div>
    </div>
  );
};

export default OldModal;

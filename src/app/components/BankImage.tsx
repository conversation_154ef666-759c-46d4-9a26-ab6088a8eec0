import React, {useState} from "react";
import Image from "next/image";
import BankDetailsForm from "@/app/select-bank/page";

const BankImage = ({ bankName, ratioHeight }: { bankName: string, ratioHeight: number,  }) => {
    const [imgSrc, setImgSrc] = useState(getBankUrlByName(bankName));

    function getBankUrlByName(bankName: string): string {
        try {
            const name = bankName.replace(/ /g, "_").replace(/\./g, "").toUpperCase();
            return `https://passets.stashfin.com/upi/bank_logos/${name}.png`;
        } catch (error) {
            console.error(error);
        }
        return `https://passets.stashfin.com/upi/bank_logos/default.png`;
    }
    return (
        <Image
            src={imgSrc}
            alt={''}
            width={ratioHeight}
            height={ratioHeight}
            onError={() => setImgSrc("/images/bank-icon.png")} // Local fallback
        />
    );
};

export default BankImage;
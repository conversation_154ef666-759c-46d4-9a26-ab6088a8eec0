import Image from 'next/image';
import React, { useMemo } from 'react';
import CoinsImg from "../images/bohotSareCoins.png";
import { formatCurrency } from '../store/middleware/currencyFormatter';
import GroupOfPeopleImg from "../images/group_of_people.png";
import styles from "./header.module.scss"
import { getRandomIntInRange } from '../utils/helperFunctions';

const textArr = [
  "Over 5,000 users like you got a higher loan by updating their banking details.",
  "Most users who shared banking details increased their loan amount.",
  "You could qualify for more—others did by verifying their bank.",
  "Join thousands who boosted their loan instantly!",
  "Many users unlocked higher limits with one simple step.",
];

interface IncreaseModalProps {
    amount: number;
    onIncreaseLimit: () => void;
    onExitClick: () => void;
}

const IncreaseLimitModal = ({onExitClick, onIncreaseLimit, amount}: IncreaseModalProps) => {
  const staticText = useMemo(() => textArr[getRandomIntInRange(0, 4)], [])
  return (
    <div className={styles.increase_limit_modal_container}>
        <Image src={CoinsImg} alt='coin image' width={158} />
        <p className={styles.amount_not_enough}>₹{formatCurrency(amount)} Not enough</p>
        <p className={styles.want_higher_limit}>Want a higher limit?</p>
        <Image src={GroupOfPeopleImg} alt='group people' />
        <p className={styles.number_of_people}>{staticText}</p>
        <div className={styles.btn_container}>
            <button className='btn btn-primary' onClick={onIncreaseLimit}>Increase Limit</button>
            <button className={styles.exit_btn} onClick={onExitClick}>Exit Anyway</button>
        </div>
    </div>
  )
}

export default IncreaseLimitModal
@import '../../scss/variable.scss';

.toast{
    width: 344px;
    max-width: 100%;
    height: auto;
    min-height: 54px;
    padding: 10px 16px;
    display: flex !important;
    align-items: center;
    justify-content: flex-start;
    color: $color-white;
    border-radius: 12px;
    position: fixed;
    bottom: 30px;
    left: 50%;
    cursor: pointer;
    transform: translateX(-50%);
    box-shadow: none;
    font-size: 16px;
    font-weight: 600;
    z-index: 9999;
    transition: 0.6s ease-in-out;
    &.hide {
        opacity: 0;
        transform: translateX(-40%);
    }
    .icon{
        width: 32px;
        height: 32px;
        flex-grow: 0;
        flex-shrink: 0;
        margin-right: 10px;
        img{
            width: 100%;
            height: auto;
        }
    }
    .close{
        position: absolute;
        cursor: pointer;
        right: 15px;
        top: calc(50% - 10px);
        width: 20px;
        height: 20px;
        margin-right: 0;
    }
}
.toast-green{
    background-color: $color-green !important;
}
.toast-red{
    background-color: $red !important;
}
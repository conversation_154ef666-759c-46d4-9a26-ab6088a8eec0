import React, { useEffect, useState } from 'react'
import './toasts.scss'
import Image from 'next/image';
import successIcon from '../../images/success-icon-bordered.svg'
import failedIcon from '../../images/failure-icon-bordered.svg'

function ToastMessage({ color, children, duration = 4000, manualClose=false }: { color: string, children: React.ReactNode, duration?: number, manualClose?: boolean }) {
  const [showToast, setShowToast] = useState(true);
  useEffect(() => {
    if (manualClose) {
      return;
    }
    const hideTimeout = setTimeout(() => {
      setShowToast(false);
    }, duration);
    return () => {
      clearTimeout(hideTimeout);
    };
  }, [duration]);
  const hideToast = () => {
    setShowToast(false);
  };
  return (
    <>
      {showToast && (
        <div className={`toast toast-${color} ${showToast ? '' : 'hide'}`} onClick={hideToast}>
          <span className={`icon`}><Image src={color === 'green' ? successIcon : failedIcon} alt={color === 'green' ? 'success' : 'failed'} /></span>
          {children}
        </div>
      )}
    </>
  )
}

export default ToastMessage
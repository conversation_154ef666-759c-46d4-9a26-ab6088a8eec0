import dynamic from 'next/dynamic';
import loadingAnimation from "../../../../public/Cash-Stack.json";
import styles from './sharedStyles.module.scss';
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

const CashLottie = () => {
  return (
    <div className={`${styles.cash_lottie_container} page-wrapper`}>
      <Lottie animationData={loadingAnimation} loop={true} style={{ height: 200, width: 200 }} />
      <p style={{fontWeight: 700, fontSize: 20}}>Processing your request</p>
      <p style={{fontWeight: 500, fontSize: 14, color: "#7b7b7b"}}>This may take a few seconds</p>
    </div>
  )
}

export default CashLottie
@import '../../scss/variable.scss';

.modal_overlay {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0.11, 0.09, 0.09, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    padding: 0 16px;

    .modal_content {
        background: #ffffff;
        border-radius: 10px;
        position: relative;
        padding: 32px 16px;
        text-align: center;
        width: 100%;

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: $light-gray;
            border-radius: 10px;
        }

        &::-webkit-scrollbar-thumb {
            background: $color-gray;
            border-radius: 10px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: $color-gray;
        }
    }

    .earlier_limit_text{
        font-size: 12px;
        font-weight: 600;
        color: $color-gray
    }

    .new_limit{
        font-size: 34px;
        font-weight: 700;
    }

    .higher_limit_text{
        font-weight: 500;
        font-size: 14px;
        color: #4D4D4D;
        margin: 16px 0;

        span{
            font-weight: 700;
        }
    }

    .accept_offer_btn{
        margin-bottom: 16px;
        display: block;
        width: 100%;
        background-color: #000;
        color: #ffffff;
        font-weight: 600;
        border-radius: 8px;
        padding: 10px 0;
        letter-spacing: 0.5px;
        border: none;
    }

    .ignore_btn{
        background: none;
        border: none;
        font-weight: 600;
        letter-spacing: 0.5px
    }

}

.cash_lottie_container{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 999;
    background-color: white;
}
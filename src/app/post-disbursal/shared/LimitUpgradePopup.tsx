"use client"
import { formatCurrency } from "@/app/store/middleware/currencyFormatter";
import Image from "next/image";
import React from "react";
import MaxLimitSliderImg from "../../images/maxLimitSlider.svg";
import modalStyles from "./sharedStyles.module.scss";

interface LimitUpgradePopupProps {
  handleIgnoreClick: () => void;
  oldLimit?: number;
  newLimit?: number;
  handleAcceptOfferClick?: () => void;
}

const LimitUpgradePopup: React.FC<LimitUpgradePopupProps> = ({handleIgnoreClick, oldLimit, newLimit, handleAcceptOfferClick }) => {

  return (
    <div className={`${modalStyles.modal_overlay}`}>
      <div
        className={modalStyles.modal_content}
        style={{
          transition: "margin-bottom 0.3s ease",
        }}
      >
        <div className={modalStyles.modal_body}>
          <p className={modalStyles.earlier_limit_text}>Earlier Limit Available</p>
          <p className={modalStyles.new_limit}>₹{formatCurrency(oldLimit || 0)}</p>
          <Image src={MaxLimitSliderImg} width={290} height={44} alt="slider-img" />
          <p style={{fontWeight: 600}}>Don't miss your upgrade!</p>
          <p className={modalStyles.higher_limit_text}>You're eligible for a higher limit of <span>₹{formatCurrency(newLimit || 0)}</span>. <br/> Grab it before the offer expires.</p>

          <button onClick={handleAcceptOfferClick} className={modalStyles.accept_offer_btn}>Accept Offer</button>
          <button onClick={handleIgnoreClick} className={modalStyles.ignore_btn}>Ignore</button>
        </div>
        
      </div>
    </div>
  );
};

export default LimitUpgradePopup;

import { formatCurrency } from "@/app/store/middleware/currencyFormatter";
import React from "react";
import selectWithdrawStyles from "../select-withdraw-amount/selectWithdraw.module.scss";

interface IncreaseLimitCheckboxProps {
    checked: boolean;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    oldLimit: number;
}

const IncreaseLimitCheckbox = ({checked, onChange, oldLimit}:IncreaseLimitCheckboxProps) => {

  return (
    <label className={selectWithdrawStyles.consent_check}>
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        style={{ marginTop: 6, accentColor: "black" }}
      />
      <p className={selectWithdrawStyles.post_checkbox}>
        I accept the revised limit shown above <br />
        <span className={selectWithdrawStyles.uncheck_warning}>
          (Unchecking this will keep your earlier offer of
          <b> ₹{formatCurrency(Number(oldLimit))}</b>)
        </span>
      </p>
    </label>
  );
};

export default IncreaseLimitCheckbox;

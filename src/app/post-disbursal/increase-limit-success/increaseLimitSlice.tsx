import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface IncreaseLimitStateType {
  // isChecked: boolean;
  revisedLimitChecked: boolean;
  isModalOpen: boolean;
}

const initialState: IncreaseLimitStateType = {
  // isChecked: true,
  revisedLimitChecked: true,  
  isModalOpen: false,
};

const increaseLimitSuccessSlice = createSlice({
  name: "increaseLimitSuccess",
  initialState,
  reducers: {
    // setIsChecked: (state, action: PayloadAction<boolean>) => {
    //   state.isChecked = action.payload;
    // },
    setReviseLimitChecked: (state, action: PayloadAction<boolean>) => {
      state.revisedLimitChecked = action.payload;
    },
    setIsModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isModalOpen = action.payload;
    },
  },
});

export const { setReviseLimitChecked, setIsModalOpen } = increaseLimitSuccessSlice.actions;
export default increaseLimitSuccessSlice.reducer;

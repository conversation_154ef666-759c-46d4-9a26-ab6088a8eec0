"use client";
import PageHeader from "@/app/components/PageHeader";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import Confetti from "react-confetti";
import { useDispatch, useSelector } from "react-redux";
import LoadingComp from "../../component/loader";
import PageWrapper from "../../components/PageWrapper";
import ToastMessage from "../../components/ToastMessage/ToastMessage";
import { ApiResponse } from "../../store/interface/apiInterface";
import { formatCurrency } from "../../store/middleware/currencyFormatter";
import { RootState } from "../../store/store";
import { apiRequest } from "../../utils/api";
import { ENDPOINTS } from "../../utils/endpoints";
import styles from "../../withdraw-amount/withdraw.module.scss";
import selectWithdrawStyles from "../select-withdraw-amount/selectWithdraw.module.scss";
import { setLocTenureDetails, setPageError, setPageSuccess, setSelectedAmt, setSliderDisabled } from "../select-withdraw-amount/selectWithdrawAmtSlice";
import IncreaseLimitCheckbox from "../shared/IncreaseLimitCheckbox";
import LimitUpgradePopup from "../shared/LimitUpgradePopup";
import { setIsModalOpen, setReviseLimitChecked } from "./increaseLimitSlice";
import { setLoanData } from "@/app/withdraw-amount/withdraw.slice";

const SelectWithdrawAmount = () => {
  const dispatch = useDispatch();
  const router = useRouter();

  const [percentage, setPercentage] = useState<number>(0);
  const [showLoader, setShowLoader] = useState<boolean>(false);

  const { revisedLimitChecked, isModalOpen } = useSelector((state: RootState) => state.increaseLimitSuccess);
  const {loanData} = useSelector((state: RootState) => state.amount)
  const [isChecked, setIsChecked] = useState(true);

  const { locDetails, locTenureDetails, breakpoints, sliderDisabled, newLocDetails, selectedAmt, pageError, pageSuccess, customerId } = useSelector(
    (state: RootState) => state.selectWithdrawAmt
  );

  // CONFETTI-----------------
  const [showConfetti, setShowConfetti] = useState<boolean>(false);
  useEffect(() => setShowConfetti(true), []);
  useEffect(() => {
    let timerId: ReturnType<typeof setTimeout> | undefined;
    if (showConfetti) {
      timerId = setTimeout(() => setShowConfetti(false), 3500);
    }

    return () => {
      clearTimeout(timerId);
    };
  }, [showConfetti]);
  // -x-x-x-x-x-x-x-x-x-x-x-x-x-

  useEffect(() => {
    const max = breakpoints.length - 1;
    const initialPercentage = (breakpoints.indexOf(selectedAmt) / max) * 100;
    setPercentage(initialPercentage);
  }, []);

  const fetchLoanDetails = async (amount: number) => {
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    try {
      const response = await apiRequest<ApiResponse>("GET", `${ENDPOINTS.getloctenure}?amount=${amount}&is_cli=true`);
      if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === "success" || response.mainData?.status === "true") {
        dispatch(setLocTenureDetails(response.mainData.data));
      } else {
        dispatch(setPageError(response.mainData?.error_message || "Unexpected error occurred."));
      }
      setTimeout(() => {
        dispatch(setSliderDisabled(false));
      }, 800);
    } catch (error) {
      dispatch(setPageError("Unexpected error occurred. Please try again later."));
      setTimeout(() => {
        dispatch(setSliderDisabled(false));
      }, 800);
    }
  };

  const roundDownToNearest500 = (value: number) =>
    Math.floor(value / 500) * 500;

  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSliderDisabled(true));
    const value = Number(event.target.value);
    const max = breakpoints.length - 1;
    const maxVal = breakpoints[max];
    const usedLimit: number = Number(localStorage.getItem("usedLimit")) || 0;
    // debugger
    if(breakpoints[value] > (maxVal - usedLimit)) {
      setPercentage(((maxVal - usedLimit) / maxVal) * 100);
      dispatch(setSelectedAmt(roundDownToNearest500(maxVal - usedLimit)));
      return;
    }
    setPercentage((value / max) * 100);
    dispatch(setSelectedAmt(breakpoints[value]));
  };

  const handleSliderChangeEnd = async () => {
    setShowLoader(true);
    await fetchLoanDetails(selectedAmt || 0);
    setShowLoader(false);
    setTimeout(() => {
      dispatch(setSliderDisabled(false));
    }, 400);
  };

  const onIncreaseLimitCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { checked } = event.target;
    dispatch(setReviseLimitChecked(checked));

    if (!checked) {
      dispatch(setIsModalOpen(true));
    }
  };

  // FOR MODAL BTN CLICK FUNCTIONS
  const handleIgnoreClick = () => {
    dispatch(setIsModalOpen(false));
    router.push("/post-disbursal/select-withdraw-amount?showCheckbox=true");
  };

  const handleAcceptOfferClick = () => {
    dispatch(setReviseLimitChecked(true));
    dispatch(setIsModalOpen(false));
  }

  // -------xxxxxx----------

  const handleProceedBtnClick = async () => {
    setShowLoader(true);
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    try {
      const payload = {
        customer_id: customerId,
        latitude: Number(localStorage.getItem("lat")),
        longitude: Number(localStorage.getItem("long")),
        ip: localStorage.getItem("ip"),
        consent: revisedLimitChecked as boolean,
      };
      const tempLoanData = {...loanData, amount: selectedAmt}
      dispatch(setLoanData(tempLoanData));
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.updateIncreasedLimit, payload);
      if (response.mainData?.success) {
        router.push("/loandetail?postDisbursal=true")
      }else{
        dispatch(setPageError(response.mainData?.error_message || "Unexpected error occurred."));
      }
    } catch (error) {
      console.error("Error in handleProceedBtn:", error);
      dispatch(setPageError("Something went wrong. Please try again later."));
      return;
    }
  };

  return (
    <PageWrapper bgGreen>
      {showConfetti && (
        <Confetti
          confettiSource={{
            x: window.innerWidth - 20,
            y: -20,
            w: window.innerWidth,
            h: 0,
          }}
          wind={-0.05}
          initialVelocityX={6}
          initialVelocityY={6}
          opacity={0.8}
          tweenDuration={1000}
        />
      )}
      <div className={selectWithdrawStyles.congratulation_color} />
      <div className={`external-wrapper ${styles.select_amount_container}`}>
        <PageHeader gating back congrats title={""} para="We have unlocked a new higher limit for you" />
        <p style={{ fontWeight: 600, padding: "0 16px", marginBottom: "16px" }}>Pick an amount from your approved limit</p>

        <div className={selectWithdrawStyles.post_congrats_container}>
          <div className={styles.loanAmount} style={{ backgroundColor: "white", border: "1px solid #EAEAEA" }}>
            <div className={styles.loanAmountContainer}>
              <div className={styles.amount}>
                <span>₹</span>
                <span>{formatCurrency(selectedAmt || 0)}</span>
              </div>
              <div
                style={
                  sliderDisabled
                    ? {
                        filter: "saturate(0)",
                        opacity: 0.4,
                        pointerEvents: "none",
                      }
                    : undefined
                }
              >
                <div className={selectWithdrawStyles.dots}>
                  {Array.from({ length: 8 }).map((_: any, index: number) => (
                    <div
                      key={index}
                      className={`${selectWithdrawStyles.dot} ${index <= breakpoints.indexOf(selectedAmt) ? selectWithdrawStyles.selected : ""}`}
                    ></div>
                  ))}
                </div>
                <input
                  type="range"
                  className={selectWithdrawStyles.slider}
                  min="0"
                  max={breakpoints.length - 1}
                  step="1"
                  style={{ background: `linear-gradient(to right, var(--black-color) ${percentage}%, var(--white-color) ${percentage}%)` }}
                  value={breakpoints.indexOf(selectedAmt)}
                  onChange={handleSliderChange}
                  onMouseUp={handleSliderChangeEnd} // Detects when user releases mouse
                  onTouchEnd={handleSliderChangeEnd} // Detects when user lifts finger (for mobile)
                />
                <div className={styles.breakpoints}>
                  <span>₹{formatCurrency(locDetails?.minimum_request_amount || 0)}</span>
                  <span>₹{formatCurrency(newLocDetails?.new_limit || 0)}</span>
                </div>
              </div>
            </div>
          </div>

          <div className={selectWithdrawStyles.expected_emi_container}>
            <p>Expected EMI</p>
            <p>
              ₹{locTenureDetails?.tenure_details[0]?.expected_emi_amount} x {locTenureDetails?.tenure_details[0]?.tenure} months
            </p>
          </div>

          <div className={selectWithdrawStyles.amount_you_repay}>
            <p>Amount you pay</p>
            <p>
              ₹
              {locTenureDetails?.tenure_details?.[0]?.expected_emi_amount && locTenureDetails?.tenure_details?.[0]?.tenure
                ? formatCurrency(locTenureDetails.tenure_details[0].expected_emi_amount * locTenureDetails.tenure_details[0].tenure)
                : 0}
            </p>
          </div>
        </div>
        <div className={selectWithdrawStyles.select_withdraw_footer}>
          <label className={selectWithdrawStyles.consent_check}>
            <div>
              <input type="checkbox" checked={isChecked} onChange={() => setIsChecked(!isChecked)} style={{ marginTop: 6, accentColor: "#000" }} />
            </div>
            <p className={selectWithdrawStyles.post_checkbox}>I confirm that my annual household income is greater than 3 lakhs.</p>
          </label>

          <IncreaseLimitCheckbox checked={revisedLimitChecked} onChange={onIncreaseLimitCheckboxChange} oldLimit={locDetails?.loc_limit || 0} />

          <button
            type="button"
            onClick={handleProceedBtnClick}
            className={`btn btn-primary ${sliderDisabled || !isChecked ? "disabled" : ""}`}
            style={{ marginTop: 10 }}
          >
            Proceed
          </button>

          <p className={selectWithdrawStyles.powered_by_select_withdraw}>Powered by Akara Capital Advisors Private Limited</p>
        </div>
      </div>
      {isModalOpen && (
        <LimitUpgradePopup
          handleAcceptOfferClick={handleAcceptOfferClick}
          handleIgnoreClick={handleIgnoreClick}
          oldLimit={locDetails?.loc_limit}
          newLimit={newLocDetails?.new_limit}
        />
      )}
      {pageError || pageSuccess ? <ToastMessage color={pageSuccess ? "green" : "red"}>{pageSuccess ? pageSuccess : pageError}</ToastMessage> : null}
      {showLoader ? <LoadingComp faded /> : null}
    </PageWrapper>
  );
};

export default SelectWithdrawAmount;

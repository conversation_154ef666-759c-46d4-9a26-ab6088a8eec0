@import '../../scss/variable.scss';

.post_congrats_container{
    position: relative;
    padding: 0 16px;
    height: 550px;

    .expected_emi_container{
        background-color: $color-light;
        border-radius: 12px;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        font-weight: 600;
        margin-bottom: 12px;
    }  

    .amount_you_repay{
        display: flex;
        justify-content: space-between;
        padding: 16px;
        border: 1px solid $color-light;
        border-radius: 12px;
        background-color: #fff;
        font-size: 14px;
    }

    .slider {
        appearance: none;
        width: 100%;
        height: 12px;
        background: $color-white;
        border-radius: 9px;
        outline: none;
        transition: background 0.3s ease;
    }


    .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 42px;
        height: 42px;
        //background: $color-white;
        border-radius: 50%;
        //border: 4px solid $color1;
        cursor: pointer;
        position: relative;
        z-index: 2;
        background-position: center;
        background-size: cover;
        background-image: url('../../images/slider-thumb.svg');
    }

    .slider::-moz-range-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 42px;
        height: 42px;
        //background: $color1;
        border-radius: 50%;
        //border: 4px solid $color-white;
        cursor: pointer;
        position: relative;
        z-index: 2;
        background-position: center;
        background-size: cover;
        background-image: url('../../images/slider-thumb.svg');
    }

    .dots {
    display: flex;
    top: 10px;
    position: relative;
    justify-content: space-around;

    /*>div {
        width: 25%;
        display: flex;
        justify-content: space-around;
    }*/

    .dot {
        content: '';
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: $light-gray;

        &.selected {
            background-color: #D2D2D2 ;
        }
    }
}
}

.consent_check {
    display: flex;
    align-items: flex-start;
    column-gap: 12px;
    margin: 12px 0;
}

.select_withdraw_footer{
    position: absolute;
    background-color: white;
    left: 0;
    padding: 16px;
    z-index: 8;
    bottom: 0;
}

.post_checkbox{
    font-size: 14px;
    font-weight: 500;
}

.uncheck_warning{
    font-size: 10px;
    color: $color-gray
}

.powered_by_select_withdraw{
    font-size: 12px;
    text-align: center;
}

.congratulation_color{
    background: #FFFFA8;
    background: linear-gradient(135deg, rgba(255, 255, 168, 1) 0%, rgba(213, 250, 230, 1) 44%, rgba(213, 250, 230, 1) 100%);
    height: 260px;
    width: 100%;
    position: fixed;
    z-index: -1;
}

.congratulation_text{
    font-size: 24px;
    font-weight: 700;
    color: #2DB166;
    padding: 0 16px;
    width: 100%;
}
"use client";
import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
// import LoadingComp from "../../component/loader";
import PageHeader from "../../components/PageHeader";
import PageWrapper from "../../components/PageWrapper";
import ToastMessage from "../../components/ToastMessage/ToastMessage";
import { ApiResponse } from "../../store/interface/apiInterface";
import { formatCurrency } from "../../store/middleware/currencyFormatter";
import { RootState } from "../../store/store";
import { apiRequest } from "../../utils/api";
import { ENDPOINTS } from "../../utils/endpoints";
import styles from "../../withdraw-amount/withdraw.module.scss";
import { setReviseLimitChecked } from "../increase-limit-success/increaseLimitSlice";
import IncreaseLimitCheckbox from "../shared/IncreaseLimitCheckbox";
import selectWithdrawStyles from "./selectWithdraw.module.scss";
import {
  setBreakpoints,
  setCustomerId,
  setIsChecked,
  setLocDetails,
  setLocTenureDetails,
  setPageError,
  setPageSuccess,
  setSelectedAmt,
  setSliderDisabled,
  setNewLocDetails,
} from "./selectWithdrawAmtSlice";
import { setLoanData } from "../../withdraw-amount/withdraw.slice";
import CashLottie from "../shared/CashLottie";

const SelectWithdrawAmount = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const showCheckbox = searchParams.get("showCheckbox");
  const auth_token = searchParams.get("auth_token");
  const device_id = searchParams.get("device_id");
  const ip = searchParams.get("ip");
  const lat = searchParams.get("lat");
  const long = searchParams.get("long");
  const [showLoader, setShowLoader] = useState<boolean>(false);
  const [sliderPercentage, setSliderPercentage] = useState<number>(0);

  const {
    sliderDisabled,
    breakpoints,
    selectedAmt,
    isChecked,
    pageError,
    pageSuccess,
    locDetails,
    customerId,
    locTenureDetails,
  } = useSelector((state: RootState) => state.selectWithdrawAmt);

  const { revisedLimitChecked } = useSelector(
    (state: RootState) => state.increaseLimitSuccess
  );

  useEffect(() => {
    if (auth_token) {
      localStorage.setItem("auth_token", auth_token);
    }
  }, [auth_token]);

  useEffect(() => {
    if (device_id) {
      localStorage.setItem("device_id", device_id);
    }
  }, [auth_token]);

  useEffect(() => {
    if (ip) {
      localStorage.setItem("ip", ip);
    }
  }, [ip]);

  useEffect(() => {
    if (lat) {
      localStorage.setItem("lat", lat);
    }
  }, [lat]);

  useEffect(() => {
    if (long) {
      localStorage.setItem("long", long);
    }
  }, [long]);

  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSliderDisabled(true));
    const value = Number(event.target.value);
    const max = breakpoints.length - 1;
    setSliderPercentage((value / max) * 100);
    dispatch(setSelectedAmt(breakpoints[value]));
  };

  const handleSliderChangeEnd = async () => {
    setShowLoader(true);
    await fetchLoanDetails(selectedAmt || 0);
    setShowLoader(false);
    setTimeout(() => {
      dispatch(setSliderDisabled(false));
    }, 400);
  };

  const handleIncLimitCheckbox = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { checked } = event.target;
    dispatch(setReviseLimitChecked(checked));
    if (checked) {
      await handleProceedBtn({ skipLoan: false });
    }
  };

  const handleProceedBtn = async ({
    skipLoan = true,
  }: {
    skipLoan?: boolean;
  }) => {
    setShowLoader(true);
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    if (showCheckbox && skipLoan) {
      router.push("/loandetail/?postDisbursal=true");
      return;
    }
    try {
      const payload = {
        customer_id: customerId,
        selected_amount: selectedAmt,
      };

      const response = await apiRequest<ApiResponse>(
        "POST",
        `${ENDPOINTS.locLimitIncrease}`,
        payload
      );

      if (response.mainData?.success) {
        if (
          locDetails &&
          response.mainData?.data?.new_limit > locDetails?.remaining_loc
        ) {
          const bpArr: any = [];
          let minAmount = roundDownToNearest500(locDetails.minimum_request_amount);
          let step = locDetails.request_amount_increment_step;
          let remainingAmount = roundDownToNearest500(response.mainData?.data?.new_limit);
          for (let i = minAmount; i <= remainingAmount; i += step) {
            if (i + step > remainingAmount && i !== remainingAmount) {
              bpArr.push(remainingAmount);
              break;
            }
            bpArr.push(i);
          }
          dispatch(setBreakpoints(bpArr));
          dispatch(setSelectedAmt(roundDownToNearest500(response.mainData.data.selected_amount)));
          dispatch(setNewLocDetails(response.mainData.data));
          locTenureDetails &&
            dispatch(
              setLocTenureDetails({
                ...locTenureDetails,
                tenure_details: [
                  {
                    tenure: response.mainData.data.new_max_tenure,
                    expected_emi_amount: response.mainData.data.emi_amount,
                  },
                ],
              })
            );
          router.push("/post-disbursal/increase-limit-success");
        } else {
          router.push("/loandetail/?postDisbursal=true");
        }
      } else if (response.mainData?.success === false) {
        if (!skipLoan) {
          dispatch(setReviseLimitChecked(false));
        }
        dispatch(
          setPageError(
            response.mainData.error_message || "Unexpected error occurred."
          )
        );
        return;
      }
    } catch (error) {
      console.error("Error in handleProceedBtn:", error);
      dispatch(setPageError("Something went wrong. Please try again later."));
      return;
    }
  };

  const fetchLoanDetails = async (amount: number) => {
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    try {
      const response = await apiRequest<ApiResponse>(
        "GET",
        `${ENDPOINTS.getloctenure}?amount=${amount}`
      );
      if (
        response.mainData?.success?.toString().toLowerCase() === "true" ||
        response.mainData?.status === "success" ||
        response.mainData?.status === "true"
      ) {
        dispatch(setLocTenureDetails(response.mainData.data));
        dispatch(setLoanData(response.mainData.data));
      } else {
        dispatch(
          setPageError(
            response.mainData?.error_message || "Unexpected error occurred."
          )
        );
      }
      setTimeout(() => {
        dispatch(setSliderDisabled(false));
      }, 800);
    } catch (error) {
      dispatch(
        setPageError("Unexpected error occurred. Please try again later.")
      );
      setTimeout(() => {
        dispatch(setSliderDisabled(false));
      }, 800);
    }
  };

  const getPersoalDetails = async () => {
    try {
      const response = await apiRequest<ApiResponse>(
        "GET",
        ENDPOINTS.personalDetail
      );
      if (response.mainData && response.mainData?.success) {
        dispatch(setCustomerId(response.mainData.data.id));
      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message));
        } else if (response.error) {
          dispatch(setPageError(response.error));
        }
      }
    } catch (error) {
      console.log("Error fetching personal details:", error);
      dispatch(
        setPageError(
          "Failed to fetch personal details. Please try again later."
        )
      );
    }
  };

  const roundDownToNearest500 = (value: number) =>
    Math.floor(value / 500) * 500;

  const fetchLocWithdraw = async () => {
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    setShowLoader(true);
    try {
      const response = await apiRequest<ApiResponse>(
        "GET",
        ENDPOINTS.getlocwithdrawform
      );
      if (
        response.mainData?.success?.toString().toLowerCase() === "true" ||
        response.mainData?.status === "success" ||
        response.mainData?.status === "true"
      ) {
        const minAmount = roundDownToNearest500(response.mainData.data.minimum_request_amount);
        const remainingAmount = roundDownToNearest500(response.mainData.data.remaining_loc);
        const step = response.mainData.data.request_amount_increment_step;

        dispatch(
          setLocDetails({
            ...response.mainData.data,
            minimum_request_amount: minAmount,
            remaining_loc: remainingAmount,
          })
        );
        const bpArr: any = [];
        for (let i = minAmount; i <= remainingAmount; i += step) {
          if (i + step > remainingAmount && i !== remainingAmount) {
            bpArr.push(remainingAmount);
            break;
          }
          bpArr.push(i);
        }
        localStorage.setItem("usedLimit", response.mainData.data.used_loc);
        dispatch(setBreakpoints(bpArr));
        dispatch(setSelectedAmt(bpArr[bpArr.length - 1]));
        setSliderPercentage(100);
        await getPersoalDetails();
        await fetchLoanDetails(remainingAmount);
      } else {
        dispatch(
          setPageError(
            response.mainData?.error_message || "Unexpected error occurred."
          )
        );
      }
    } catch (error) {
      dispatch(setPageError("Unexpected error occurred."));
    } finally {
      setShowLoader(false);
    }
  };

  useEffect(() => {
    fetchLocWithdraw();
  }, []);

  return (
    <PageWrapper>
      <div className={`external-wrapper ${styles.select_amount_container}`}>
        <PageHeader
          title="Your loan, Your way"
          back
          para="Pick an amount from your approved limit"
        />

        <div className={selectWithdrawStyles.post_congrats_container}>
          <div className={styles.loanAmount}>
            <div className={styles.loanAmountContainer}>
              <div className={styles.amount}>
                <span>₹</span>
                <span>{Number(selectedAmt).toLocaleString("en-IN")}</span>
              </div>
              <div
                style={
                  sliderDisabled
                    ? {
                      filter: "saturate(0)",
                      opacity: 0.4,
                      pointerEvents: "none",
                    }
                    : undefined
                }
              >
                <div className={selectWithdrawStyles.dots}>
                  {Array.from({ length: 8 }).map((_: any, index: number) => (
                    <div
                      key={index}
                      className={`${selectWithdrawStyles.dot} ${index <= breakpoints.indexOf(selectedAmt)
                          ? selectWithdrawStyles.selected
                          : ""
                        }`}
                    ></div>
                  ))}
                </div>
                <input
                  type="range"
                  className={selectWithdrawStyles.slider}
                  min="0"
                  max={breakpoints.length - 1}
                  step="1"
                  style={{
                    background: `linear-gradient(to right, var(--black-color) ${sliderPercentage}%, var(--white-color) ${sliderPercentage}%)`,
                  }}
                  value={breakpoints.indexOf(selectedAmt)}
                  onChange={handleSliderChange}
                  onMouseUp={handleSliderChangeEnd} // Detects when user releases mouse
                  onTouchEnd={handleSliderChangeEnd} // Detects when user lifts finger (for mobile)
                />
                <div className={styles.breakpoints}>
                  <span>₹{locDetails?.minimum_request_amount}</span>
                  <span>₹{locDetails?.remaining_loc}</span>
                </div>
              </div>
            </div>
          </div>

          <div className={selectWithdrawStyles.expected_emi_container}>
            <p>Expected EMI</p>
            <p>
              ₹{locTenureDetails?.tenure_details[0]?.expected_emi_amount} x{" "}
              {locTenureDetails?.tenure_details[0]?.tenure} months
            </p>
          </div>

          <div className={selectWithdrawStyles.amount_you_repay}>
            <p>Amount you pay</p>
            <p>
              ₹
              {locTenureDetails?.tenure_details?.[0]?.expected_emi_amount &&
                locTenureDetails?.tenure_details?.[0]?.tenure
                ? formatCurrency(
                  locTenureDetails.tenure_details[0].expected_emi_amount *
                  locTenureDetails.tenure_details[0].tenure
                )
                : 0}
            </p>
          </div>

          <div className={selectWithdrawStyles.select_withdraw_footer}>
            <div>
              <label className={selectWithdrawStyles.consent_check}>
                <input
                  type="checkbox"
                  checked={isChecked}
                  onChange={() => dispatch(setIsChecked(!isChecked))}
                  style={{ marginTop: 6, accentColor: "black" }}
                />
                <p className={selectWithdrawStyles.post_checkbox}>
                  I confirm that my annual household income is greater than 3
                  lakhs.
                </p>
              </label>
            </div>

            {showCheckbox && (
              <IncreaseLimitCheckbox
                checked={revisedLimitChecked}
                onChange={handleIncLimitCheckbox}
                oldLimit={locDetails?.loc_limit || 0}
              />
            )}

            <button
              type="button"
              onClick={() => handleProceedBtn({})}
              className={`btn btn-primary ${sliderDisabled || !isChecked ? "disabled" : ""
                }`}
              style={{ marginTop: 10 }}
            >
              Proceed
            </button>

            <p className={selectWithdrawStyles.powered_by_select_withdraw}>
              Powered by Akara Capital Advisors Private Limited
            </p>
          </div>
        </div>
      </div>
      {pageError || pageSuccess ? (
        <ToastMessage color={pageSuccess ? "green" : "red"}>
          {pageSuccess ? pageSuccess : pageError}
        </ToastMessage>
      ) : null}
      {/* {showLoader ? <LoadingComp faded /> : null} */}
      {showLoader ? <CashLottie /> : null}
    </PageWrapper>
  );
};

export default SelectWithdrawAmount;
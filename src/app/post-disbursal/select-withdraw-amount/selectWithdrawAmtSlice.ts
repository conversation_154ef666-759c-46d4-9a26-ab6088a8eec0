import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface NewLocDetails {
  bill_amount: number;
  emi_amount: number;
  message: string;
  new_limit: number;
  new_max_tenure: number;
  selected_amount: number;
}
interface LocDetails {
  loc_limit: number;
  bill_date: string;
  minimum_request_amount: number;
  request_amount_increment_step: number;
  used_loc: number;
  pending_used_loc: number;
  max_tenure: number;
  rate_of_interest: number;
  min_tenure: number;
  can_reload_card: boolean;
  remaining_loc: number;
  request_max_percentage: number;
  sanction_letter: string;
  loc_disable: boolean;
}

interface LocTenureDetails {
  amount: number;
  bill_date: string;
  min_tenure: number;
  max_tenure: number;
  msg: string;
  first_emi_date: string;
  fcp_fee: string;
  fcp_interest: number;
  tenure_details: { tenure: number; expected_emi_amount: number }[];
  is_fip: boolean;
  amount_grossup: number;
}
interface SelectWithdrawAmtState {
  sliderDisabled: boolean;
  breakpoints: number[];
  selectedAmt: number;
  isChecked: boolean;
  pageError?: string | null;
  pageSuccess?: string | null;
  customerId?: string | null;
  locDetails?: LocDetails | null;
  locTenureDetails: LocTenureDetails | null;
  newLocDetails?: NewLocDetails | null;
}

const initialState: SelectWithdrawAmtState = {
  sliderDisabled: false,
  breakpoints: [],
  selectedAmt: 0,
  isChecked: true,
  pageError: null,
  pageSuccess: null,
  customerId: null,
  locDetails: null,
  locTenureDetails: null,
  newLocDetails: null,
};

const selectWithdrawAmtSlice = createSlice({
  name: "selectWithdrawAmt",
  initialState,
  reducers: {
    setSliderDisabled: (state, action: PayloadAction<boolean>) => {
      state.sliderDisabled = action.payload;
    },
    setBreakpoints: (state, action: PayloadAction<number[]>) => {
      state.breakpoints = action.payload;
    },
    setSelectedAmt(state, action: PayloadAction<number>) {
      state.selectedAmt = action.payload;
    },
    setIsChecked: (state, action: PayloadAction<boolean>) => {
      state.isChecked = action.payload;
    },
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageError = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pageSuccess = action.payload;
    },
    setCustomerId: (state, action: PayloadAction<string | null>) => {
      state.customerId = action.payload;
    },
    setLocDetails: (state, action: PayloadAction<LocDetails | null>) => {
      state.locDetails = action.payload;
    },
    setLocTenureDetails: (state, action: PayloadAction<LocTenureDetails | null>) => {
      state.locTenureDetails = action.payload;
    },
    setNewLocDetails: (state, action: PayloadAction<NewLocDetails | null>) => {
      state.newLocDetails = action.payload;
    },
  },
});

export const {
  setSliderDisabled,
  setSelectedAmt,
  setBreakpoints,
  setIsChecked,
  setPageError,
  setPageSuccess,
  setCustomerId,
  setLocDetails,
  setLocTenureDetails,
  setNewLocDetails,
} = selectWithdrawAmtSlice.actions;
export default selectWithdrawAmtSlice.reducer;

'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import failureIcon from '../images/failure-icon.svg'
import styles from './senctioned.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import { navigateToPage } from '../utils/bridge';
import CtEvents from '../utils/Ctevents';
function Page() {
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_rejection_screen", "Product category": 'cb', "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])

    const returnBackToApp = () => {
        // debugger;
        // const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        // const event_name = 'Button Clicked'
        // const productcode = localStorage.getItem("product_code")
    
        // const event_property = { "CTA": 'Back to Home', "Page Shown": "Congrats Screen", "Banner": "LAMF", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP }
        // setCtdata({ event_name, event_property })
        window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
    };

    return (
        <div className={`external-wrapper ${styles.externalWrapper}`}>
            <PageHeader title="Sorry, We can't process your application at this time" centeredHeader={true} call={false} nobg={true} bgcolor="#FFE7E7" icon={<Image src={failureIcon} alt='Header' />} />
            <div className={`page-content ${styles.pageContent}`}>
                <div className={`${styles.creditBox}`}>
                    <div className={styles.title}>
                        credit report
                    </div>
                    <p>Check your Credit score for FREE</p>
                    <div className={styles.circularbtn} onClick={() => {
                        navigateToPage({
                            type: "page",
                            landingPage: "pcr_launch"
                        });

                        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                        const event_name = 'Button Clicked'
                        const productcode = localStorage.getItem("product_code")
                        const event_property = { "Screen Name": "cb_rejection_screen", "Product category": 'cb', "CTA": "Pay Now", "Source": cteventsfromstorage?.source }
                        setCtdata({ event_name, event_property })

                    }}>Check now</div>
                </div>
                <div className="bottom-footer p-0 mt-auto">
                    <button type="button" className="btn btn-primary-outline" onClick={() => returnBackToApp()}>Go to Homepage</button>
                    <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
                </div>
            </div>
            <CtEvents data={ctData} />

        </div>
    );
}

export default Page;

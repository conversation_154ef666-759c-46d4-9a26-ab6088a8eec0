@import '../scss/variable.scss';

.externalWrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;

    h1 {
        font-size: 20px !important;
    }

    header {
        padding-bottom: 20px;
    }

    .pageContent {
        min-height: calc(100vh - 263px);
    }
}

.circularbtn {
    padding: 12px 16px;
    border-radius: 66px;
    border: 1px solid #000000;
    background: #fff;
    font-family: var(--font-mona-sans);
    font-weight: 600;
    line-height: 1;
    font-size: 16px;
    color: #000;
    cursor: pointer;
    max-width: 120px;
}

.creditBox {
    background: #F0F0F0 url(../images/credittextimg1.svg) no-repeat;
    background-position: bottom 0 right 16px;
    padding: 16px 24px;
    border-radius: 12px;

    .title {
        font-family: var(--font-mona-sans);
        font-weight: 600;
        line-height: 16px;
        font-size: 16px;
        max-width: calc(100% - 60px);
        text-transform: uppercase;
        margin-bottom: 10px;
    }

    p {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        line-height: 26px;
        font-size: 20px;
        max-width: calc(100% - 70px);
        margin-bottom: 15px;
    }
}
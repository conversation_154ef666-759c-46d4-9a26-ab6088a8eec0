'use client'
import React from 'react';
import '../scss/button.scss';
import failureImg from '../images/selfie-failed.png'
import ShieldIcon from '../images/shield-icon.svg'
import styles from './kyc.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import PageWrapper from '../components/PageWrapper';
import { ENDPOINTS } from '../utils/endpoints';
import { apiRequest } from '../utils/api';
import { setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
function SelfieFailure() {
    const dispatch = useDispatch();
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);

    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        landing_page: string,
        profile_pic_upload_url: string,
        status: any
    }
    const TakeASelfie = async () => {
        try {
            const payload = {
                "redirect_url": `${window.location.origin}/selfie-polling`,
            }
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.selfie, payload);

            if (response.mainData && response.mainData.status && response.mainData.status.toString().toLowerCase() === "success") {
                debugger;
                window.location.assign(response.mainData.profile_pic_upload_url)

            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
                    dispatch(setPageError(response.mainData.error_message as string | "null"))
                } else if (response.error) {
                    // debugger;
                } else {
                }
            }
        } catch (error) {
        }
    }
    return (
        <PageWrapper>
            <div className={`external-wrapper ${styles.selfieStatusPage}`}>
                <PageHeader title='' />
                <div className='page-content'>
                    <div className={styles.kycContent}>
                        <div className={styles.kycCard}>
                            <Image src={failureImg} alt="Congratulations" />
                            <h3>Selfie verification failed</h3>
                            <p>We couldn't verify your face. Please retake your selfie in good light</p>
                        </div>
                    </div>
                    <div className="bottom-footer p-0">
                        <p className="secure-tag">
                            <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                        </p>
                        <button type="submit" className="btn btn-primary" onClick={TakeASelfie}>Retry</button>
                    </div>
                </div>
            </div>
        </PageWrapper>
    );
}

export default SelfieFailure;

"use client";

import { useState } from 'react';
import { runFirebaseTests, testFirebaseDatabase, testFirebaseAnalytics } from '../utils/firebaseTest';

export default function FirebaseTestPage() {
    const [testResults, setTestResults] = useState<any>(null);
    const [loading, setLoading] = useState(false);

    // Check if we're in development environment
    const isDevelopment = process.env.NEXT_PUBLIC_ENV === 'development' || process.env.NEXT_PUBLIC_ENV === 'dev';

    // Show 404-like page if not in development
    if (!isDevelopment) {
        return (
            <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '100vh',
                fontFamily: 'Arial, sans-serif',
                textAlign: 'center'
            }}>
                <h1 style={{ fontSize: '4rem', margin: '0', color: '#666' }}>404</h1>
                <h2 style={{ fontSize: '1.5rem', margin: '10px 0', color: '#888' }}>Page Not Found</h2>
                <p style={{ color: '#999' }}>This page is only available in development environment.</p>
            </div>
        );
    }

    const handleRunTests = async () => {
        setLoading(true);
        try {
            const results = await runFirebaseTests();
            setTestResults(results);
        } catch (error) {
            console.error('Test execution error:', error);
            setTestResults({ error: 'Failed to run tests' });
        } finally {
            setLoading(false);
        }
    };

    const handleTestDatabase = async () => {
        setLoading(true);
        try {
            const result = await testFirebaseDatabase();
            setTestResults({ database: result });
        } catch (error) {
            console.error('Database test error:', error);
            setTestResults({ database: { success: false, error } });
        } finally {
            setLoading(false);
        }
    };

    const handleTestAnalytics = () => {
        setLoading(true);
        try {
            const result = testFirebaseAnalytics();
            setTestResults({ analytics: result });
        } catch (error) {
            console.error('Analytics test error:', error);
            setTestResults({ analytics: { success: false, error } });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
            <h1>Firebase Development Environment Test</h1>

            <div style={{ marginTop: '20px', marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
                <h3>Environment Info</h3>
                <p><strong>Environment:</strong> {process.env.NEXT_PUBLIC_ENV || 'unknown'}</p>
                <p><strong>Firebase Project:</strong> {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}</p>
                <p><strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL}</p>
            </div>

            <div style={{ marginBottom: '20px', display: 'flex', gap: '10px', justifyContent: 'center' }}>
                <button
                    onClick={handleRunTests}
                    disabled={loading}
                    style={{
                        padding: '10px 20px',
                        marginRight: '10px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    {loading ? 'Testing...' : 'Run All Tests'}
                </button>

                <button
                    onClick={handleTestDatabase}
                    disabled={loading}
                    style={{
                        padding: '10px 20px',
                        marginRight: '10px',
                        backgroundColor: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    Test Database
                </button>

                <button
                    onClick={handleTestAnalytics}
                    disabled={loading}
                    style={{
                        padding: '10px 20px',
                        backgroundColor: '#ffc107',
                        color: 'black',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: loading ? 'not-allowed' : 'pointer'
                    }}
                >
                    Test Analytics
                </button>
            </div>

            {testResults && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '5px',
                    border: '1px solid #dee2e6'
                }}>
                    <h3>Test Results</h3>
                    <pre style={{
                        backgroundColor: '#343a40',
                        color: '#f8f9fa',
                        padding: '15px',
                        borderRadius: '5px',
                        overflow: 'auto'
                    }}>
                        {JSON.stringify(testResults, null, 2)}
                    </pre>
                </div>
            )}

            <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#d1ecf1', borderRadius: '5px' }}>
                <h3>Instructions</h3>
                <ol>
                    <li>Make sure you have created a <code>.env.local</code> file with your Firebase dev credentials</li>
                    <li>Run the tests to verify Firebase connection</li>
                    <li>Check the browser console for detailed logs</li>
                    <li>Database tests write to <code>test/connection</code> path in Firebase</li>
                    <li>Analytics tests send a <code>test_event</code> to Firebase Analytics</li>
                </ol>
            </div>
        </div>
    );
} 
@import '../scss/variable.scss';

.bankDetails {
    p {
        font-size: 13px;
        font-weight: 500;
        margin-bottom: 8px;
        color: $color-gray;
    }
}

.bankDetailsPage {
    header {
        margin-bottom: 20px;
    }

    form {
        display: flex;
        flex-direction: column;
        min-height: calc(100vh - 190px);
    }
}

.pageContent {
    min-height: calc(100vh - 200px);
}
.iframe {
    min-height: 100vh !important;
    iframe {
        height: 100vh;
    }
}
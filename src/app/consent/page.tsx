'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import '../scss/button.scss';
import style from './bank.module.scss'
import { apiRequest, fetchCommonApi } from '../utils/api';
import store from "../store/store";
import { setCommonData } from "../store/slices/commonSlice";
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';

interface ConsentData {
    fip_ids: string[];
    mobile_number: string;
    organisation_id: string;
    reference_id: string[];
    tracking_id: string;
    data_details: any;
    vua_id: string;
    account_aggregator_id: string;
    return_url?: string;
}

interface RootState {
    bank: {
        consentData: ConsentData;
    };
}

const ConsentCheck = () => {
    const router = useRouter();
    const [oneMoneyUrl, setOneMoneyUrl] = useState<string | null>(null);
    const consentData = { fip_ids: [], mobile_number: '', organisation_id: '', reference_id: [], return_url: '', tracking_id: '', data_details: '', vua_id: '', account_aggregator_id: '' }
    const [status, setStatus] = useState({ monitoring_status: "INITIATED", underwriting_status: "INITIATED" });


    const {
        fip_ids,
        mobile_number,
        organisation_id,
        reference_id,
        tracking_id,
        data_details,
        vua_id,
        account_aggregator_id,
        return_url
    } = consentData;
    useEffect(() => {
        setOneMoneyUrl(localStorage.getItem("ignosisurl")); // Runs only in the browser
      }, []);
    // const oneMoneyUrl =
    //     typeof window !== "undefined" ? localStorage.getItem("ignosisurl") || "" : "";
    // const returnToAppSuccess = `${localStorage.getItem('currenturl')}/loading`;
    // const returnToAppFailure = `${localStorage.getItem('currenturl')}/loading`;

    const onLoaded = () => {
        const iframe = document.getElementById("inner") as HTMLIFrameElement;
        if (!iframe) return;

        // const payload = {
        //     consentHandle: [...reference_id],
        //     mobileNumber: mobile_number,
        //     fipId: fip_ids,
        //     organisationId: organisation_id,
        //     referenceId: reference_id,
        //     trackingId: tracking_id,
        //     dataDetails: data_details,
        //     vuaId: vua_id,
        //     accountAggregatorId: account_aggregator_id
        // };

        // iframe.contentWindow?.postMessage(payload, oneMoneyUrl);

        // const messageHandler = (event: MessageEvent) => {
        //     if (event.origin !== new URL(oneMoneyUrl).origin) return;
        //     if (event.data?.success) {
        //         const { eventCode } = event.data.request || {};

        //         if (eventCode === "INVALID_CONSENT_HANDLE") {
        //             payload.consentHandle = reference_id;
        //         }

        //         if (eventCode === "NO_ACCOUNTS_FOUND" || eventCode === "FIP_FAILED") {
        //             router.back();
        //         }

        //         if (eventCode === "CONSENT_APPROVED_SUCCESS") {
        //             window.location.href = return_url || returnToAppSuccess;
        //         }

        //         if (
        //             eventCode === "CONSENT_REJECTED_SUCCESS" ||
        //             eventCode === "CONSENT_APPROVED_FAILED" ||
        //             eventCode === "CONSENT_REJECTED_FAILED"
        //         ) {
        //             window.location.href = return_url || returnToAppFailure;
        //         }
        //     }
        // };

        // window.addEventListener("message", messageHandler);

        // return () => {
        //     window.removeEventListener("message", messageHandler);
        // };

        window.addEventListener("message", (event) => {
            // debugger;
            console.log({ event });
            if (event.data) {
                const eventCode = event.data.eventCode;
                if (eventCode === "INVALID_CONSENT_HANDLE") {
                    // debugger;
                    router.back();
                    // onemoneyKeys.consentHandle = consentHandle;
                }
                if (eventCode === "NO_ACCOUNTS_FOUND" || eventCode === "FIP_FAILED") {
                    debugger

                    // router.push("/select-bank");
                    router.back();

                }
                if (eventCode === "CONSENT_APPROVED_SUCCESS") {
                    debugger

                    // router.push("/loading");
                    // window.location.href = return_url || returnToAppSuccess;
                    pollData();

                }
                if (
                    eventCode === "CONSENT_REJECTED_SUCCESS" ||
                    eventCode === "CONSENT_APPROVED_FAILED" ||
                    eventCode === "CONSEN_REJECTED_FAILED"
                ) {
                    debugger
                    pollData();
                    // router.push("/loading");
                    // window.location.href = return_url || returnToAppFailure;

                }
            }
        });
    };
    // };

    // useEffect(() => {
    //     return () => {
    //         window.removeEventListener("message", () => { });
    //     };
    // }, []);

    const aapolling = async () => {
        try {
            const payload = {
                "monitoring_reference_id": localStorage.getItem('monitoring_reference_id'),
                "underwriting_reference_id": localStorage.getItem('underwriting_reference_id')
            };

            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.aapolling, payload);

            if (response.mainData) {
                return {
                    monitoring_status: response.mainData?.monitoring_status || "INITIATED",
                    underwriting_status: response.mainData.underwriting_status || "INITIATED",
                };
            }
        } catch (error) {
            console.error("Polling error:", error);
        }
        return { monitoring_status: "INITIATED", underwriting_status: "INITIATED" }; // Return default values
    };

    const pollData = async () => {
        debugger;
        const result = await aapolling();
        setStatus(result);
        if (result.monitoring_status === "FAILED" && result.underwriting_status === "FAILED") {
            // const commonData = await fetchCommonApi();
            // store.dispatch(setCommonData(commonData));
            router.push('/select-bank')


        }

        else if (result.monitoring_status === "COMPLETED" || result.underwriting_status === "COMPLETED"
        ) {
            console.log("Polling stopped - Status changed.");

            // Fetch and store common API data once status changes
            // const commonData = await fetchCommonApi();
            // store.dispatch(setCommonData(commonData));
            router.push('/pollingsuccess')
        }
        else {
            // Keep polling by calling itself again
            setTimeout(() => {
                pollData();
            }, 3000);
        }
    };

    return (
        <div className='external-wrapper verify-bank retrive-statement statement-page'>
            <div className={style.iframe} style={{ minHeight: '100% !important' }}>
                <iframe
                    title="oneMoney"
                    onLoad={onLoaded}
                    width="100%"
                    id="inner"
                    src={oneMoneyUrl || ""}
                    style={{ height: '100% !important' }}
                />
            </div>
        </div>
    );
};

export default ConsentCheck;
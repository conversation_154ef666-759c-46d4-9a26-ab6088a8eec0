'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import styles from './aapolling.module.scss'
import { setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
// For common API call
import { apiRequest, fetchCommonApi } from '../utils/api';
import store from "../store/store";
import { setCommonData } from "../store/slices/commonSlice";
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';
import loadingAnimation from "../../../public/Loader_Red.json"
import dynamic from "next/dynamic";
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
function AAPolling() {
    const dispatch = useDispatch();
    const [status, setStatus] = useState({ monitoring_status: "INITIATED", underwriting_status: "INITIATED" });

    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);

    interface SaveBasicDetailsResponse {
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
        redirection_url: string
    }
    // const aapolling = async () => {
    //     try {

    //         const payload = {
    //             "monitoring_reference_id": localStorage.getItem('monitoring_reference_id'),
    //             "underwriting_reference_id": localStorage.getItem('underwriting_reference_id')
    //         }

    //         const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.aapolling, payload);

    //         if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
    //             debugger;
    //             // Common Api Respoinsegit status
    //             const commonData = await fetchCommonApi();
    //             store.dispatch(setCommonData(commonData));

    //         } else {
    //             debugger;
    //             if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
    //                 debugger;

    //             } else if (response.error) {
    //                 debugger;
    //             } else {
    //             }
    //         }
    //     } catch (error) {
    //     }
    // }
    const aapolling = async () => {
        try {
            const payload = {
                "monitoring_reference_id": localStorage.getItem('monitoring_reference_id'),
                "underwriting_reference_id": localStorage.getItem('underwriting_reference_id')
            };

            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.aapolling, payload);

            if (response.mainData) {
                return {
                    monitoring_status: response.mainData?.monitoring_status || "INITIATED",
                    underwriting_status: response.mainData.underwriting_status || "INITIATED",
                };
            }
        } catch (error) {
            console.error("Polling error:", error);
        }
        return { monitoring_status: "INITIATED", underwriting_status: "INITIATED" }; // Return default values
    };

    useEffect(() => {
        let intervalId: NodeJS.Timeout;
        let elapsedTime = 0; // Track elapsed time
        const maxTime = 60 * 1000; // 1 minute (60,000 ms)
        const pollingInterval = 3000; // 3 seconds

        // wthi timer

        // const pollData = async () => {
        //     if (elapsedTime >= maxTime) {
        //         clearInterval(intervalId);
        //         console.log("Polling stopped after 1 minute.");
        //         return;
        //     }

        //     const result = await aapolling();
        //     if (result) {
        //         setStatus(result); // Update state

        //         if (result.monitoring_status !== "INITIATED" && result.underwriting_status !== "INITIATED") {
        //             clearInterval(intervalId);
        //             console.log("Polling stopped - Status changed.");

        //             // Fetch and store common API data once status changes
        //             const commonData = await fetchCommonApi();
        //             store.dispatch(setCommonData(commonData));
        //         }
        //     }

        //     elapsedTime += pollingInterval;
        // };

        // intervalId = setInterval(pollData, pollingInterval);
        // return () => clearInterval(intervalId); 

        const pollData = async () => {
            debugger;
            const result = await aapolling();
            setStatus(result);
            if (result.monitoring_status === "FAILED" && result.underwriting_status === "FAILED") {
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));

            }
    
            else if (result.monitoring_status === "COMPLETED" || result.underwriting_status === "COMPLETED"
            ) {
                console.log("Polling stopped - Status changed.");
    
                // Fetch and store common API data once status changes
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
            }
            else {
                // Keep polling by calling itself again
                setTimeout(() => {
                    pollData();
                }, 3000);
            }
        };
        pollData();
        return () => {};
    }, []);

    return (
        <div className='external-wrapper'>
            <div className='page-content'>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        {/* <Image src={successImg} alt="Congratulations" /> */}
                        {/* <div className={`lds-ellipsis`}><div></div><div></div><div></div><div></div></div> */}
                        <Lottie
                            animationData={loadingAnimation}
                            loop={true}
                            style={{ height: 100, width: 100 }} // Adjust size as needed
                        />
                        <p>Please Wait...</p>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default AAPolling;

import { configureStore } from '@reduxjs/toolkit';
import bankReducer from './features/bankSlice';
import amountReducer from '../withdraw-amount/withdraw.slice';
import referenceReducer from './features/addressSlice';
import employmentReducer from '../employment-details/employmentSlice';
import registerReducer from '../register/register.slice';
import registercbReducer from '../register-cb/registercb.slice';
import otpReducer from '../verify-email/verify.slice';
import addressReducer from '../address-details/address.slice';
import comAddressReducer from '../communication-address-details/comaddress.slice';
import authReducer from "./slices/authSlice";
import uploadReducer from '../upload-document/upload.slice'
import confirmAddressReducer from '../select-address/confirmaddress.slice'
import loanDetailsReducer from '../loandetail/loanDetails.slice'
import loanDetailsV3Reducer from '../loandetail-v3/loanDetails.slice'
import defenceReducer from '../force-details/defence.slice'
import notaaReducer from '../aa-not-possible/notaa.slice'
import kycReducer from '../kyc-verification/kyc.slice'
import commonReducer from './slices/commonSlice';
import profiosReducer from '../profios/profiosSlice';
import ckycOtpInputReducer from "../ckyc-otp/ckycInputSlice";
import SelectWithdrawReducer from "../post-disbursal/select-withdraw-amount/selectWithdrawAmtSlice";
import IncreaseLimitSuccessReducer from "../post-disbursal/increase-limit-success/increaseLimitSlice";
import loanDetailV3Reducer from "../loandetail-v3/loanDetails.slice";


const store = configureStore({
  reducer: {
    register: registerReducer,
    registercb: registercbReducer,
    bank: bankReducer,
    amount: amountReducer,
    references: referenceReducer,
    employment: employmentReducer,
    otp: otpReducer,
    address: addressReducer,
    comaddress: comAddressReducer,
    auth: authReducer,
    uploaddoc: uploadReducer,
    confirmaddress: confirmAddressReducer,
    loandetails: loanDetailsReducer,
    defence: defenceReducer,
    notaa: notaaReducer,
    kyc: kycReducer,
    common: commonReducer,
    profios: profiosReducer,
    ckycOtp: ckycOtpInputReducer,
    selectWithdrawAmt: SelectWithdrawReducer,
    increaseLimitSuccess: IncreaseLimitSuccessReducer,
    loanDetailV3: loanDetailV3Reducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
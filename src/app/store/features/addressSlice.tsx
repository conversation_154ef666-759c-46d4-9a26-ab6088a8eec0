import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface AddressState {
  startCapture: boolean;
  capturedImage: string | null;
  missingDetails: string | null;
  pageerror: string | null;
  fileerror: string | null;
  fileData: string | null;
  selectedDocType: string | null;
  isUploadModalOpen: boolean;
  uploadedImage: string;
}

// Define the initial state
const initialState: AddressState = {
  startCapture: false,
  capturedImage: null,
  missingDetails: null,
  pageerror: null,
  fileerror: null,
  fileData: null,
  selectedDocType: null,
  isUploadModalOpen: false,
  uploadedImage: '',
};

// Create the slice
const addressSlice = createSlice({
  name: 'address',
  initialState,
  reducers: {
    setStartCapture: (state, action: PayloadAction<boolean>) => {
      state.startCapture = action.payload;
    },
    setCapturedImage: (state, action: PayloadAction<string | null>) => {
      state.capturedImage = action.payload;
    },
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setMissingDetails: (state, action: PayloadAction<string | null>) => {
      state.missingDetails = action.payload;
    },
    setSelectedDocType: (state, action: PayloadAction<string | null>) => {
      state.selectedDocType = action.payload;
    },
    openUploadModal: (state) => {
      state.isUploadModalOpen = true;
    },
    closeUploadModal: (state) => {
      state.fileData = '';
      state.isUploadModalOpen = false;
    },
    setFileError: (state, action: PayloadAction<string | null>) => {
      state.fileerror = action.payload;
    },
    setFileData: (state, action: PayloadAction<string | null>) => {
      state.fileData = action.payload;
    },
    setUploadedFile: (state, action: PayloadAction<string>) => {
      state.uploadedImage = action.payload;
    },
  },
});

// Export actions
export const {
  setStartCapture,
  setCapturedImage,
  setPageError,
  setMissingDetails,
  setSelectedDocType,
  openUploadModal,
  closeUploadModal,
  setFileError,
  setFileData,
  setUploadedFile,
} = addressSlice.actions;

// Export the reducer
export default addressSlice.reducer;

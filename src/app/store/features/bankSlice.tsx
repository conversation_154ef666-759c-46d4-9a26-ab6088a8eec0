import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    selectedBank: '',
    bankList: [],
    error: '',
    accountNumber: '',
    confirmAccountNumber: '',
    perfios_url: '',
    ifscCode: '',
    isChecked: true,
    isModalOpen: false,
    isErrorModalOpen: false,
    bankDetails: null,
    redirectionLink: null,
    isSalaried: null
};

const bankSlice = createSlice({
    name: 'bank',
    initialState,
    reducers: {
        setSelectedBank: (state, action) => {
            state.selectedBank = action.payload;
            state.error = '';
        },
        setError: (state, action) => {
            state.error = action.payload;
        },
        setBankList: (state, action) => {
            state.bankList = action.payload;
        },
        setPerfiosUrl: (state, action) => {
            state.perfios_url = action.payload;
        },
        setAccountNumber: (state, action) => {
            state.accountNumber = action.payload;
        },
        setConfirmAccountNumber: (state, action) => {
            state.confirmAccountNumber = action.payload;
        },
        setIfscCode: (state, action) => {
            state.ifscCode = action.payload;
        },
        toggleChecked: (state) => {
            state.isChecked = !state.isChecked;
        },
        openModal: (state) => {
            state.isModalOpen = true;
        },
        closeModal: (state) => {
            state.isModalOpen = false;
        },
        openErrorModal: (state) => {
            state.isErrorModalOpen = true;
        },
        closeErrorModal: (state) => {
            state.isErrorModalOpen = false;
        },
        setBankDetails: (state, action) => {
            state.bankDetails = action.payload;
        },
        setRedirection: (state, action) => {
            state.redirectionLink = action.payload;
        },
        setIsSalaried: (state, action) => {
            state.selectedBank = ''
            state.isSalaried = action.payload;
        }
    }
});

export const {
    setSelectedBank,
    setError,
    setBankList,
    setPerfiosUrl,
    setAccountNumber,
    setConfirmAccountNumber,
    setIfscCode,
    toggleChecked,
    openModal,
    closeModal,
    openErrorModal,
    closeErrorModal,
    setBankDetails,
    setRedirection,
    setIsSalaried
} = bankSlice.actions;
export default bankSlice.reducer;

export interface ApiResponse {
    signature?: any;
    payload?: any;
    success?: boolean | string;
    status?: boolean | string;
    error_message?: string;
    message?: any;
    data: any;
    address_details?: any;
    source?: any;
    url?: string;
    redirectUrl?: string;
    transaction_id:string;
    monitoring_status:string;
    underwriting_status:string;
    code:any;
    phone?: string;
    step?: string;
    fee?: number | string;
    freedomFee?: {
        fee: number | string;
        total: number | string;
    };
}
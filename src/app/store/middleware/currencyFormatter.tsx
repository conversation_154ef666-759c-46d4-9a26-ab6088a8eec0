import CryptoJS from 'crypto-js';

export const formatCurrency = (num: number | bigint) => {
    return new Intl.NumberFormat('en-IN').format(num);
}

export const generateChecksum = (body: Record<string, any>) => {
    const finalString = Object.keys(body)
        .filter(key => key !== 'checksum')
        .map(key => `${key}=${body[key]}`)
        .join('&');
    const secretKey = process.env.NEXT_PUBLIC_SECRET_WEB_KEY ?? '';
    return CryptoJS.HmacSHA256(finalString, secretKey).toString();
}
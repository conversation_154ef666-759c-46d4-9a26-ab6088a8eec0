import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface AuthState {
  auth_token: string | null;
  device_id: string | null;
}

const initialState: AuthState = {
  auth_token: typeof window !== "undefined" ? localStorage.getItem("auth_token") : null,
  device_id: typeof window !== "undefined" ? localStorage.getItem("device_id") : null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setAuthToken: (state, action: PayloadAction<string | null>) => {
      state.auth_token = action.payload;
      if (action.payload) localStorage.setItem("auth_token", action.payload);
    },
    setDeviceId: (state, action: PayloadAction<string | null>) => {
      state.device_id = action.payload;
      if (action.payload) localStorage.setItem("device_id", action.payload);
    },
  },
});

export const { setAuthToken, setDeviceId } = authSlice.actions;
export default authSlice.reducer;

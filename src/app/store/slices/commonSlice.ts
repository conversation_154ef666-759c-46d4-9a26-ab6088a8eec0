import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface CommonState {
  data: any | null;
}

const initialState: CommonState = {
  data: null,
};

const commonSlice = createSlice({
  name: "common",
  initialState,
  reducers: {
    setCommonData: (state, action: PayloadAction<any>) => {
      state.data = action.payload;
    },
  },
});

export const { setCommonData } = commonSlice.actions;
export default commonSlice.reducer;

'use client'
import React, { useEffect, useState } from 'react';
import styles from './page.module.scss'
import LoadingComp from '../component/loader';
import { initiatePayment } from '../utils/bridge';
import { GetFreedomFee } from '../utils/fetchData';
import { apiRequest } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';
function Page() {

    const [paymentInit, setPaymentInit] = useState(false)
    const [freedomfee, setFreedomfee] = useState<number | string>();

    useEffect(() => {
        const fetchData = async () => {
            const result = await apiRequest<ApiResponse>("GET", ENDPOINTS.getFreedomFee);
            console.log("result", result);
            if (result.mainData?.success) {
                setFreedomfee(result.mainData?.freedomFee?.total)
            }
        };

        fetchData();
    }, []);

    useEffect(() => {
        if (!paymentInit && freedomfee) {
            console.log("freedomfee", freedomfee);
            setPaymentInit(true)
            initiatePayment({
                amount: freedomfee,
                paymentFlag: 29,
                redirection_url_after_poll: `https://${window.location.hostname}/loading`,
            })
        }
    }, [freedomfee])
    return (
        <div className={`external-wrapper ${styles.externalWrapper}`}>
            <LoadingComp />
        </div>
    );
}

export default Page;

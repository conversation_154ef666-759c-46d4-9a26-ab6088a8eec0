@import '../scss/variable.scss';

.loanBanner {
    background-color: $color2;
    border-radius: 12px;
    padding: 17px;
    background-image: url('../images/loan-banner-image.svg');
    background-position: right 5px bottom 6px;
    background-repeat: no-repeat;

    .headingPara {
        color: $color1;
        font-weight: 600;
        font-size: 12px;
        margin-bottom: 15px;
        line-height: 1;
        text-transform: uppercase;
    }

    h2 {
        font-size: 24px;
        color: $color-black;
        font-weight: 700;
        line-height: 34px;
        margin-bottom: 15px;
    }

    .tickedList {
        margin: 0;
        padding: 0;
        list-style: none;
        margin-bottom: 10px;

        li {
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            line-height: 1;

            &::before {
                content: '';
                width: 20px;
                height: 20px;
                position: relative;
                margin-right: 10px;
                display: inline-block;
                background-image: url('../images/tick.svg');
            }

            &:not(:last-of-type) {
                margin-bottom: 15px;
            }
        }
    }
}

.btnRounded {
    width: auto !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    line-height: 1;
    height: 40px !important;
    padding: 0 20px;
}

.widget {
    padding: 20px 12px;
    margin-top: 10px;
    border-radius: 12px;
    min-height: 150px;
    border: 1px solid $color-light;
    background-image: url('../images/mutual-fund.svg');
    background-position: right 10px bottom 15px;
    background-repeat: no-repeat;
    width: 100%;

    .headingPara {
        color: $color1;
        font-weight: 600;
        font-size: 12px;
        margin-bottom: 15px;
        line-height: 1;
        text-transform: uppercase;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .nextIcon {
            cursor: pointer;
        }
    }

    h2 {
        font-weight: 700;
        font-size: 16px;
    }

    &.widgetHalf {
        min-width: auto;
        width: calc(50% - 31px);
        max-width: 50%;
        min-height: 190px;
    }
}

.widgetWrapper {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}
"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './lending.module.scss'
import nextIcon from '../images/next-blue.svg'

function Lending() {
  return (
    <div>
      <PageHeader title="Cash" />
      <div className={styles.loanBanner}>
        <p className={styles.headingPara}>flexi loan</p>
        <h2>Get Flexi Loan up to 5 Lacs</h2>
        <ul className={styles.tickedList}>
          <li>Interest-free for up to 30 days</li>
          <li>Flexible withdrawal</li>
          <li>100% Paperless</li>
        </ul>
        <button className={`btn btn-primary btn-rounded ${styles.btnRounded}`}>Apply now</button>
      </div>
      <div className={styles.widgetWrapper}>
        <div className={styles.widget}>
          <p className={styles.headingPara}>flexi loan <Image src={nextIcon} width={18} height={18} alt="next" className={styles.nexticon} /></p>
          <h2>Get Flexi Loan up to 5 Lacs</h2>
        </div>
        <div className={`${styles.widget} ${styles.widgetHalf}`}>
          <p className={styles.headingPara}>Credit builder <Image src={nextIcon} width={18} height={18} alt="next" className={styles.nexticon} /></p>
          <h2>Use your mutual funds to get instant cash</h2>
        </div>
        <div className={`${styles.widget} ${styles.widgetHalf}`}>
          <p className={styles.headingPara}>credit report <Image src={nextIcon} width={18} height={18} alt="next" className={styles.nexticon} /></p>
          <h2>Check your Credit score for FREE</h2>
        </div>
      </div>
    </div>
  )
}

export default Lending
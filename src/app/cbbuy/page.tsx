"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './cbbuy.module.scss'
import cashblack from '../images/cashback.svg'
import flash from '../images/flash.svg'
import lowcibil from '../images/lowcibil.svg'
import calenderoutline from '../images/calendaroutline.svg'
import document from '../images/document.svg'
import { useThemeColor } from '../hooks/useThemeColor'
import { ENDPOINTS } from '../utils/endpoints';
import store, { RootState } from '../store/store';
import { setCommonData } from '../store/slices/commonSlice';
import { useDispatch, useSelector } from "react-redux"
import { apiRequest, fetchCommonApi } from "../utils/api"
import { setPageError } from '../register/register.slice';
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { useEffect, useState } from "react"
import CtEvents from "../utils/Ctevents"



function CbLandingPage() {
  const dispatch = useDispatch();
  const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
  interface SaveBasicDetailsResponse {
    success: boolean | string;
    error_message?: string;
    data: Record<string, any>;
    landing_page: string,
    status: any,
    plan_id: string;
    amount: string;
    is_recommended: string;
    recommended_text: string;
    emi: string;
    fee: string;
    what_to_do_points: string[];
  }
  const [plans, setPlans] = useState<SaveBasicDetailsResponse[]>([]);
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

  const [activePlan, setActivePlan] = useState<SaveBasicDetailsResponse | null>(null);


  const GetCbBuyPlan = async () => {
    try {
      const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.get_cb_plan);

      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
        const allPlans = response.mainData.data.plans;
        const recommended = allPlans.find((plan: { is_recommended: string }) => plan.is_recommended === "true");
        // setPlans(response.mainData.data.plans)
        setPlans(allPlans);
        setActivePlan(recommended || allPlans[0]);

      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }
        else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
          // debugger;
          dispatch(setPageError(response.mainData.error_message as string | "null"))
        } else if (response.error) {
        } else {
        }
      }
    } catch (error) {
    }
  };

  useThemeColor('#FFE7E7');
  useEffect(() => {


    GetCbBuyPlan();
  }, [])

  const SaveCbPlan = async () => {
    const buyamount = parseInt(activePlan?.amount ?? "0", 10);
    const plainid = parseInt(activePlan?.plan_id ?? "0", 10);
    const payload = {
      plan_id: plainid,
      amount: buyamount
    }
    localStorage.setItem('cb_plan_id', activePlan?.plan_id || '0')
    localStorage.setItem('buyamount', activePlan?.amount || '0')
    try {
      const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.save_cb_plan, payload);

      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {

        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_plan_screen", "Product category": 'cb', "CTA": "Buy Credit Builder", "plan": buyamount, "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })

        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));

      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }
        else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
          // debugger;
          dispatch(setPageError(response.mainData.error_message as string | "null"))
        } else if (response.error) {
        } else {
        }
      }
    } catch (error) {
    }
  };
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code")

    const event_property = { "Screen Name": "cb_plan_screen", "Product category": 'cb', "Source": cteventsfromstorage?.source }
    setCtdata({ event_name, event_property })
  }, [])
  return (
    <div>
      <div className={styles.header}>
        <PageHeader title="" />
        <div className={styles.headercont}>
          <div className={styles.select}>
            <p className={styles.congotxt}>
              Build your credit score in a <br />
              2 EMI loan with credit
              builder fee of ₹{Math.round(Number(activePlan?.fee) || 0)}
            </p>
          </div>
          <div className={styles.icon}>
            <Image src={lowcibil} alt="" />
          </div>
        </div>
      </div>

      <div className={styles.body}>
        {/* Plan Tabs */}
        <div className={styles.choosebox}>
          <p>Choose your loan amount</p>
          <div className={styles.tabs}>
            {plans.map((plan) => (
              <div
                key={plan.plan_id}
                className={`${styles.tab} ${plan.plan_id === activePlan?.plan_id ? styles.active : ""
                  } ${plan.recommended_text ? styles.recommended : ""}`}
                onClick={() => setActivePlan(plan)}
              >
                ₹{Number(plan.amount).toLocaleString('en-IN')}
              </div>
            ))}
          </div>
        </div>

        {/* Credit Builder Steps */}
        {activePlan && (
          <div className={styles.creditbuilderbox}>
            <div className={styles.title}>What to do</div>
            <div className={styles.steps}>
              {activePlan.what_to_do_points.map((point, index) => {
                let formattedPoint = point;

                // 1. Format existing ₹ amounts (round + commas)
                formattedPoint = formattedPoint.replace(/₹([\d.,]+)/g, (match, amountStr) => {
                  const num = Number(amountStr.replace(/,/g, ''));
                  if (isNaN(num)) return match;
                  return `₹${num.toLocaleString('en-IN', { maximumFractionDigits: 0 })}`;
                });

                // 2. Add ₹ before standalone amounts (e.g., "get back 2800" → "get back ₹2,800")
                formattedPoint = formattedPoint.replace(/\b(\d{4,6})\b/g, (match) => {
                  const num = Number(match);
                  if (isNaN(num)) return match;
                  return `₹${num.toLocaleString('en-IN')}`;
                });

                return (
                  <div key={index}>
                    <Image
                      src={
                        index === 0
                          ? cashblack
                          : index === 1
                            ? flash
                            : index === 2
                              ? calenderoutline
                              : document
                      }
                      alt=""
                    />
                    <p dangerouslySetInnerHTML={{ __html: formattedPoint }}></p>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        <div className={styles.higher}>
          Higher amount Credit Builder loan has more impact on Cibil score
        </div>

        <div className={`footerbottombtn ${styles.footerbottombtn}`} onClick={SaveCbPlan}>
          Buy Credit Builder
        </div>
      </div>

      {pageerror || pagesuccess ? (
        <ToastMessage color={pagesuccess ? "green" : "red"}>
          {pagesuccess ? pagesuccess : pageerror}
        </ToastMessage>
      ) : null}
      <CtEvents data={ctData} />

    </div>
  )
}

export default CbLandingPage

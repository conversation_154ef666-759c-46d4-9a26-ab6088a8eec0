'use client';

import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import ShieldIcon from '../images/shield-icon.svg';
import SelectDropdown from '../components/SelectDropdown';
import Image from 'next/image';
import store, { AppDispatch, RootState } from '../store/store';
import { useDispatch, useSelector } from 'react-redux';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { setPageError, setPageSuccess } from '../register/register.slice';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import React, { useEffect, useState } from 'react';
import { ENDPOINTS } from '../utils/endpoints';
import PageWrapper from '../components/PageWrapper';
import styles from './page.module.scss'
import { setCommonData } from '../store/slices/commonSlice';
import LoadingComp from '../component/loader';
import { useRouter } from "next/navigation";
import { pushCleverTapEvent } from '../utils/cleverTapClient';


function PersonalDetailsSalaried() {
    const router = useRouter();

    const dispatch = useDispatch<AppDispatch>();
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [loaded, setLoaded] = useState<boolean>(false)
    const [personalDetailData, setPersonalDetailData] = useState<any>(null)
    const [cteventsfromstorage, setCteventsfromstorage] = useState<any>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const eventsData = JSON.parse(window.localStorage.getItem("eventsdata") || "null");
      setCteventsfromstorage(eventsData);
    }
  }, []);

    const validationSchema = Yup.object({
        fatherName: Yup.string()
            .nullable()
            .test(
                "fatherName-not-na",
                "Father's Name is required",
                (value) => !!value // Check if value exists
            )
            .test(
                "fatherName-alphabetic",
                "Father's Name must contain only alphabetic characters",
                (value) => !value || /^[A-Za-z\s]+$/.test(value)
            ),
        emailId: Yup.string()
            .nullable()
            .matches(
                /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                "Enter a valid email ID"
            )
            .required("Email ID is required"),
        /*occupationType: Yup.string()
            .required("Occupation type is required"),
        companyName: Yup.string()
            .nullable()
            .test(
                "companyName-required-if-salaried",
                "Company Name is required for salaried occupation",
                (value, context) => {
                    const occupationType = context.parent.occupationType;
                    if (occupationType === "Salaried") {
                        return !!value; // Require companyName if occupationType is "Salaried"
                    }
                    return true;
                }
            ),
        salaryDate: Yup.string()
            .nullable()
            .test(
                "salaryDate-required-if-salaried",
                "Salary Date is required for salaried occupation",
                (value, context) => {
                    const occupationType = context.parent.occupationType;
                    if (occupationType === "Salaried") {
                        return !!value; // Require salaryDate if occupationType is "Salaried"
                    }
                    return true;
                }
            ),
        entityName: Yup.string()
            .nullable()
            .test(
                "entityName-required-if-self-employed",
                "Entity Name is required for self-employed occupation",
                (value, context) => {
                    const occupationType = context.parent.occupationType;
                    if (occupationType === "Self Employed/Business") {
                        return !!value; // Require entityName if occupationType is "Self Employed/Business"
                    }
                    return true;
                }
            ),
        annualIncome: Yup.string()
            .nullable()
            .required("Annual Income is required"),*/
    });
    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
    }

    useEffect(() => {
        if(cteventsfromstorage){
        pushCleverTapEvent({
            eventName: "Screen View",
            eventData: {
                "Screen Name": "LOC_Additional Details Submiited",
                "Product category": cteventsfromstorage.productcode,
                "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP",
                "Source": cteventsfromstorage?.source
                }
            })
        }
    }, [cteventsfromstorage])

    useEffect(() => {
        const getPersonalDetails = async () => {
            try {
                const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.personalDetail);

                if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                    // dispatch(setPageSuccess('Data updated'))

                    setPersonalDetailData(response.mainData?.data)
                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }

                    else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            } catch (error) {

                dispatch(setPageError('Error in fetching basic details'))
            }
        }
        getPersonalDetails()
    }, [])
    const formik = useFormik({
        initialValues: {
            emailId: '',
            fatherName: '',
        },
        validationSchema,
        onSubmit: async (values) => {
            setLoaded(false)
            try {
                const payload = {
                    father_name: values.fatherName,
                    email: values.emailId.toLowerCase(),
                };
                const response = await apiRequest<SaveBasicDetailsResponse>("PUT", ENDPOINTS.updateadditionaldetails, payload);

                if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                    const event_name = 'Button Clicked'
                    const event_property = { "CTA": "Continue","Page Name": "LOC_Additional Details Submiited", "Product category": cteventsfromstorage.productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP" }
                    pushCleverTapEvent({eventName: event_name, eventData: event_property})

                    const loc_event_name = 'LOC_Additional Details Submiited'
                    const loc_event_property = { "Personal Email ID": values.emailId, "Fathers Name": values.fatherName, "Product category": cteventsfromstorage.productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP", "Source": cteventsfromstorage.source }
                    pushCleverTapEvent({eventName: loc_event_name, eventData: loc_event_property})

                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }

                    else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            } catch (error) {


                //dispatch(setPageError('Error submitting basic details'))
                //console.error("Error submitting basic details:", error);
                //alert("Failed to save basic details. Please try again.");
            } finally{
                setTimeout(() => {
                    setLoaded(true)
                }, 2000);
            }
        },
    });
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    return (
        <PageWrapper>
            {loaded ?
                <div className={`registration-page ${styles.personalDetails}`}>
                    <PageHeader title={`Hello ${personalDetailData?.first_name || ''}`} para="Please provide these to help us finalise your loan." />
                    <div className="page-content">
                        <form onSubmit={formik.handleSubmit}>

                            {/* Email ID */}
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    autoComplete="off"
                                    name="emailId"
                                    placeholder=" "
                                    value={formik.values.emailId}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>Personal Email ID</label>
                                {formik.errors.emailId && formik.touched.emailId && (
                                    <div className="error">{formik.errors.emailId}</div>
                                )}
                            </div>

                            {/*<div className="input-wrapper radio-wrapper">
                        <label className="radio-label">Occupation type</label>
                        <div className="radios">
                            <div className="radio">
                                <input
                                    type="radio"
                                    id="salaried"
                                    name="occupationType"
                                    value="Salaried"
                                    checked={formik.values.occupationType === 'Salaried'}
                                    onChange={() => formik.setFieldValue('occupationType', 'Salaried')}
                                />
                                <label htmlFor="salaried" className="noabs">Salaried</label>
                            </div>
                            <div className="radio">
                                <input
                                    type="radio"
                                    id="selfEmployed"
                                    name="occupationType"
                                    value="Self Employed/Business"
                                    checked={formik.values.occupationType === 'Self Employed/Business'}
                                    onChange={() => formik.setFieldValue('occupationType', 'Self Employed/Business')}
                                />
                                <label htmlFor="selfEmployed" className="noabs">Self Employed/Business</label>
                            </div>
                        </div>
                        {formik.errors.occupationType && formik.touched.occupationType && (
                            <div className="error">{formik.errors.occupationType}</div>
                        )}
                    </div>

                    {formik.values.occupationType === 'Salaried' && (
                        <>
                            <div className="input-wrapper">
                                <SelectDropdown
                                    name="companyName"
                                    options={['Company A', 'Company B', 'Company C']} // Example options
                                    labelText="Company Name"
                                    value={formik.values.companyName}
                                    onChange={(option) => formik.setFieldValue('companyName', option)}
                                />
                                {formik.errors.companyName && formik.touched.companyName && (
                                    <div className="error">{formik.errors.companyName}</div>
                                )}
                            </div>
                            <div className="input-wrapper">
                                <SelectDropdown
                                    name="salaryDate"
                                    options={['1st', '15th', 'Last Day']}
                                    labelText="Salary Date"
                                    value={formik.values.salaryDate}
                                    onChange={(option) => formik.setFieldValue('salaryDate', option)}
                                />
                                {formik.errors.salaryDate && formik.touched.salaryDate && (
                                    <div className="error">{formik.errors.salaryDate}</div>
                                )}
                            </div>
                        </>
                    )}
                    {formik.values.occupationType === 'Self Employed/Business' && (
                        <div className="input-wrapper">
                            <input
                                type="text"
                                name="entityName"
                                placeholder=" "
                                value={formik.values.entityName}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="form-control"
                            />
                            <label>Entity Name</label>
                            {formik.errors.entityName && formik.touched.entityName && (
                                <div className="error">{formik.errors.entityName}</div>
                            )}
                        </div>
                    )}

                    <div className="input-wrapper radio-wrapper">
                        <label className="radio-label">Annual Income</label>
                        <div className="radios">
                            <div className="radio">
                                <input
                                    type="radio"
                                    id="income1"
                                    name="annualIncome"
                                    value="3 - 5 Lacs"
                                    checked={formik.values.annualIncome === '3 - 5 Lacs'}
                                    onChange={() => formik.setFieldValue('annualIncome', '3 - 5 Lacs')}
                                />
                                <label htmlFor="income1" className='noabs'>3 - 5 Lacs</label>
                            </div>
                            <div className="radio">
                                <input
                                    type="radio"
                                    id="income2"
                                    name="annualIncome"
                                    value="5 - 10 Lacs"
                                    checked={formik.values.annualIncome === '5 - 10 Lacs'}
                                    onChange={() => formik.setFieldValue('annualIncome', '5 - 10 Lacs')}
                                />
                                <label htmlFor="income2" className='noabs'>5 - 10 Lacs</label>
                            </div>
                            <div className="radio">
                                <input
                                    type="radio"
                                    id="income3"
                                    name="annualIncome"
                                    value="10 - 15 Lacs"
                                    checked={formik.values.annualIncome === '10 - 15 Lacs'}
                                    onChange={() => formik.setFieldValue('annualIncome', '10 - 15 Lacs')}
                                />
                                <label htmlFor="income3" className='noabs'>10 - 15 Lacs</label>
                            </div>
                            <div className="radio">
                                <input
                                    type="radio"
                                    id="income4"
                                    name="annualIncome"
                                    value="15+ Lacs"
                                    checked={formik.values.annualIncome === '15+ Lacs'}
                                    onChange={() => formik.setFieldValue('annualIncome', '15+ Lacs')}
                                />
                                <label htmlFor="income4" className='noabs'>15+ Lacs</label>
                            </div>
                        </div>
                        {formik.errors.annualIncome && formik.touched.annualIncome && (
                            <div className="error">{formik.errors.annualIncome}</div>
                        )}
                    </div>*/}

                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    autoComplete="off"
                                    name="fatherName"
                                    placeholder=" "
                                    value={formik.values.fatherName}
                                    onChange={(e) => {
                                        const value = e.target.value.replace(/[^A-Za-z\s]/g, '');
                                        formik.setFieldValue('fatherName', value);
                                    }}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>Father's Name (as per your PAN)</label>
                                {formik.errors.fatherName && formik.touched.fatherName && (
                                    <div className="error">{formik.errors.fatherName}</div>
                                )}
                            </div>

                            {/* Footer */}
                            <div className="bottom-footer p-0 mt-auto">
                                <p className="secure-tag">
                                    <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                </p>
                                <button type="submit" className={`btn btn-primary mb-0 ${!formik.isValid || !formik.dirty ? 'disabled' : ''}`}>Continue</button>
                            </div>
                        </form>
                    </div>

                </div>
                :

                <LoadingComp />
            }
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
        </PageWrapper>
    );
}

export default PersonalDetailsSalaried;

.header {

    header {
        min-height: auto;
        padding: 0;
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 0;
        margin-bottom: 0;
        background-size: 100% calc(100% - 5px);
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;
    min-height: calc(100vh - 90px);
    display: flex;
    flex-flow: column;

    >h1 {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 24px;
        line-height: 28.8px;
        color: #000;
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30px;

        >img {
            width: 70px;
            height: 70px;
        }
    }

    >p {
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        margin-top: 16px;
        color: var(--black);
    }

    .box {
        border: 1px solid #E6E6E6;
        border-radius: 12px;
        display: flex;
        padding: 16px;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        align-items: center;
        background: #F5F5F5;
        position: relative;

        .icon {
            position: absolute;
            right: 14px;
            top: 14px;
            width: 24px;
            height: 24px;

            img {
                width: 24px;
                height: 24px;
            }
        }

        >div {
            display: flex;
            flex-flow: column;
            align-items: flex-start;
            width: calc(100% - 85px);

            p {
                display: flex;
                align-items: center;
                font-family: var(--font-mona-sans);
                font-weight: 700;
                font-size: 16px;
                line-height: 16px;
                color: var(--black);

                img {
                    margin-left: 5px;
                    width: 49px;
                    height: 12px;
                }
            }

            span {
                margin: top 6px;
                font-family: var(--font-mona-sans);
                font-weight: 400;
                font-size: 14px;
                line-height: 16px;
                color: var(--black);
                padding-right: 20px;
            }
        }

        >img {
            min-width: 75px;
            max-width: 75px;
            height: auto;
            margin-right: 10px;
        }
    }

    .fetchthese {
        margin-top: 30px;
        color: #000;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        font-size: 16px;
        line-height: 20px;
        margin-bottom: 14px;
    }

    .termsbox {
        margin-top: 30px;
        display: flex;
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;

        a {
            color: var(--primary-color);
        }

        // input {
        //     display: none;

        label {
            display: flex;
            align-items: flex-start;

            span {
                display: flex;
                width: 18px;
                height: 18px;
                background: url(../images/ablackuncheck.png) no-repeat;
                margin-right: 5px;
                background-size: 100%;
            }

            &+img {
                margin-left: 5px;
                cursor: pointer;
            }

            input {
                display: none;

                &:checked {
                    &+span {
                        background: url(../images/ablackcheck.png) no-repeat;
                        background-size: 100%;
                    }
                }
            }
        }




        // }
    }

    .footerbottombtn {
        margin-top: auto;

        .datasafety {
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            color: var(--black);

            img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }
        }

        .btn {
            margin-top: 20px;
        }
    }
}

.consent {
    margin-top: 30px;
}
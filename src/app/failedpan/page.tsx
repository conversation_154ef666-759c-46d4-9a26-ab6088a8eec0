"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './failedpan.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import greentick from '../images/greentick.png'
import pan from '../images/pan.png'
import aadhar from '../images/aadhar.svg'
import warning from '../images/warning.png'
import shield from '../images/shield-icon.svg'
import { useEffect, useState } from "react"
import React from "react"
import { apiRequest } from '../utils/api';
import { ENDPOINTS } from "../utils/endpoints"
import PageWrapper from "../components/PageWrapper"
import { useDispatch, useSelector } from "react-redux"
import { RootState } from "../store/store"
import { setIsChecked } from "../register/register.slice"
import '../scss/form.scss'


function FailedpanPage() {
  const [data, setdata] = useState([] as any)
  const { isChecked } = useSelector((state: RootState) => state.register)
  const dispatch = useDispatch()

  useThemeColor('#fff')
  interface SaveBasicDetailsResponse {
    success: boolean | string;
    error_message?: string;
    data: Record<string, any>; // Adjust according to the actual data structure you expect
  }
  useEffect(() => {
    getDocs()

  }, [])
  const getDocs = async () => {

    try {

      // Pass SaveBasicDetailsResponse as the type parameter
      const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.documentlist);
      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
        setdata(response.mainData.data)
        if (response.commonData.lead_history.at(-1).step_name == "SENTINEL_BASIC_DETAILS_1") {
          // dispatch(openForceModal())
        }
      } else {
        
        if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
        } else if (response.error) {
        } else {
        }
      }
    } catch (error) {
      //console.error("Error submitting basic details:", error);
      //alert("Failed to save basic details. Please try again.");
    }

  }
  return (
    <PageWrapper>
      <div className={styles.header}>
        <PageHeader title="" />
      </div>
      <div className={styles.body}>
        <h1>Available documents
        </h1>
        {data[0]?.docName == "aadhaar" &&
          <div className={styles.box}>
            <Image src={aadhar} alt="rentagreement" />
            <div className={styles.icon}>
              <Image src={greentick} alt="" />
            </div>
            <div>
              <p>Aadhaar Card </p>
              <span>Fetched Successfully</span>
            </div>

          </div>
        }
        <div className={styles.fetchthese}>
          Fetch these documents
        </div>
        <div className={styles.box}>
          <Image src={pan} alt="rentagreement" />
          <div className={styles.icon}>
            <Image src={warning} alt="" />
          </div>
          <div>
            <p>PAN </p>
            <span>Get your PAN securely
              from Income Tax depatment</span>
          </div>

        </div>
        <div className={`consent-check ${styles.consent}`}><label><div className={`checkbox ${isChecked ? 'checked' : ''}`}>
          <input type="checkbox" checked={isChecked} onChange={() => {
            dispatch(setIsChecked(!isChecked))
          }} />
        </div>
          <p>I provide my consent to share my Aadhaar Number, date of birth and name from my Aadhaar eKYC information with the income tax department for the purpose of fetching my PAN verification record into AKARA.</p></label></div>
        <div className='bottom-footer mt-auto'>
          <p className="secure-tag">
            <Image src={shield} alt="" /> Your data is 100% safe & secure
          </p>
          <button className={`btn btn-primary mb-0`}>
            Get PAN Now!
          </button>
        </div>

      </div>
    </PageWrapper>
  )
}

export default FailedpanPage
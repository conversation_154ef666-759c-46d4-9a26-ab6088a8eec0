'use client';

import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import SelectDropdown from '../components/SelectDropdown';
import ShieldIcon from '../images/shield-icon.svg';
import Image from 'next/image';
import styles from './reference.module.scss'
import { setPageError, setPageSuccess } from '../register/register.slice';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { useDispatch, useSelector } from 'react-redux';
import store, { AppDispatch, RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { ENDPOINTS } from '../utils/endpoints';
import { getRelation } from '../utils/fetchData';
import PageWrapper from '../components/PageWrapper';
import { setCommonData } from '../store/slices/commonSlice';
import LoadingComp from '../component/loader';
import { useRouter } from "next/navigation";


const relationshipOptions = ['Family', 'Friend', 'Colleague', 'Other'];

// Extend the Navigator interface to include the ContactsManager API
declare global {
    interface Navigator {
        contacts?: {
            select: (properties: string[], options: { multiple: boolean }) => Promise<Array<{ name?: string[]; tel?: string[] }>>;
        };
    }
}
interface Data {
    ref1: string[];
    ref2: string[];
}
function References() {
    const router = useRouter();

    const dispatch = useDispatch<AppDispatch>();
    const [data, setData] = useState<Data | null>(null);
    const [loaded, setLoaded] = useState<boolean>(false)


    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
    }
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);

    const handleConfirm = async (values: any) => {
        setLoaded(false)
        try {
            const payload = {
                // "customer_id": 58912863,
                "references": [
                    {
                        "name": values.customer_refrence1,
                        "mobile": values.refrence_mobile1.replace(/\s+/g, ''),
                        "relation": values.relationship1
                    },
                    {
                        "name": values.customer_refrence2,
                        "mobile": values.refrence_mobile2.replace(/\s+/g, ''),
                        "relation": values.relationship2
                    }
                ]
            };

            // Pass SaveBasicDetailsResponse as the type parameter
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.addloanreference, payload);
            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {

                dispatch(setPageSuccess('Data updated'))
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                    dispatch(setPageError(response.mainData.error_message || ''))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
        } catch (error) {

            //dispatch(setPageError('Error submitting basic details'))
            //console.error("Error submitting basic details:", error);
            //alert("Failed to save basic details. Please try again.");
        } finally{
            setTimeout(() => {
                setLoaded(true)
            }, 2000);
        }
    };

    const formik = useFormik({
        initialValues: {
            customer_refrence1: '',
            refrence_mobile1: '',
            relationship1: '',
            customer_refrence2: '',
            refrence_mobile2: '',
            relationship2: '',
        },
        validationSchema: Yup.object({
            customer_refrence1: Yup.string()
                .required('Reference name is required')
                .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed'),
            refrence_mobile1: Yup.string()
                .required('Contact Number is required')
                .matches(/^\d{5} \d{5}$/, 'Must be in 5-digit chunks (e.g., 12345 67890)')
                .max(11, 'Contact Number cannot exceed 10 digits'),
            relationship1: Yup.string().required('Relationship is required'),
            customer_refrence2: Yup.string()
                .required('Reference name is required')
                .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed'),
            refrence_mobile2: Yup.string()
                .required('Contact Number is required')
                .matches(/^\d{5} \d{5}$/, 'Must be in 5-digit chunks (e.g., 12345 67890)')
                .max(11, 'Contact Number cannot exceed 10 digits'),
            relationship2: Yup.string().required('Relationship is required'),
        }),
        onSubmit: handleConfirm,
    });

    const pickContact = async (type: string) => {
        if (navigator.contacts && navigator.contacts.select) {
            try {
                const props = ['name', 'tel'];
                const contacts = await navigator.contacts.select(props, { multiple: false });

                if (contacts.length > 0) {
                    const selectedContact = contacts[0];
                    const contactName = (selectedContact.name?.[0] || '').replace(/[^A-Za-z\s]/g, '');
                    const contactTel = (selectedContact.tel?.[0] || '')
                        .replace(/^(\+91|0)/, '')
                        .replace(/[^0-9]/g, '');

                    if (type === 'type1') {
                        formik.setFieldValue('customer_refrence1', contactName);
                        formik.setFieldValue('refrence_mobile1', contactTel);
                        formik.setFieldTouched('customer_refrence1', false);
                        formik.setFieldTouched('refrence_mobile1', false);
                    } else if (type === 'type2') {
                        formik.setFieldValue('customer_refrence2', contactName);
                        formik.setFieldValue('refrence_mobile2', contactTel);
                        formik.setFieldTouched('customer_refrence2', false);
                        formik.setFieldTouched('refrence_mobile2', false);
                    }
                } else {
                    // Clear errors if the user skips the contact picker
                    if (type === 'type1') {
                        formik.setErrors({
                            ...formik.errors,
                            customer_refrence1: undefined,
                            refrence_mobile1: undefined,
                        });
                    } else if (type === 'type2') {
                        formik.setErrors({
                            ...formik.errors,
                            customer_refrence2: undefined,
                            refrence_mobile2: undefined,
                        });
                    }
                }
            } catch (error) {
                console.error('Error picking contact:', error);
                // Optionally show an error message to the user
            }
        } else {
            console.warn('Contacts Picker API not supported on this browser.');
            // Optionally show a warning message to the user
        }
    };
    useEffect(() => {
        const fetchData = async () => {
            const result = await getRelation();
            const newData: Data = result;
            setData(newData);
        };
        fetchData();
    }, []);
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    return (
        <PageWrapper>
            {loaded ?
                <div className="external-wrapper employment-details">
                    <PageHeader title='Add your references' para="Enter the details of two reliable contacts " />
                    <div className="page-content">
                        <form onSubmit={formik.handleSubmit}>
                            {/* Reference 1 */}
                            <div className="reference-box">
                                <p className={styles.referenceHeading}>Reference 1</p>

                                <div className="input-wrapper">
                                    <SelectDropdown
                                        name="relationship1"
                                        id="relationship1"
                                        options={data?.ref1 || []}
                                        labelText="Relationship"
                                        value={formik.values.relationship1}
                                        onChange={(option) => formik.setFieldValue('relationship1', option)}
                                    />
                                    {formik.errors.relationship1 && formik.touched.relationship1 && (
                                        <div className="error">{formik.errors.relationship1}</div>
                                    )}
                                </div>
                                <div className="input-wrapper over-text">
                                    <input
                                        type="text"
                                        name="customer_refrence1"
                                        placeholder="Name"
                                        value={formik.values.customer_refrence1}
                                        onChange={(e) =>
                                            formik.setFieldValue(
                                                'customer_refrence1',
                                                e.target.value.replace(/[^A-Za-z\s]/g, '')
                                            )
                                        }
                                        onBlur={formik.handleBlur}
                                        className="form-control"
                                    />
                                    <div className={styles.contactPicker} onClick={() => pickContact('type1')}>Add contact</div>
                                    {formik.errors.customer_refrence1 && formik.touched.customer_refrence1 && (
                                        <div className="error">{formik.errors.customer_refrence1}</div>
                                    )}
                                </div>

                                <div className="input-wrapper">
                                    <input
                                        type="text"
                                        name="refrence_mobile1"
                                        placeholder="Contact Number"
                                        maxLength={11}
                                        value={formik.values.refrence_mobile1}
                                        onChange={(e) => {
                                            formik.setFieldValue(
                                                'refrence_mobile1',
                                                e.target.value.replace(/[^0-9]/g, '').match(/.{1,5}/g)?.join(' ') || ''
                                            )
                                            if (e.target.value?.length === 11) {
                                                setTimeout(() => e.target.blur(), 100)
                                            }
                                        }
                                        }
                                        onBlur={formik.handleBlur}
                                        className="form-control"
                                    />
                                    {formik.errors.refrence_mobile1 && formik.touched.refrence_mobile1 && (
                                        <div className="error">{formik.errors.refrence_mobile1}</div>
                                    )}
                                </div>
                            </div>

                            {/* Reference 2 */}
                            <div className="reference-box">
                                <p className={styles.referenceHeading}>Reference 2</p>

                                <div className="input-wrapper">
                                    <SelectDropdown
                                        name="relationship2"
                                        id="relationship2"
                                        options={data?.ref2 || []}
                                        labelText="Relationship"
                                        value={formik.values.relationship2}
                                        onChange={(option) => formik.setFieldValue('relationship2', option)}
                                    />
                                    {formik.errors.relationship2 && formik.touched.relationship2 && (
                                        <div className="error">{formik.errors.relationship2}</div>
                                    )}
                                </div>
                                <div className="input-wrapper over-text">
                                    <input
                                        type="text"
                                        name="customer_refrence2"
                                        placeholder="Name"
                                        value={formik.values.customer_refrence2}
                                        onChange={(e) =>
                                            formik.setFieldValue(
                                                'customer_refrence2',
                                                e.target.value.replace(/[^A-Za-z\s]/g, '')
                                            )
                                        }
                                        onBlur={formik.handleBlur}
                                        className="form-control"
                                    />
                                    <div className={styles.contactPicker} onClick={() => pickContact('type2')}>Add contact</div>
                                    {formik.errors.customer_refrence2 && formik.touched.customer_refrence2 && (
                                        <div className="error">{formik.errors.customer_refrence2}</div>
                                    )}
                                </div>

                                <div className="input-wrapper">
                                    <input
                                        type="text"
                                        name="refrence_mobile2"
                                        placeholder="Contact Number"
                                        maxLength={11}
                                        value={formik.values.refrence_mobile2}
                                        onChange={(e) => {
                                            formik.setFieldValue(
                                                'refrence_mobile2',
                                                e.target.value.replace(/[^0-9]/g, '').match(/.{1,5}/g)?.join(' ') || ''
                                            )
                                            if (e.target.value?.length === 11) {
                                                setTimeout(() => e.target.blur(), 100);
                                            }
                                        }
                                        }
                                        onBlur={formik.handleBlur}
                                        className="form-control"
                                    />
                                    {formik.errors.refrence_mobile2 && formik.touched.refrence_mobile2 && (
                                        <div className="error">{formik.errors.refrence_mobile2}</div>
                                    )}
                                </div>
                            </div>
                            <p className={styles.info}>References may be contacted for verification of loan application or when you are not reachable for repayment of your dues.</p>

                            <div className="bottom-footer ps-0 pe-0">
                                <p className="secure-tag">
                                    <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                </p>
                                <button
                                    type="submit"
                                    className={`btn btn-primary ${!formik.isValid || !formik.dirty ? 'disabled' : ''}`}
                                >
                                    Continue
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                :

                <LoadingComp />
            }
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
        </PageWrapper>
    );
}

export default References;

'use client';

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import BottomPopup from '../components/popups/BottomPopup';
import ShieldIcon from '../images/shield-icon.svg';
import { closeModal, openModal, setAccountNumber, setConfirmAccountNumber, setIfscCode } from '../store/features/bankSlice';
import Image from 'next/image';
import store, { RootState } from '../store/store';
import { ENDPOINTS } from '../utils/endpoints';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { setPageError } from '../register/register.slice';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import styles from './bank.module.scss'
import PageWrapper from '../components/PageWrapper';
import { setCommonData } from '../store/slices/commonSlice';
import { useSearchParams } from "next/navigation";
import LoadingComp from '../component/loader';
import { useRouter } from "next/navigation";


const BankDetailsForm = () => {
    const router = useRouter();
    const dispatch = useDispatch();
    const searchParams = useSearchParams();
    const bank_id = searchParams.get(`bank_id`);
    const [loaded, setLoaded] = useState<boolean>(false)
    const [addressdata, setaddressdata] = useState<any>([])


    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);

    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        landing_page: string,
        bank_address: any
    }
    const {
        accountNumber,
        confirmAccountNumber,
        ifscCode,
        isChecked,
        isModalOpen,
        bankDetails,
    } = useSelector((state: any) => state.bank);

    const formik = useFormik({
        initialValues: {
            accountNumber,
            confirmAccountNumber,
            ifscCode,
        },
        validationSchema: Yup.object({
            accountNumber: Yup.string()
                .required('Bank account number is required')
                // .matches(/^\d{1,4}( \d{1,4}){0,3}$/, 'Only numbers are allowed and must be grouped in blocks of 4')
                .test(
                    'valid-length',
                    'Bank account number must contain between 1 and 25 digits',
                    (value) => {
                        if (!value) return false;
                        return value.replace(/ /g, '').length <= 25;
                    }
                ),
            confirmAccountNumber: Yup.string()
                .oneOf([Yup.ref('accountNumber'), undefined], 'Account numbers must match')
                .required('Please re-enter your account number'),
            ifscCode: Yup.string()
                .required('IFSC Code is required')
                .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC Code format'),
        }),
        onSubmit: async (values) => {
            try {
                try {
                    const response = await apiRequest<SaveBasicDetailsResponse>("GET", `${ENDPOINTS.bank_address}?ifsc_code=${values.ifscCode}`,);

                    if (response.mainData && response.mainData.bank_address !== '') {
                        setaddressdata(response.mainData)
                    } else {

                        // debugger;
                        dispatch(setPageError('No Address found'))

                    }
                } catch (error) {
                }
                dispatch(openModal())


            } catch (error) {
                console.error("Fetch error:", error);
                throw error;
            }

        },
    });

    const fetchBankDetailsByIFSC = async (ifsc: string) => {
        dispatch(openModal())
    };

    const handleConfirm = async () => {
        setLoaded(false)

        try {
            const payload = {
                // "customer_id": "********",
                "account_number": accountNumber.replaceAll(" ", ""),
                "ifsc_code": ifscCode
            }
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.trigger_penny_drop, payload);

            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                router.push(`/banksuccess`);
                //router.push('/consent')
                // window.location.assign(response.mainData.landing_page)
                // await fetchBankDetailsByIFSC(values.ifscCode);
                // const commonData = await fetchCommonApi();
                // store.dispatch(setCommonData(commonData));


            } else {
                setLoaded(true)
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
                    // debugger;
                    dispatch(setPageError(response.mainData.error_message as string | "null"))
                } else if (response.error) {
                } else {
                }
            }
        } catch (error) {
            setLoaded(true)
        } finally{
            setTimeout(() => {
                dispatch(setPageError(''))
            }, 4000);
        }
    };

    useEffect(() => {
        console.log(bank_id)
    }, []);
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading");  setLoaded(true)}

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    return (
        <PageWrapper>
            {loaded ?

                <div className={`external-wrapper bank-details ${styles.bankDetailsPage}`}>
                    <PageHeader title='Enter your bank account details' para="Funds will be deposited here, and EMIs will be deducted from this account." />
                    <div className={`page-content ${styles.pageContent}`}>
                        <form onSubmit={formik.handleSubmit}>
                            <div className="input-wrapper">
                                <input
                                    type="password"
                                    name="accountNumber"
                                    placeholder=" "
                                    inputMode="numeric"
                                    style={{ fontSize: '24px' }}
                                    className={`form-control`}
                                    value={formik.values.accountNumber}
                                    onPaste={(e) => e.preventDefault()}
                                    onCopy={(e) => e.preventDefault()}
                                    disabled={!isChecked}
                                    onChange={(e) => {
                                        const value = e.target.value.replace(/[^0-9]/g, '');
                                        formik.setFieldValue(
                                            'accountNumber',
                                            value.match(/.{1,4}/g)?.join(' ').slice(0, 31) || '',
                                        );
                                        dispatch(setAccountNumber(value.match(/.{1,4}/g)?.join(' ').slice(0, 31) || ''));
                                        if (value?.length === 25) {
                                            setTimeout(() => e.target.blur(), 0)
                                        }
                                    }}
                                />
                                <label>Bank Account Number</label>
                                {typeof formik.errors.accountNumber === 'string' && formik.touched.accountNumber && (
                                    <div className="error">{formik.errors.accountNumber}</div>
                                )}
                            </div>
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    autoComplete='off'
                                    name="confirmAccountNumber"
                                    inputMode="numeric"
                                    placeholder=" "
                                    className={`form-control`}
                                    value={formik.values.confirmAccountNumber}
                                    maxLength={31}
                                    onChange={(e) => {
                                        const value = e.target.value ? e.target.value.replace(/[^0-9]/g, "") : '';
                                        const formattedValue = value ? value
                                            .match(/.{1,4}/g)
                                            ?.join(" ")
                                            .slice(0, 31) : '';
                                        formik.setFieldValue("confirmAccountNumber", formattedValue);
                                        dispatch(setConfirmAccountNumber(formattedValue));
                                        if (formattedValue?.length === 31) {
                                            setTimeout(() => e.target.blur(), 0)
                                        }
                                    }}
                                    onBlur={formik.handleBlur}
                                    onPaste={(e) => e.preventDefault()}
                                    onCopy={(e) => e.preventDefault()}
                                    disabled={!isChecked}
                                />
                                <label>Re-enter Account Number</label>
                                {typeof formik.errors.confirmAccountNumber === 'string' && formik.errors.confirmAccountNumber && formik.touched.confirmAccountNumber && (
                                    <div className="error">{formik.errors.confirmAccountNumber}</div>
                                )}
                            </div>

                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    name="ifscCode"
                                    autoComplete='off'
                                    placeholder=" "
                                    maxLength={11}
                                    className={`form-control`}
                                    value={formik.values.ifscCode}
                                    onChange={(e) => {
                                        //formik.handleChange(e);
                                        const value = e.target.value
                                        const formattedValue = value ? value.replace(/[^A-Za-z0-9]/g, "").slice(0, 11) : ''
                                        formik.setFieldValue("ifscCode", (formattedValue).toUpperCase());
                                        dispatch(setIfscCode((formattedValue).toUpperCase()));
                                        if (formattedValue?.length === 11) {
                                            setTimeout(() => e.target.blur(), 0)
                                        }
                                    }}
                                    onBlur={formik.handleBlur}
                                    disabled={!isChecked}
                                />
                                <label>IFSC Code</label>
                                {typeof formik.errors.ifscCode === 'string' && formik.errors.ifscCode && formik.touched.ifscCode && (
                                    <div className="error">{formik.errors.ifscCode}</div>
                                )}
                            </div>
                            <div className="bottom-footer mt-auto">
                                <p className="secure-tag">
                                    <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                </p>
                                <button type="submit" className={`btn btn-primary mb-0 ${!formik.isValid || !formik.dirty ? 'disabled' : ''}`}>
                                    Continue
                                </button>
                            </div>
                        </form>
                    </div>
                    <BottomPopup
                        isOpen={isModalOpen}
                        onClose={() => dispatch(closeModal())}
                        title="Bank Details"
                        buttons={[
                            { label: 'Continue', onClick: handleConfirm }
                        ]}
                    >
                        {/*bankDetails && (
                    <ul className="bank-details">
                        <li><span>Bank Name:</span><span>{bankDetails.BANK}</span></li>
                        <li><span>Account Number:</span><span>{accountNumber}</span></li>
                        <li><span>IFSC Code:</span><span>{bankDetails.IFSC}</span></li>
                        <li className="address"><span>Branch Address:</span><span>{bankDetails.ADDRESS}</span></li>
                    </ul>
                )*/}
                        <ul className="bank-details">
                            <li><span>Bank Name:</span><span>{addressdata?.bank_name}</span></li>
                            <li><span>Account Number:</span><span>{accountNumber}</span></li>
                            <li><span>IFSC Code:</span><span>{ifscCode}</span></li>
                            <li className="address"><span>Branch Address:</span>
                                <span>{addressdata?.bank_address}</span>
                            </li>
                        </ul>
                        <p className="secure-tag mb-0">
                            <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                        </p>
                    </BottomPopup>

                </div>
                :

                <LoadingComp />
            }
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
        </PageWrapper>
    );
};

export default BankDetailsForm;

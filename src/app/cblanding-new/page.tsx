"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './cblanding.module.scss'
import lowintrestrates from '../images/lowintrestrates.png'
import creditbuilder from '../images/creditbuilder.png'
import lowcibilscore from '../images/lowcibilscore.png'
import higherlimit from '../images/higherlimit.png'
import loancard from '../images/loancard.svg'
import bulb from '../images/bulb.png'
import rejectedheadericonexcla from '../images/rejectedheadericonexcla.png'
import creditscoreunavailable from '../images/creditscoreunavailable.png'
import craditscore from '../images/cradit-score.png'
import money from '../images/money.png'
import overduewithbeauro from '../images/overduewithbeauro.png'
import { useThemeColor } from '../hooks/useThemeColor'
import { useEffect, useState } from "react"
import { apiRequest, fetchCommonApi } from "../utils/api"
import { ENDPOINTS } from "../utils/endpoints"
import { useDispatch, useSelector } from 'react-redux';
import store, { RootState } from "../store/store"
import { setPageError, setPageSuccess } from '../register/register.slice';
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { setCommonData } from "../store/slices/commonSlice"
import { ApiResponse } from "../store/interface/apiInterface"
import CtEvents from "../utils/Ctevents"
import PageWrapper from "../components/PageWrapper"
import lowcibilscorecb from '../images/lowcibilscorecb.svg'
import loanoverduecb from '../images/loanoverduecb.svg'
import scoreunavailablecb from '../images/scoreunavailablecb.svg'



function CbLandingPage() {
  const dispatch = useDispatch();
  const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
  const [status, setStatus] = useState<any>('');
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
  const [showModal, setShowModal] = useState(false);

  const handleOpen = () => setShowModal(true);
  const handleClose = () => setShowModal(false);


  useThemeColor('#FFE7E7')
  interface SaveBasicDetailsResponse {
    success: boolean | string;
    error_message?: string;
    data: Record<string, any>;
    landing_page: string,
    profile_pic_upload_url: string,
    status: any
  }
  const GetLanding = async () => {
    try {

      const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.cb_landing);
      if (response.mainData && response.mainData.data) {
        setStatus(response.mainData.data.rejectReason)
        // setStatus('GENERAL')
        if (response.mainData.data.rejectReason == "LOW_CIBIL") {
          const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
          const event_name = 'Screen View'
          const productcode = localStorage.getItem("product_code")
          const event_property = { "Screen Name": "Loc_cibil", "Product category": 'cb', "Reject Product": productcode, "Source": cteventsfromstorage?.source }
          setCtdata({ event_name, event_property })
        }
        if (response.mainData.data.rejectReason == "OVERDUE") {
          const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
          const event_name = 'Screen View'
          const productcode = localStorage.getItem("product_code")
          const event_property = { "Screen Name": "Over due with Bureau", "Product category": 'cb', "Reject Product": productcode, "Source": cteventsfromstorage?.source }
          setCtdata({ event_name, event_property })
        }
        if (response.mainData.data.rejectReason == "NTC") {
          const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
          const event_name = 'Screen View'
          const productcode = localStorage.getItem("product_code")
          const event_property = { "Screen Name": "credit score unavailable", "Product category": 'cb', "Reject Product": productcode, "Source": cteventsfromstorage?.source }
          setCtdata({ event_name, event_property })
        }
        if (response.mainData.data.rejectReason == "GENERAL") {
          const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
          const event_name = 'Screen View'
          const productcode = localStorage.getItem("product_code")
          const event_property = { "Screen Name": "Freedon to cb", "Product category": 'cb', "Reject Product": productcode, "Source": cteventsfromstorage?.source }
          setCtdata({ event_name, event_property })
        }

      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }
        else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
          dispatch(setPageError(response.mainData.error_message as string | "null"))
        } else if (response.error) {
          // debugger;
        } else {
        }
      }
    } catch (error) {
    }
  }
  useEffect(() => {
    GetLanding()
  }, [])

  const createNextStep = async () => {

    const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.nextStep);
    if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
      const commonData = await fetchCommonApi();
      store.dispatch(setCommonData(commonData));
    }
    else {
      debugger;
      dispatch(setPageError(response.mainData?.error_message || "No step matched"))
    }
  }
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code")

    const event_property = { "Screen Name": "cb_final_rejection_screen", "Product category": 'cb', "Source": cteventsfromstorage?.source }
    setCtdata({ event_name, event_property })
  }, [])


  return (
    <PageWrapper>
      <div className={styles.header}>
        <div className={styles.gradientCircle}></div>

        <PageHeader title="" cross={true} call={true} />

        {/* overdue with Bureau */}
        {status === "OVERDUE" &&
          <div className={styles.headercont}>
            <div className={styles.select}>
              <p className={styles.congotxt}>Your loan is overdue with Bureau</p>
              <p>We’re unable to offer this product due to overdue accounts in your credit report. Consider our Credit Builder Loan to improve your credit standing.</p>
              <p><strong>Don’t worry we can help in increasing your CIBIL score</strong></p>
            </div>
            <div className={styles.icon}>
              <Image src={loanoverduecb} alt="" />
            </div>
          </div>
        }

        {/* Credit score unavailable */}
        {status === "NTC" &&

          <div className={styles.headercont}>
            <div className={styles.select}>
              <p className={styles.congotxt}>Credit score unavailable</p>
              <p>It seems you don’t have a credit score due to a lack of credit history. Applying for a loan typically requires a good credit score</p>
              <p><strong>Check out our Credit Builder Loan to start building your credit history.</strong></p>
            </div>
            <div className={styles.icon}>
              <Image src={scoreunavailablecb} alt="" />
            </div>
          </div>
        }

        {/* Low CIBIL score */}
        {status === "LOW_CIBIL" &&

          <div className={styles.headercont}>
            <div className={styles.select}>
              <p className={styles.congotxt}>Low CIBIL score</p>
              <p>Looks Like you are not eligible for loan right now due to low CIBIL score.</p>
              <p><strong>Don’t worry we can help in increasing your CIBIL score</strong></p>
            </div>
            <div className={styles.icon}>
              <Image src={lowcibilscorecb} alt="" />
            </div>
          </div>
        }
        {status === "GENERAL" &&

          <div className={styles.headercont}>
            <div className={styles.select}>
              <p className={styles.congotxt}>Looks Like you are not eligible for loan right now due to low CIBIL score.</p>
              {/* <p>Looks Like you are not eligible for loan right now due to low CIBIL score.</p> */}
              {/* <p><strong>Don’t worry we can help in increasing your CIBIL score</strong></p> */}
            </div>
            <div className={styles.icon}>
              <Image src={rejectedheadericonexcla} alt="" style={{ width: '72px', height: '80px' }} />
            </div>
          </div>
        }
      </div>

      <div className={styles.body}>

        {showModal && (
          <div className={styles.overlay} onClick={handleClose}>
            <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
              <button className={styles.close} onClick={handleClose}>×</button>
              <div className={styles.video}>
                <div className="video-container">
                  <iframe
                    width="100%"
                    height="100%"
                    src="https://www.youtube.com/embed/xC3tPCbHyqo?autoplay=1&&loop=1&controls=1&disablekb=1&modestbranding=1&rel=0"
                    title="How It Works Video"
                    allow="autoplay; encrypted-media"
                    allowFullScreen
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="py-4">
          {/* <h2 className='fw-bolder'>Application Unsuccessful</h2>
          <p className='fw-medium pe-20 mb-12'>We regret you are not eligible for this loan due to low Credit Score <span
            className='text-danger fw-bolder'>650</span></p>
          <p className='mb-3'><a href="#" className='more-text'>Know more</a></p> */}
          <h3 className='fw-bold'>What you can do now?</h3>
        </div>
        <div className='card-section-1'>
          <div className='credit-banner mb-12'>
            <Image src={craditscore} alt='' />
          </div>
          <h3 className={`fw-bolder ${styles.repairstar}`}>Repair your Credit Score</h3>
          <ul className='ullist'>
            <li>Boost credit score & credibility</li>
            <li>Increase chances of loan approval</li>

            <li>Build your credit score from scratch</li>
            <li>Get better interest rates</li>
          </ul>
          <div className='d-flex-section'>
            <div className={`how-btn ${styles.howbtn}`} onClick={handleOpen}>How it works?</div>
            <div onClick={() => createNextStep()} className='improve-btn'>Improve Now</div>
          </div>
        </div>

        {/* <div className='card-section-2 mt-4 position-relative'>
          <span className='pre-approved'>Pre approved loan up to ₹20 Lakh</span>
          <div className='pt-3'>
            <Image src={money} alt='' />
          </div>
          <h4 className='fw-bolder pt-3'>Use your mutual funds to <br /><span className='text-danger'>get instant cash</span>
          </h4>
          <ul className='ullist-2'>
            <li><span className='fw-bold'> 100% eligibility!</span> Even for low CIBIL score </li>
            <li><span className='fw-bold'>No MF selling required.</span> Mutual funds stay intact, while they keep
              growing</li>
          </ul>
          <div className='d-flex justify-content-between alig-items-center'>
            <button className='proceed-btn'>Proceed to pledge mutual funds </button>
          </div>
        </div> */}
      </div>
      {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
      <CtEvents data={ctData} />
    </PageWrapper>
  )
}

export default CbLandingPage
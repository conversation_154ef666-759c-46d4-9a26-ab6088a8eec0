.header {
    // background: #FFE7E7;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 10px;
    position: relative;

    header {
        padding-bottom: 0 !important;
        margin-bottom: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        >div {
            padding-top: 0 !important;
            padding-bottom: 0;
        }
    }

    .headercont {
        padding-top: 20px;
        display: flex;
        padding-bottom: 15px;
        padding-left: 0;
        padding-right: 0;

        >div {
            &.select {
                padding-top: 0;

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--black);
                    padding-right: 30px;

                    &+p {
                        margin-top: 7px;
                    }

                    &.congotxt {
                        font-family: var(--font-mona-sans);
                        font-weight: 700;
                        font-size: 18px;
                        line-height: 28px;
                        color: var(--black);
                    }
                }


            }

            &.icon {
                min-width: 80px;

                img {
                    width: 100%;
                    height: auto
                }

                ;
            }
        }
    }

    header {
        min-height: auto;
        padding: 0;
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 10px;
        background-size: 100% calc(100% - 5px);
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;

    .circularbtn {
        padding: 8px 16px;
        border-radius: 66px;
        border: 1px solid #000000;
        background: #fff;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        line-height: 24px;
        font-size: 16px;
        color: #000;
        cursor: pointer;
    }

    .whatucantitle {
        font-family: var(--font-mona-sans);
        padding: 25px 0 16px 0;
        font-weight: 700;
        line-height: 24px;
        font-size: 16px;
        color: #000;
    }

    .creditbuilderbox {
        padding: 24px 10px 30px 16px;
        background: #F5F5F5;
        background-position: bottom 15px right 16px;
        border-radius: 12px;
        margin-top: 25px;

        >img {
            width: 205px;
            height: 80px;
        }

        .title {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            line-height: 30px;
            font-size: 20px;
            color: #101010;
            margin-top: 15px;
            padding-right: 50px;
        }

        >p {
            margin-top: 9px;
            font-family: var(--font-mona-sans);
            font-weight: 600;
            padding-right: 50px;
            line-height: 14px;
            font-size: 12px;
            color: #101010;
            margin-top: 24px;
        }

        .steps {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;

            >div {
                width: 80px;

                img {
                    width: 40px;
                    height: 40px;
                }

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 12px;
                    line-height: 18px;
                    color: var(--black);
                }
            }
        }

        .circularbtn {
            margin-top: 24px;
            max-width: 121px;
        }
    }

    .notebox {
        width: 100%;
        background: #FFF8E0;
        border-radius: 12px;
        padding: 16px 16px;
        margin-top: 25px;
        display: flex;

        img {
            width: 36px;
            height: 36px;
            margin-right: 8px;
        }

        >div {
            .title {
                font-family: var(--font-mona-sans);
                font-weight: 700;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
            }

            .subtitle {
                font-family: var(--font-mona-sans);
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;
                margin-top: 5px;
                color: var(--black);
            }
        }
    }

    .video {
        margin-top: 25px;
        background: var(--black);
        width: 100%;
        height: 180px;
        border-radius: 12px;
        margin-bottom: 20px;
    }

    .powerdby {
        margin-top: 18px;
        text-align: center;
        display: flex;
        align-items: center;
        flex-flow: column;
        padding-bottom: 5px;
        margin-top: auto;

        p {
            font-family: var(--font-mona-sans);
            font-weight: 400;
            font-size: 10px;
            line-height: 17px;
            color: #5A5A5A;
        }

        span {
            font-family: var(--font-mona-sans);
            font-weight: 400;
            font-size: 10px;
            line-height: 17px;
            color: #5A5A5A;
        }
    }

    .footerbottombtn {
        margin-bottom: 12px;
    }
}
.gradientCircle {
    position: absolute;
    top: 0;
    left: 0;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(144deg, rgba(255, 203, 203, 1) 30%, rgba(255, 255, 255, 1) 0%);
    filter: blur(69.4px); // Figma blur value
    z-index: -1;
    pointer-events: none;
}
.repairstar {
    position: relative;
    font-size: 30px;
    display: flex;
    max-width: 290px;
    &:after {
        content: "";
        width: 23px;
        height: 22px;
        background: url(../images/repairstar.svg) no-repeat;
        background-size: 100%;
        position: relative;
        top: -6px;
        left: 4px;
    }
}
.howbtn {
    position: relative;
    display: flex;
    &::after {
        content: "";
        width: 20px;
        height: 20px;
        background: url(../images/videoplayicon.svg) no-repeat;
        background-size: 100%;
        margin-left: 7px;
    }
}
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal {
    position: relative;
    width: 90%;
    max-width: 800px;
    background: #000;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  }
  
  .close {
    position: absolute;
    top: -12px;
    right: -12px;
    background: #fff;
    border: none;
    color: #000;
    font-size: 1.5rem;
    padding: 0.25rem 0.6rem;
    border-radius: 50%;
    cursor: pointer;
    z-index: 2;
    width: 45px;
  }
  
  .video {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
    border-radius: 8px;
  
    .video-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
  
      iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
  }
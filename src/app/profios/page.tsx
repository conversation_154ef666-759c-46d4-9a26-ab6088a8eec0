"use client";

import { FC, Suspense, useEffect, useState } from 'react';
import './styles/profios.scss';
import certficate1 from './images/certificate1.png'
import certficate2 from './images/certificate2.png'
import certficate3 from './images/certificate3.png'
import certficate4 from './images/certificate4.png'
import certficate5 from './images/certificate5.png'
import certficate6 from './images/certificate6.png'
import { useRouter, useSearchParams } from 'next/navigation';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { apiRequest } from '../utils/api';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { setMessage, setProfiosData } from './profiosSlice';
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';

const ProfiosContent: FC = () => {
  const { payload, signature, message, url } = useSelector((state: RootState) => state.profios);
  const searchParams = useSearchParams();
  const router = useRouter();
  const dispatch = useDispatch();
  const [loader, setLoader] = useState(false);

  const getPerfiosDataApiCall = async () => {
    setLoader(true);
    try {
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.initiatenetbanking, {});

      if (response.mainData && response.mainData.url) {
        localStorage.setItem('transaction_id', response.mainData.transaction_id ?? '');
        const data = { payload: response?.mainData?.payload,  signature: response?.mainData?.signature,  url: response?.mainData?.url };
        dispatch(setProfiosData(data));        
      } else {
        if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
        } else if (response.error) {
          toast.error(response.error)
        }
      }
    } catch (error) {
      console.log(error)
    }finally{
      setLoader(false)
    }
  }

  useEffect(() => {
    const token = searchParams.get('auth_token') || window.localStorage.getItem('auth_token');
    if (!token) {
      window.open('https://www1.stashfin.com/login', '_self');
      return;
    }
    window.localStorage.setItem('auth_token', token);
    getPerfiosDataApiCall();
  }, [message, router, searchParams]);

  useEffect(() => {
    toast.error(message)
  }, [message]);

  const handleButtonClick = () => {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;

    const payloadInput = document.createElement('input');
    payloadInput.type = 'hidden';
    payloadInput.name = 'payload';
    payloadInput.value = payload;
    form.appendChild(payloadInput);

    const signatureInput = document.createElement('input');
    signatureInput.type = 'hidden';
    signatureInput.name = 'signature';
    signatureInput.value = signature;
    form.appendChild(signatureInput);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form); 
  };

  return (
    <div className='customBody'>
      {loader ? (
        <div className='loader_container'>
          <div className='loader'></div>
          <p>Please Wait...</p>
        </div>
      ) : (
        <div className='profios_container'>
            <p className='para1'>Your banking details can help you get a higher limit -</p>
            <p className='para2'>Use secure netbanking OR upload the latest bank statementof your salary account.</p>
            <div className='banking_mode_div'>
                <span className='statemnt_mode_text'>Statement Mode</span>
                <div className='netBanking_div'>Net Banking</div>
            </div>
            <span className='ready_tetx'>* Please keep your netbanking id and password ready</span>
            <div className='btns_div'>
                <button className='start_btn' onClick={handleButtonClick}>Start</button>
                <button className='backPbtn'>Back</button>
            </div>
            <span className='safe_secure_text'>SAFE AND SECURE</span>
            <div className='certificates_div'>
                <div className='certicate_row'>
                    <img className='certificate' src={certficate1.src}></img>
                    <img className='certificate certificate_norton' src={certficate2.src}></img>
                </div>
                <div className='certicate_row'>
                    <img className='certificate' src={certficate3.src}></img>
                    <img className='certificate' src={certficate4.src}></img>
                </div>
                <div className='certicate_row'>
                    <img className='certificate' src={certficate5.src}></img>
                    <img className='certificate' src={certficate6.src}></img>
                </div>
            </div>
            <span className='info_span'># Above certifications are for perfios website not for stashfin.com</span>
            <p className='perfios_intro'>Perfios is an independent solution provider that specializes in collecting bank statements for customers in a safe and secure way.</p>
            <span className='read_more_text'>Read more</span>
            <ToastContainer />
        </div>
      )}

      {/* {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null} */}
    </div>
  );
};

const profiosPage: FC = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <ProfiosContent />
  </Suspense>
);

export default profiosPage;

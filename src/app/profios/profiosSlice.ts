import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ProfiosState {
    payload : string,
    signature : string,
    url : string,
    message: string,
}

const initialState: ProfiosState = {
    payload : "",
    signature : "",
    url : "",
    message: ""
};

const ProfiosSlice = createSlice({
  name: "profios",
  initialState,
  reducers: {
    setProfiosData: (state, action: PayloadAction<{payload: string, signature: string, url: string}>) => {
      state.payload = action.payload.payload;
      state.signature = action.payload.signature;
      state.url = action.payload.url;
    },
    setMessage: (state, action: PayloadAction<string>) => {
        state.message = action.payload
    }
  },
});

export const { setProfiosData, setMessage } = ProfiosSlice.actions;
export default ProfiosSlice.reducer;

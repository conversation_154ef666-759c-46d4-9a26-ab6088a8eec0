.customBody {
    padding-top: '4%';
    .profios_container{
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-top: 50px;
        gap: 20px;
        .para1{
            font-size: 22px;
            text-align: center;
            color: rgba(0, 0, 0, 0.7);
        }
        .para2{
            font-size: 19px;
            text-align: center;
            color: rgba(0, 0, 0, 0.6);
            font-style: italic;
            margin-top: -15px;
        }
        .banking_mode_div{
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            .statemnt_mode_text{
                font-size: 17px;
                color: rgba(0, 0, 0, 0.7);
            }
            .netBanking_div{
                background: #e7660e;
                padding: 5px 10px;
                border-radius: 6px;
                color: white;
            }
        }
        .ready_tetx{
            color: rgba(0, 0, 0, 0.7);
            text-align: center;
        }
        .btns_div{
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-top: 30px;
            .start_btn{
                padding: 8px 27px;
                background: #3333fe;
                color: white;
                border-radius: 10px;
                font-size: 18px;
            }
            .backPbtn{
                border: 1px solid rgba(0, 0, 0, 0.4);
                border-radius: 6px;
                padding: 8px 27px;
                font-size: 18px;
            }
        }
        .safe_secure_text{
            text-align: center;
            font-size: 23px;
            color: rgba(0, 0, 0, 0.7);
        }
        .certificates_div{
            display: flex;
            flex-direction: column;
            gap: 36px;
            .certicate_row{
                display: flex;
                justify-content: space-around;
                align-items: center;
                .certificate{
                    height: 88px;
                }
                .certificate_norton{
                    height: 62px;
                }
            }
        }
        .info_span{
            font-size: 18px;
            color: rgba(0, 0, 0, 0.7);
            text-align: center;
        }
        .perfios_intro{
            font-size: 16px;
            color: rgba(0, 0, 0, 0.5);
            text-align: center;
        }
        .read_more_text{
            color: blue;
            text-decoration: underline;
            cursor: pointer;
        }
    }

    .loader_container{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
        .loader {
            width: 120px;
            height: 20px;
            -webkit-mask: radial-gradient(circle closest-side,#000 94%,#0000) left/20% 100%;
            background: linear-gradient(#FF002B 0 0) left/0% 100% no-repeat #ddd;
            margin: 0 auto;
            margin-bottom: 40px;
            animation: l17 1s infinite alternate steps(600);
          }
          @keyframes l17 {
              100% {background-size:120% 100%}
          }
    }
}
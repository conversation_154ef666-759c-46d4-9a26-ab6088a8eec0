"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './manualbankverify.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import defaultbank from '../images/defaultbank.svg'
import shield from '../images/shield-icon.svg'
import { useEffect, useState } from "react"
import { apiRequest } from '../utils/api'; // Common API call file
import { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { setCustomerBankDetails, setPageError, setPageSuccess } from '../register/register.slice';
import { ENDPOINTS } from '../utils/endpoints';
import { useDispatch, useSelector } from 'react-redux';
import { ApiResponse } from "../store/interface/apiInterface"
import PageWrapper from "../components/PageWrapper"
import { useRouter } from "next/navigation";
import LoadingComp from "../component/loader"
import BankImage from "../components/BankImage"




function ManualbBankVerifyPage() {
  const router = useRouter();
  useThemeColor('#fff')
  const dispatch = useDispatch();
  const [loaded, setLoaded] = useState<boolean>(false)

  const { pageerror, pagesuccess, customerBankDetails } = useSelector((state: RootState) => state.register);
  const [otherBanks, setOtherBanks] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [productcode, setproductcode] = useState(false)
  const [popularBanks, setPopularBanks] = useState<any[]>([]);
  const [selectedOccupation, setSelectedOccupation] = useState<string>('');



  interface SaveBasicDetailsResponse {
    filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
    success: boolean | string;
    error_message?: string;
    data: Record<string, any>; // Adjust according to the actual data structure you expect
    shortUrl: string;
    upiLink: string
  }
  const handleRPDClick = async () => {
    setLoaded(false)
    try {
      const payload = {
        // customer_id: '********'
      };
      const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.rpd, payload);

      if (response.mainData && response.mainData.upiLink) {

        window.location.assign(response.mainData.upiLink)
        router.push('/rpdpolling');
      } else {


        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message));
        } else if (response.error) {
        } else {
        }
      }
    } catch (error) {


      // dispatch(setPageError('Error submitting basic details'))
      //console.error("Error submitting basic details:", error);
      //alert("Failed to save basic details. Please try again.");
    } finally {
      setTimeout(() => {
        setLoaded(true)
      }, 2000);
    }
  };
  useEffect(() => {
    const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
    const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

    handleStart();
    setTimeout(handleComplete, 1000);

    return () => handleComplete();
  }, [router]);
  const fetchUserBank = async () => {
    dispatch(setPageError(''));
    dispatch(setPageSuccess(''));

    try {

      const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.customerBankList);
      if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
        if (response.mainData.data.length > 0) {

          dispatch(setCustomerBankDetails(response.mainData.data));

        }
        else {

          getbankList()
        }
      } else {
        dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
      }
    } catch (error) {
      //dispatch(setPageError('Error submitting address confirmation'));
    }
  }
  useEffect(() => {
    fetchUserBank()
    const geproductcode = localStorage.getItem('product_code')
    if (geproductcode == "Sentinel") {
      setproductcode(true)
    }
  }, [])
  const getbankList = async () => {
    try {

      const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.banklist);

      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
        // setBankList(response.mainData.data);

        setPopularBanks(response.mainData.data.filter((bank: any) => bank.is_popular));
        setOtherBanks(response.mainData.data.filter((bank: any) => !bank.is_popular));
      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }
        if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
        } else if (response.error) {
        } else {
        }
      }
    } catch (error) {
    }
  }

  const filteredBanks = otherBanks.filter((bank) =>
    bank.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    const getPersoalDetails = async () => {
      try {
        const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.personalDetail);
        if (response.mainData && response.mainData?.data?.occupation) {
          setSelectedOccupation(response.mainData.data.occupation)
        } else {
          if (response.mainData && response.mainData.error_message) {
            dispatch(setPageError(response.mainData.error_message))
          }

          else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
          } else if (response.error) {
            dispatch(setPageError(response.error))

          } else {
          }
        }
      } catch (error) {
      } finally {
        setTimeout(() => {
          // setLoaded(true)
        }, 2000);
      }
    };
    getPersoalDetails()
  }, [])
  return (
    <PageWrapper>
      {loaded ?
        <>
          <div className={styles.header}>
            <PageHeader title="" />
          </div>
          <div className={styles.body}>
            <h1>Select {`${selectedOccupation === 'SALARIED' ? 'salary' : 'primary'}`} bank account</h1>

            {/*<div className={styles.notebox}>
              <div className={styles.title}>
                Important Note:
              </div>
              <div className={styles.subtitle}>
                Please ensure you use the same bank account in your UPI app for verification.
              </div>
            </div>*/}
            {customerBankDetails && customerBankDetails.length ? customerBankDetails.map((item: any, index: any) => <div className={styles.box} key={index}>
              <div>
                <p>{item?.ifsc_code}</p>
                <span>{item?.account_number}</span>
                <div className={styles.btn} onClick={handleRPDClick}>Proceed</div>
              </div>
              {/* <Image src={item?.bank_logo_url || defaultbank} alt='' /> */}
              <BankImage bankName={item?.bank_name || defaultbank} ratioHeight={48} />


            </div>) :
              <>
                <div className={styles.notebox}>
                  <div className={styles.title}>
                    Important Note:
                  </div>
                  <div className={styles.subtitle}>Please ensure you use the same bank account in your UPI app for verification.</div>
                </div>
                <div className="page-content">
                  <ul className={styles.bankList}>
                    {popularBanks.map((bank) => (
                      <li
                        key={bank.id}
                        onClick={() => handleRPDClick()}
                      >
                        <BankImage bankName={bank.name} ratioHeight={48} />
                        <p>{bank.name}</p>
                      </li>
                    ))}

                  </ul>
                  <div className={styles.otherBanks}>
                    <h3>Other Banks</h3>
                    <div className="input-wrapper">
                      <input
                        type="text"
                        name="otherBank"
                        autoComplete="off"
                        placeholder=" "
                        className={`form-control ${styles.search}`}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <label>Search by bank name</label>
                    </div>
                    <ul className={styles.otherBanksList}>
                      {filteredBanks.map((bank) => (
                        <li
                          key={bank.id}
                          onClick={() => handleRPDClick()}
                        >
                          <BankImage bankName={bank.name} ratioHeight={32} />
                          <span>{bank.name}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </>
            }

            <div className={styles.footerbottombtn}>
              <div className={styles.datasafety}>
                <Image src={shield} alt="" /> Your data is 100% safe & secure
              </div>
            </div>

          </div>
        </>
        :

        <LoadingComp />
      }
      {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
    </PageWrapper>
  )
}


export default ManualbBankVerifyPage
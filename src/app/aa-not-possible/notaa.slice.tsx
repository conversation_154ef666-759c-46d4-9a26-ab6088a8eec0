import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface NotaaState {
  pageerror: string | null;
  pagesuccess: string | null;
  fileuploaderror: string | null;
  fileuploadsuccess: string | null;
  fileerror: string | null;
  isUploadModalOpen: boolean;
  uploadedImage: string;
  uploadingImage: boolean;
  uploadProgress: number;
}

// Define the initial state
const initialState: NotaaState = {
  pageerror: null,
  pagesuccess: null,
  fileuploaderror: null,
  fileuploadsuccess: null,
  fileerror: null,
  isUploadModalOpen: false,
  uploadedImage: '',
  uploadProgress: 0,
  uploadingImage: false
};

// Create the slice
const notaaSlice = createSlice({
  name: 'notaa',
  initialState,
  reducers: {
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setFileUploadError: (state, action: PayloadAction<string | null>) => {
      state.fileuploaderror = action.payload;
    },
    setUploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    setFileUploadSuccess: (state, action: PayloadAction<string | null>) => {
      state.fileuploadsuccess = action.payload;
    },
    setFileUploading: (state, action: PayloadAction<boolean>) => {
      state.uploadingImage = action.payload;
    },
    openUploadModal: (state) => {
      state.isUploadModalOpen = true;
    },
    closeUploadModal: (state) => {
      state.isUploadModalOpen = false;
    },
    setFileError: (state, action: PayloadAction<string | null>) => {
      state.fileerror = action.payload;
    },
    setUploadedFile: (state, action: PayloadAction<string>) => {
      state.uploadedImage = action.payload;
    },
  },
});

// Export actions
export const {
  setPageError,
  setPageSuccess,
  setFileUploadError,
  setFileUploadSuccess,
  setUploadProgress,
  setFileUploading,
  openUploadModal,
  closeUploadModal,
  setFileError,
  setUploadedFile,
} = notaaSlice.actions;

// Export the reducer
export default notaaSlice.reducer;

"use client"
import Image from "next/image"
import { useDispatch, useSelector } from 'react-redux';
import PageHeader from "../components/PageHeader"
import styles from './aanotpossible.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import uploadpdf from '../images/uploadpdf.svg'
import shield from '../images/shield-icon.svg'
import failedIcon from '../images/failed-icon.svg'
import successIcon from '../images/success-upload-icon.svg'
import removeIcon from '../images/remove-uload.svg'
import netbanking from '../images/netbanking.svg'
import React, { useEffect, useRef, useState } from "react"
import { apiRequest, fetchCommonApi } from '../utils/api'; // Common API call file
import store, { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { ENDPOINTS } from "../utils/endpoints";
import { ApiResponse } from "../store/interface/apiInterface";
import { closeUploadModal, openUploadModal, setFileError, setFileUploadError, setFileUploading, setFileUploadSuccess, setPageError, setPageSuccess, setUploadedFile, setUploadProgress } from "./notaa.slice";
import BottomPopup from "../components/popups/BottomPopup";
import PageWrapper from "../components/PageWrapper";
import { setCommonData } from "../store/slices/commonSlice";
import { useRouter } from "next/navigation";
import LoadingComp from "../component/loader";
import CtEvents from "../utils/Ctevents";


function AaNotpossiblePage() {
  useThemeColor('#fff')
  const router = useRouter();
  const dispatch = useDispatch();
  const { pageerror, pagesuccess, isUploadModalOpen, uploadedImage, fileerror, fileuploaderror, fileuploadsuccess, uploadProgress, uploadingImage } = useSelector((state: RootState) => state.notaa);
  const [loaded, setLoaded] = useState<boolean>(false)
  const uploadInputRef = useRef<HTMLInputElement>(null);
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

  const [showBankStatement, setShowBankStatement] = useState(false)

  useEffect(() => {
    if(pageerror){
      setTimeout(() => {
        setPageError('')
      }, 4000);
    }
    if(pagesuccess){
      setTimeout(() => {
        setPageSuccess('')
      }, 4000);
    }
  }, [])
  const MAX_FILE_SIZE = 5 * 1024 * 1024;
  const ALLOWED_FILE_TYPES = ['application/pdf'];

  const validateAndUploadFile = (file: File) => {
    dispatch(setFileError(''))
    if (!file) return;
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      dispatch(setFileError('Only support PDF format'))
      return;
    }
    if (file.size > MAX_FILE_SIZE) {
      dispatch(setFileError('File size must be less than 5 MB.'));
      return;
    }
    const reader = new FileReader();
    reader.onloadend = () => {
      dispatch(setUploadedFile(reader.result as string));
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndUploadFile(file);
    }
  };

  const handleBrowseFilesClick = () => {
    uploadInputRef.current?.click();
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file) {
      validateAndUploadFile(file);
    }
  };
  const handleNetBankingClick = async () => {
    setLoaded(false)
    try {
      const payload = {
        // customer_id: ********,
      };
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.initiatenetbanking, payload);

      if (response.mainData && response.mainData.url) {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")
        const event_property = { "CTA": " NetBanking", "Page Name": "Bank Statement Manual", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", }
        setCtdata({ event_name, event_property })
        localStorage.setItem('transaction_id', response.mainData.transaction_id ?? '')
        // window.location.assign(response.mainData.url)
        router.push("/profios")

      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }

        else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
        } else if (response.error) {
          dispatch(setPageError(response.error))

        } else {
        }
      }
    } catch (error) {
    } finally{
      setLoaded(true)
    }
  };
  const handleUploadStatement = async () => {
    dispatch(setFileUploadError(''))
    dispatch(setFileUploadSuccess(''))
    dispatch(closeUploadModal())
    dispatch(setFileUploading(true));
    //setLoaded(false)
    try {
      const payload = {
        // customer_id: ********,
        file: uploadedImage,
        fileName: "Bank Statement",
        fileType: "pdf"
      };
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.uploadBankStatement, payload, false, (progress) => dispatch(setUploadProgress(progress)));

      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
        dispatch(setFileUploadSuccess(response.mainData.message || 'Upload succesfull'))
      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setFileUploadError(response.mainData.error_message))
        }
        else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
          dispatch(setFileUploadError(response.mainData.error_message || ''))
        } else if (response.error) {
          dispatch(setFileUploadError(response.error))
        } else {
          dispatch(setFileUploadError('Unexpected error occurred.'))
        }
      }
    } catch (error) {
      dispatch(setFileUploadError('Error in uploading bank statement'))
      // dispatch(setPageError('Error submitting basic details'))
      //console.error("Error submitting basic details:", error);
      //alert("Failed to save basic details. Please try again.");
    } finally {
      //setLoaded(false)
      //dispatch(closeUploadModal())
      dispatch(setUploadProgress(0));
      dispatch(setFileUploading(false));
    }
  };
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code")
    const event_property = { "Screen Name": "Bank Statement Manual", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
    setCtdata({ event_name, event_property })
  }, [])
  useEffect(() => {
    const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
    const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

    handleStart();
    setTimeout(handleComplete, 1000);

    return () => handleComplete();
  }, [router]);

  useEffect(() => {
    const getPersoalDetails = async () => {
      setLoaded(false)
      try {
        const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.personalDetail);

        if (response.mainData && response.mainData?.data?.source) {
          if (response.mainData.data.source.slice(0, 3).toLowerCase() === 'idd') {
            setShowBankStatement(true)
          } else {
            setShowBankStatement(false)
          }
        } else {
          if (response.mainData && response.mainData.error_message) {
            dispatch(setPageError(response.mainData.error_message))
          }

          else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
          } else if (response.error) {
            dispatch(setPageError(response.error))

          } else {
          }
        }
      } catch (error) {
      } finally{
        setLoaded(true)
      }
    };
    getPersoalDetails()
  }, [])
  const handleButtonClick = async () => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Button Clicked'
    const productcode = localStorage.getItem("product_code")

    const event_property = { "CTA": " Upload PDF", "Page Name": "Bank Statement Manual", "Product category":productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", }
    setCtdata({ event_name, event_property })
    const commonData = await fetchCommonApi();
    store.dispatch(setCommonData(commonData));
  }
  return (
    <PageWrapper>
      {loaded ?
        <div>
          <div className={styles.header}>
            <PageHeader title="Select your preferred option" para="Select between Net Banking or Uploading a PDF" />
          </div>
          <div className={styles.body}>
            {/*<div className={styles.notebox}>
          <div className={styles.title}>
            Important Note:
          </div>
          <div className={styles.subtitle}>
            Please ensure you use the same bank account in your UPI app for verification.
          </div>
        </div>*/}
            <div className={styles.box}>
              <div className={styles.boxWrapper}>
                <div>
                  <p>Net Banking Login</p>
                  <span>Securely sign in with your netbanking credentials to share statements.</span>
                  <div className={styles.btn} onClick={handleNetBankingClick}>Net Banking Login</div>
                </div>
                <Image src={netbanking} alt='' />
              </div>
            </div>
            {showBankStatement ? <div className={styles.box}>
              <div className={styles.boxWrapper}>
                <div>
                  <p>Upload Bank Statement</p>
                  <span>Manually upload a PDF of your latest 6-month statement.</span>
                  <div className={styles.btn} onClick={() => dispatch(openUploadModal())}>
                    {uploadingImage ?
                      'File Uploading...' :
                      fileuploaderror ? <span className={styles.failedUpload}><Image src={failedIcon} alt="Failed" /> Upload failed</span> :
                        fileuploadsuccess ? <span className={styles.successUpload}><Image src={successIcon} alt="Success" /> filename <Image src={removeIcon} alt="Success" className={styles.remove} onClick={() => {
                          dispatch(setUploadedFile(''))
                          dispatch(setFileUploadSuccess(''))
                        }} /></span> :
                          'Upload PDF'}
                  </div>
                  {fileuploaderror ? <div className={styles.retry} onClick={() => {
                    dispatch(openUploadModal())
                    dispatch(setFileUploadError(''))
                    dispatch(setUploadedFile(''))
                  }}>Try again</div> : null}
                </div>
                <Image src={uploadpdf} alt='' />
              </div>
              {uploadingImage ? <div className={styles.loadingProgress}>
                <div className={styles.progressBar}>
                  <div style={{ width: `${uploadProgress}%` }}></div>
                </div>
                {uploadProgress > 0 && <div className={styles.progressPercent}>{uploadProgress}%</div>}
              </div> : null}

            </div> : null}
            <div className={styles.footerbottombtn}>
              <div className={styles.datasafety}>
                <Image src={shield} alt="" /> Your data is 100% safe & secure
              </div>
              {showBankStatement ? <div className={`footerbottombtn ${styles.btn}`} onClick={handleButtonClick}>
                Continue
              </div> : null}
            </div>
            <BottomPopup
              isOpen={isUploadModalOpen}
              onClose={() => dispatch(closeUploadModal())}
              title="Upload"
              footerClass="pt-0"
              buttons={[
                { label: 'Upload & Save', onClick: handleUploadStatement, className: uploadedImage ? '' : 'disabled' }
              ]}
            >
              <div>
                <p style={{ textAlign: 'left' }}>Max Size: 5 MB</p>
                <p style={{ textAlign: 'left', marginBottom: '15px' }}>Supported Format: .PDF</p>
                <div className="upload-btn">
                  <button
                    className="btn btn-primary-outline"
                    onClick={handleBrowseFilesClick}
                    type="button"
                  >
                    Browse files
                  </button>
                  {/* Hidden file input to handle file selection */}
                  <input
                    type="file"
                    ref={uploadInputRef}
                    style={{ display: 'none' }}
                    accept="application/pdf,image/jpeg,image/png"
                    onChange={handleFileInputChange}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                  />
                </div>
                {fileerror ? <div className="error">{fileerror}</div> : null}
              </div>
            </BottomPopup>
          </div>
        </div>
        :

        <LoadingComp />
      }
      {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
      <CtEvents data={ctData} />

    </PageWrapper>
  )
}

export default AaNotpossiblePage
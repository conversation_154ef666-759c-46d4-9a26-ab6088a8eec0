.header {


    header {
        min-height: auto;
        padding: 0;
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 10px;
        background-size: 100% calc(100% - 5px);
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;
    min-height: calc(100vh - 195px);
    display: flex;
    flex-flow: column;

    >h1 {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 24px;
        line-height: 28.8px;
        color: #000;
        margin-top: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        >img {
            width: 70px;
            height: 70px;
        }
    }

    >span {
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: var(--black);
    }

    >p {
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        margin-top: 16px;
        color: var(--black);
    }

    .notebox {
        width: 100%;
        background: #FFF8E0;
        border-radius: 12px;
        padding: 11px 16.5px;
        margin-top: 8px;

        .title {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
            color: var(--black);
        }

        .subtitle {
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            margin-top: 5px;
            color: var(--black);
        }
    }

    .box {
        border: 1px solid #E6E6E6;
        border-radius: 12px;
        margin-bottom: 20px;
        padding: 18px 12px 12px;

        .boxWrapper {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;

            >div {
                display: flex;
                flex-flow: column;
                align-items: flex-start;

                p {
                    display: flex;
                    align-items: center;
                    font-family: var(--font-mona-sans);
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 20px;
                    color: var(--black);
                    margin-bottom: 12px;

                    img {
                        margin-left: 5px;
                        width: 49px;
                        height: 12px;
                    }
                }

                span {
                    margin: top 6px;
                    font-family: var(--font-mona-sans);
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 16px;
                    color: var(--black);
                }

                .btn {
                    border: 1px solid #000;
                    border-radius: 66px;
                    padding: 4px 10px;
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 12px;
                    line-height: 20px;
                    color: #000;
                    margin-top: 14px;
                    display: flex;
                    cursor: pointer;
                }
            }

            >img {
                width: 64px;
                height: 64px;
            }
        }
    }

    .setupmandate {
        margin-top: 30px;
        text-align: center;
        color: #909090;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
    }

    .footerbottombtn {
        margin-top: auto;
        bottom: 0;
        background-color: #ffffff;

        .datasafety {
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            color: var(--black);

            img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }
        }

        .btn {
            margin-top: 22px;
        }
    }
}

.loadingProgress {
    display: flex;
    align-items: center;
    margin-top: 12px;
    margin-bottom: 8px;

    .progressPercent {
        margin-left: 8px;
        color: #0064e0;
        font-size: 14px;
        font-weight: 500;
        width: 34px;
        text-align: right;
        line-height: 1;
    }
}

.progressBar {
    width: 100%;
    background: #E2E2E2;
    border-radius: 2px;

    >div {
        width: 0%;
        background: #0064E0;
        height: 2px;
        border-radius: 2px;
        transition: width 0.3sease-in-out;
    }
}

.failedUpload {
    font-size: 12px !important;
    font-weight: 500 !important;
    color: #FF3547 !important;

    img {
        margin-right: 10px;
    }
}

.successUpload {
    font-size: 12px !important;
    font-weight: 500 !important;

    img {
        margin-right: 10px;
    }

    .remove {
        margin-right: 0;
        margin-left: 8px;
    }
}

.retry {
    line-height: 1;
    font-size: 14px;
    color: #0064E0;
    font-weight: 600;
    margin-top: 10px;
    cursor: pointer;
}
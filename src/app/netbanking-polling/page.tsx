'use client'
import React, { useEffect } from 'react';
import '../scss/button.scss';
import styles from './netbankingpolling.module.scss'
import { setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
// For common API call
import { apiRequest, fetchCommonApi } from '../utils/api';
import store from "../store/store";
import { setCommonData } from "../store/slices/commonSlice";

import { ENDPOINTS } from '../utils/endpoints';
import loadingAnimation from "../../../public/Loader_Red.json"
import dynamic from "next/dynamic";
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
function RpdPolling() {
    const dispatch = useDispatch();
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);

    interface SaveBasicDetailsResponse {
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
        redirection_url: string;
        status:string
    }
    const rpdpolling = async () => {
        try {

            const response = await apiRequest<SaveBasicDetailsResponse>("GET", `${ENDPOINTS.netbankingPolling}/${localStorage.getItem('transaction_id')}`);
            // debugger;
            if (response.mainData && response.mainData.status !=="pending") {
                // Common Api Respoinsegit status
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));

            } else {
                setTimeout(() => {
                    rpdpolling()
                }, 3000);
            }
        } catch (error) {
        }
    }
    useEffect(() => {
        rpdpolling()
    })
    return (
        <div className='external-wrapper'>
            <div className='page-content'>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        {/* <Image src={successImg} alt="Congratulations" /> */}
                        {/* <div className={`lds-ellipsis`}><div></div><div></div><div></div><div></div></div> */}
                        <Lottie
                            animationData={loadingAnimation}
                            loop={true}
                            style={{ height: 100, width: 100 }} // Adjust size as needed
                        />
                        <p>Please Wait...</p>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default RpdPolling;

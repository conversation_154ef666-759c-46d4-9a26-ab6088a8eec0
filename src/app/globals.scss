@import url('https://fonts.googleapis.com/css2?family=Mona+Sans:ital,wght@0,200..900;1,200..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Mona+Sans:ital,wght@0,200..900;1,200..900&display=swap');
@import './scss/button.scss';
@import './scss/variable.scss';
@import '../../node_modules/bootstrap/scss/bootstrap.scss';

:root {
    --text-color: #000000;
    --black-color: #000000;
    --primary-color: #0064E0;
    --secondary-color: #E9F0FA;
    --third-color: #E2F1FF;
    --white-color: #ffffff;
    --black: #1F1F1F;
    --balck-text: #000000;
    --light-gray: #EAEAEA
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    text-decoration: none;
}

html,
body {
    touch-action: manipulation !important;
    scroll-behavior: smooth;
    overflow-y: auto;
    height: 100%;
    /* Prevents double-tap zoom */
}

html {
    -webkit-text-size-adjust: none !important;
    text-size-adjust: none !important;
}

input[type="text"],
input[type="nummber"] textarea {
    font-size: 16px !important;
    /* Prevent iOS zoom */
    transform: scale(0.9);
    /* Visually reduce size */
    transform-origin: left center;
    /* Keep scaling aligned */
    width: 111% !important;
    max-width: 111% !important;
    /* Expand width to counteract scaling */
    // padding: 10px;
    /* Adjust padding if needed */
    box-sizing: border-box;
}

.react-datepicker-wrapper {
    width: 100%;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

.main-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.back-icon {
    cursor: pointer;
}

.footerbottombtn {
    width: 100%;
    background: var(--black);
    border-radius: 8px;
    display: flex;
    margin-top: 50px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-mona-sans);
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    color: #fff;
    cursor: pointer;
}

body {
    font-family: "Mona Sans", "Mona Sans Fallback" !important;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}

.error {
    color: $red;
    font-size: 12px;
    margin-top: 4px;
}

.link {
    color: $color1;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
}

.main-container {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
}

.page-content {
    padding: 0 16px;
    min-height: calc(100vh - 300px);
    display: flex;
    flex-direction: column;
}

.consent-check {
    p {
        margin-top: -3px;
        font-size: 12px;
    }

    label {
        display: flex;
        font-size: 12px;
        font-weight: 500;
        color: $color-gray;

        input {
            margin-right: 8px;
        }
    }
}

.secure-tag {
    font-size: 14px !important;
    color: $color-black !important;
    line-height: 25px;
    margin-bottom: 22px !important;
    text-align: center;
}

.powered-by {
    text-align: center;
    margin-top: 20px;
    position: relative;
    margin-bottom: 20px;
    font-size: 12px;
    font-weight: 500;
}

.referal-tag {
    background-color: $color-gray-light;
    font-size: 12px;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 8px;
    color: $color-gray;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 1;
    margin-bottom: 25px;
}

.bank-details {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
    font-weight: 400;

    li {
        padding: 15px 0px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:not(:last-of-type) {
            border-bottom: 1px solid #eeeeee;
        }

        &.address {
            flex-wrap: wrap;
            text-align: left;

            span:first-of-type {
                font-weight: 700;
            }

            span:last-of-type {
                display: block;
                width: 100%;
            }
        }
    }

    p {
        font-size: 13px;
        font-weight: 500;
        margin-bottom: 8px;
        color: $color-gray;
    }

    .consent-check {
        margin-bottom: 20px;
        width: 336px;
        max-width: 100%;
    }

    .powered-by {
        margin-bottom: 25px;
    }
}

.valid {
    background-image: url('./images/valid-check.svg');
    width: 16px;
    height: 16px;
    position: absolute;
    right: 18px;
    top: 18px;
}

.page-wrapper {
    background-image: url('./images/header-bg.svg');
    background-repeat: no-repeat;
    background-size: 100%;
    padding-bottom: 0;
}

.page-wrapper-green {
    background-image: url('./images/header-bg-green.svg');
    background-repeat: no-repeat;
    background-size: 100%;
    padding-bottom: 0;
}

@keyframes l2 {
    0% {
        background-size: 0%;
    }

    100% {
        background-size: 110%;
    }
}

.bottom-footer {
    z-index: 9;
    background-color: #fff;
    padding-bottom: 20px !important;
}

.lds-ellipsis,
.lds-ellipsis div {
    box-sizing: border-box;
}

.lds-ellipsis {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.lds-ellipsis div {
    position: absolute;
    top: 33.33333px;
    width: 13.33333px;
    height: 13.33333px;
    border-radius: 50%;
    background: #FF002B;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.lds-ellipsis div:nth-child(1) {
    left: 8px;
    animation: lds-ellipsis1 0.6s infinite;
}

.lds-ellipsis div:nth-child(2) {
    left: 8px;
    animation: lds-ellipsis2 0.6s infinite;
}

.lds-ellipsis div:nth-child(3) {
    left: 32px;
    animation: lds-ellipsis2 0.6s infinite;
}

.lds-ellipsis div:nth-child(4) {
    left: 56px;
    animation: lds-ellipsis3 0.6s infinite;
}

@keyframes lds-ellipsis1 {
    0% {
        transform: scale(0);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes lds-ellipsis3 {
    0% {
        transform: scale(1);
    }

    100% {
        transform: scale(0);
    }
}

@keyframes lds-ellipsis2 {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(24px, 0);
    }
}

.transitFooter {
    padding-bottom: 300px;
}

input.form-control[readonly] {
    opacity: 50%;

    //color: #fff;
    &:placeholder-shown {
        +label {
            background-color: transparent;
        }
    }
}

button.btn.btn-primary.btn-primary-outline.samebg {
    background-color: transparent !important;
    color: var(--black) !important;

    &:focus,
    &:hover {
        background-color: transparent !important;
        color: var(--black) !important;

    }
}

button.btn.btn-primary.btn-primary-outline.blackbg {
    background-color: var(--black) !important;
    color: #fff !important;

    &:focus,
    &:hover {
        background-color: var(--black) !important;
        color: #fff !important;

    }
}

button.btn.btn-primary.btn-primary-outline.samebg,
button.btn.btn-primary.btn-primary-outline.blackbg {
    // margin-bottom: 30px !important;
}

.modal-overlay.bottom-modal .modal-content .modal-footer {
    padding-bottom: 30px !important;
}

.custom-select.selected>div.select-header.form-control {
    padding-left: 11px !important;
}

.registerpage {
    .react-datepicker-wrapper {
        position: absolute;
        width: 20px;
        height: 20px;
        right: 16px;
        top: 16px;
        padding: 0 !important;
        z-index: 9;

        .react-datepicker__input-container {
            height: 20px;

            >input {
                height: 20px;
                border: none !important;
                background: transparent;
            }
        }
    }
}

.video-container {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    /* 16:9 aspect ratio */
    overflow: hidden;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 180px !important;
    border-radius: 25px;
}

.checkbox {
    position: relative;
    display: inline-block;
}

.circle {
    position: absolute;
    top: -4px;
    left: -4px;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    border: 2px solid red; // Color of the circle
    opacity: 0; // Initially hidden
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.animate {
    opacity: 1; // Show the circle
    transform: scale(1.2); // Scale up for effect
    animation: pulse 1s infinite; // Add pulse animation
}

@keyframes pulse {
    0% {
        transform: scale(1.2);
    }

    50% {
        transform: scale(1.4);
    }

    100% {
        transform: scale(1.2);
    }
}

.fw-bolder {
    font-weight: 700 !important;
}

.fw-bold {
    font-weight: 600 !important;
}

.pb-12 {
    padding-bottom: 12px;
}

.pt-24 {
    padding-top: 24px;
}

.pt-3 {
    padding-top: 12px !important;
}

.mb-4 {
    margin-bottom: 4px !important;
}

.mt-4 {
    margin-top: 24px;
}

.m-0 {
    margin-block: 0 !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: 4px !important;
}

.position-relative {
    position: relative !important;
}

.d-flex-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
}

.d-flex {
    display: flex;
}

section.container {
    max-width: 376px;
    margin: auto;
    padding-left: 16px;
    padding-right: 16px;
    font-family: "Mona Sans", sans-serif !important;
}

.card-section {
    border: 1px solid #E9E9E9;
    width: 100%;
    padding: 24px 16px;
    border-radius: 12px;
    padding-block: 24px;
}

.card-section h2 {
    color: var(--black);
    font-size: 20px;
    line-height: 100%;
    margin: 0;
}

.subheading {
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
    color: var(--balck-text);
}

.mobile-banner {
    max-width: 95px;
    width: 100%;
}

.mobile-banner img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.home-banner {
    max-width: 90px;
    width: 100%;
    position: absolute;
    top: 0px;
    right: 0;
}

.home-banne2 {
    max-width: 90px;
    width: 100%;
    position: absolute;
    top: -20px;
}

.home-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subheding-2 {
    color: var(--red-text);
    font-size: 14px;
    line-height: 100%;
    margin: 6px 0px;
}

.plan-pra {
    color: var(--balck-text);
    font-size: 10px;
    line-height: 14px;
    margin-top: 0;
}


.right-content {
    position: relative;
}

// .right-content::after { content: ''; position: relative;bottom: 0; background: url('./images/dot-1.png'); background-repeat: no-repeat; height: 20px;display: flex; background-size: 150px;}
.left-content {
    position: relative;
    padding-top: 40px;
}

.left-content-2 {
    position: relative;
    width: 100%;
}

// .left-content::before { content: ''; position: relative;left: 54px; background: url('./images/dot-2.png'); background-repeat: no-repeat; height: 20px;display: flex; background-size: 150px;}
// .left-content-2::after {content: '';position: relative;top: -61px;left: 54px;background: url('./images/dot-3.png');background-repeat: no-repeat;height:60px;display: flex;background-size: 150px;}
.tpl1 {
    position: relative;
    left: 0;
    width: 100%;
    top: -5px;
    margin-left: 84px;
}

.tpl2 {
    position: absolute;
    top: 54px;
    left: 85px;
    max-width: 130px;
}

.tpl3 {
    position: relative;
    max-width: 100%;
    padding-top: 85px;
    padding-left: 20px;
}

.left-content-3 {
    position: relative;
    width: 100%;
    top: -65px;
}

// .left-content-3::after {content: '';position: absolute;top: 68px;background: url('./images/dot-4.png');background-repeat: no-repeat;height: 30px;display: flex;background-size: 150px;width: 150px;right: 40px;}
.max-w-100 {
    max-width: 100px;
}

.pt-44 {
    padding-top: 28px;
}

.total-refund {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: rgba(0, 0, 0, 1);
    justify-content: center;
}

.checkmark-circle {
    width: 24px;
    height: 24px;
    background: #FFF;
    border: 1px solid rgba(31, 31, 31, 1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(31, 31, 31, 1);
    font-size: 12px;
    font-weight: bold;
    flex-shrink: 0;
}

.max-h-120 {
    max-height: auto;
}

.-mt-24 {
    margin-top: -28px;
    padding: 0px 16px;
}

.disclaimer {
    color: rgba(0, 0, 0, 0.5);
    font-size: 10px;
    line-height: 13px;
    text-align: center;
    padding-top: 24px;
}

.disclaimer p {
    margin-bottom: 20px;
}

.disclaimer p:last-child {
    margin-bottom: 0;
}

.proceed-btn {
    width: 100%;
    background: var(--black);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 9px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.proceed-btn:hover {
    background: #453a3a;
}

.how-btn {
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 36px;
    color: var(--balck-text);
    font-weight: 600;
    background: white;
    text-decoration: none;
    border: 1px solid #000000;
    border-radius: 100px;
    padding: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

// .how-btn:hover {
//     background: #e8e8e8;
// }

.improve-btn {
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 36px;
    color: white;
    font-weight: 600;
    background-color: hsl(0, 4%, 14%);
    text-decoration: none;
    border: 1px solid #000000;
    border-radius: 100px;
    padding: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

// .improve-btn:hover {
//     background-color: #453a3a;
// }

.card-section-1 {
    width: 100%;
    background-color: rgba(222, 243, 221, 1);
    padding: 16px;
    border-radius: 12px;
    padding-bottom: 50px;
}

.credit-banner {
    max-width: 150px;
    width: 100%;
}

.credit-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}


.midd-content {
    width: 100%;
}

.midd-content h2 {
    color: var(--black);
    font-size: 24px;
    line-height: 28px;
}

.midd-content p {
    color: var(--balck-text);
    font-size: 14px;
    line-height: 22px;
}

.pre-approved {
    position: absolute;
    font-weight: 600;
    top: 0;
    border-radius: 0px 0px 5px 5px;
    left: 16px;
    background-color: rgba(52, 199, 89, 1);
    font-size: 14px;
    padding: 2px 7px;
    color: white;
}

.midd-content .text-danger {
    color: #FF3E3E;
}

.pe-20 {
    padding-right: 50px;
}

.more-text {
    color: #1C43DF;
    text-decoration: none;
    font-size: 14px;
    line-height: 16px;
    font-weight: 600;
}

.mb-12 {
    margin-bottom: 12px;
}

.midd-content h3 {
    color: var(--black);
    font-size: 20px;
    line-height: 100%;
    margin-top: 0;
}

.midd-content h4 {
    color: var(--black);
    font-size: 18px;
    line-height: 24px;
    margin-block: auto;
}

.card-section-2 {
    background-color: #F6F3EC;
    border-radius: 12px;
    padding: 33px 16px 20px;
}

.ullist {
    padding-top: 10px;
    position: relative;
    padding-left: 18px;
}

.ullist li::before {
    content: '';
    position: absolute;
    left: 0;
    background: url('./images/check.png');
    background-size: 14px;
    background-repeat: no-repeat;
    height: 14px;
    width: 14px;
    margin-top: 4px;
}

.ullist li {
    font-size: 18px;
    line-height: 22px;
    color: var(--black);
    padding-bottom: 10px;
    font-weight: 500;
    list-style: none;
    margin-bottom: 10px;
}

.ullist-2 {
    padding-top: 10px;
    position: relative;
    padding-left: 18px;
}

.ullist-2 li::before {
    content: '';
    position: absolute;
    left: 0;
    margin-top: 2px;
    background: url('./images/check.png');
    background-size: 14px;
    background-repeat: no-repeat;
    height: 14px;
    width: 14px;
}

.ullist-2 li {
    font-size: 14px;
    line-height: 19px;
    color: var(--black);
    padding-bottom: 10px;
    font-weight: 500;
    list-style: none;
}

.date {
    color: #FF3E3E;
}

@media screen and (min-width:0) and (max-width:360px) {
    body {
        overflow-x: hidden !important;
    }

    .tpl2 {
        left: 65px;
    }

    .plan-pra {
        font-size: 9px;
    }

    .home-banner {
        max-width: 80px;
    }

    .mobile-banner {
        max-width: 85px;
    }

    .tpl2 {
        max-width: 110px;
    }

    .tpl1 {
        top: -25px;
    }

    .how-btn,
    .improve-btn {
        font-size: 11px;
    }
}

custom-bank-popup {
  .modal-header {
    font-size: 24px !important;
    font-weight: 700 !important;
    line-height: 28px !important;
  }
}

.hintText {
  .modal-header {
    font-size: 20px !important;
    font-weight: 700 !important;
    line-height: 28px !important;
  }
  font-weight: 400 !important;
  font-size: 12px !important;
  line-height: 20px !important;
  margin-top: 4px !important;
  color: var(--black) !important;
  text-align: left !important;
}
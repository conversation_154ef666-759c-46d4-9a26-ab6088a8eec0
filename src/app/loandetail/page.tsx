"use client"
import PageHeader from "../components/PageHeader"
import styles from './loandetail.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import Image from "next/image"
import upperCarrot from '../images/uppercarrot.png';
import downCarrot from '../images/downcarrot.png';
import info from '../images/info.png';
import percentimg from '../images/percenticon.svg'
import banklogo from '../images/banklogo.png'
import { useEffect, useState } from "react"
import LoanDetailPopup from "./DetailPopup"
import jsondata from './loandetailcontent.json'
import '../scss/button.scss'
import '../scss/form.scss'
import React from "react"
import { useDispatch, useSelector } from "react-redux"
import { setCibilChecked, setCustomerBankDetails, setIsChecked, setLoanWithdrawData, setPageError, setPageSuccess, toggleCollapse, toggleEmiModal } from "./loanDetails.slice"
import { apiRequest, fetchCommonApi } from "../utils/api"
import { ApiResponse } from "../store/interface/apiInterface"
import store, { RootState } from "../store/store"
import Link from "next/link"
import BottomPopup from "../components/popups/BottomPopup"
import EmiPopup from "./EmiAmountPopup"
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { useRouter, useSearchParams } from "next/navigation"
import { ENDPOINTS } from "../utils/endpoints"
import defaultbank from '../images/defaultbank.svg'
import { setCommonData } from "../store/slices/commonSlice"
import LoadingComp from "../component/loader"
import CtEvents from "../utils/Ctevents"
import { formatCurrency } from "../store/middleware/currencyFormatter"
import BankImage from "../components/BankImage"
import { debug } from "console"
import { logFirebaseEvent } from "../utils/firebaseAnalytics";
import { pushCleverTapEvent } from "../utils/cleverTapClient"

function LoanDetailPage() {
  useThemeColor('#f5f5f5')
  const dispatch = useDispatch()
  const router = useRouter()
  const searchParams = useSearchParams();
  const fromPostDisbursal = searchParams.get("postDisbursal");
  const { pageerror, pagesuccess, collapsedItems, isChecked, loanWithdrawData, emiModal, customerBankDetails } = useSelector((state: RootState) => state.loandetails);
  const { loanData } = useSelector((state: RootState) => state.amount);
  const [loader, setLoader] = useState<boolean>(true);
  const [openAccordion, setOpenAccordion] = useState<string | null>(null);
  const [creditShieldOpt, setCreditShieldOpt] = useState<boolean>(true);
  const [pcrOpt, setPcrOpt] = useState<boolean>(true);
  const toggleAccordion = (id: string) => {
    setOpenAccordion((prev) => (prev === id ? null : id));
  };
  const [isOpen, setIsOpen] = useState(false);
  const [popupName, setPopupName] = useState("");
  const [popupData, setPopupData] = useState(null);
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
  const [loaded, setLoaded] = useState<boolean>(false)




  const openPopup = (name: string, data: any) => {
    setPopupName(name);
    setPopupData(data);
    setIsOpen(true);
  };

  const closePopup = () => {
    setIsOpen(false);
    setPopupName("");
    setPopupData(null);
  };

  const errorEvent = ({ errorMessage }: { errorMessage: string }) => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Error Shown'
    const event_property = { "Screen Name": "LOC_Offer Details", "Product category": cteventsfromstorage.productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP", "Source": cteventsfromstorage.source, "Error Message": errorMessage, message: "Application unsuccessful", "Error_code": errorMessage, "Error_reason": errorMessage }
    pushCleverTapEvent({ eventName: event_name, eventData: event_property })
  }

  const fetchLocWithdraw = async () => {
    dispatch(setPageError(''));
    dispatch(setPageSuccess(''));
    try {
      const payload = {
        credit_shield_enable: creditShieldOpt,
        pcr_enable: pcrOpt,
        amount: loanData.amount,
        is_fip: loanData.is_fip,
        gateway_type: '',
        credit_shield_opt: creditShieldOpt,
        tenure: loanData.tenure_details[0].tenure,
        pcr_opt: pcrOpt,
        gateway_enable: 0
      };
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.getlocwithdraw, payload);
      if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
        if (response.mainData?.data.error) {
          dispatch(setPageError(response.mainData?.data.error))
          setLoader(false)
          setLoaded(true)
          errorEvent({ errorMessage: response.mainData?.data.error })
        } else {
          console.log(response.mainData.data)
          dispatch(setLoanWithdrawData(response.mainData.data));
          const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
          const event_name = 'LOC_Offer Details'
          const productcode = localStorage.getItem("product_code")

          const event_property = { "Disbursed Amount": response.mainData.data.final_disbursal_amount, "Loan amount": response.mainData.data.approved_amount, "Fee & charges": response.mainData.data.fee_and_charges, "Cibil Add on": payload.is_fip, "Loan Protect Add on": payload.credit_shield_opt, "Amount you will get": response.mainData.data.final_disbursal_amount, "EMI Start Date": response.mainData.data.first_emi_date, "No. of EMIs": response.mainData.data.installment_amounts.split(',').length, "EMI Amount": response.mainData.data.emi_amount, "Product category": productcode, "Segment": cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source, "Total Payable Amount": loanWithdrawData?.net_amount_payable, "EMI Deducted Bank": customerBankDetails[0]?.bank_name, "EMI Deducted A/C no.": customerBankDetails[0]?.account_number.slice(-4) }
          setCtdata({ event_name, event_property })
          await fetchUserBank()
          setLoader(false)
          setLoaded(true)

        }
      } else {
        dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
        setLoader(false)
        setLoaded(true)
        errorEvent({ errorMessage: response.mainData?.error_message || 'Unexpected error occurred.' })
      }
    } catch (error) {
      dispatch(setPageError('Something went wrong'));
      if (fromPostDisbursal) {
        setLoader(false);
        setLoaded(true);
        return;
      }
      router.push('/withdraw-amount')
      setLoader(false)
      setLoaded(true)
    }
  }
  const fetchUserBank = async () => {
    dispatch(setPageError(''));
    dispatch(setPageSuccess(''));

    try {

      const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.customerBankList);
      if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
        dispatch(setCustomerBankDetails(response.mainData.data));
      } else {
        dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
        errorEvent({ errorMessage: response.mainData?.error_message || 'Unexpected error occurred.' })
      }
    } catch (error) {
      //dispatch(setPageError('Error submitting address confirmation'));
    }
  }

  const transferLoanAmount = async () => {
    dispatch(setPageError(''));
    dispatch(setPageSuccess(''));
    setLoader(true)

    try {
      const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
      const event_name = 'Button Clicked'
      const productcode = localStorage.getItem("product_code")

      const event_property = {
        "CTA": "Transfer Loan amount", "Dropdown1": true, "Dropdown2": true, "Product category": productcode, "Segment": cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP", "Page shown": "LOC_Offer Details", "Disbursed Amount": loanWithdrawData.final_disbursal_amount, "Source": cteventsfromstorage?.source, "loan_id": loanWithdrawData.loan_id,
        "Status": ""
      }
      setCtdata({ event_name, event_property })
      const payload = {
        amount: loanWithdrawData.approved_amount,
        credit_report_fees: pcrOpt ? loanWithdrawData.credit_report_fees : 0,
        gateway_type: "",
        early_foreclosure_fee: 0.0,
        is_fcp: true, //loanWithdrawData?.is_fcp || false
        txn_id: loanWithdrawData?.txn_id || '',
        is_fip: loanWithdrawData?.is_fip || true,
        max_amount: creditShieldOpt ? loanWithdrawData?.max_amount : 0,
        gateway_fees: null,
        tenure: loanWithdrawData.tenure,
        plan_id: pcrOpt ? loanWithdrawData?.plan_id : 0
      }
      console.log(payload);
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.getlocconfirm, payload);
      if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const productcode = localStorage.getItem("product_code") || "n/a"

        const event_property = { "Disbursed Amount": loanWithdrawData?.final_disbursal_amount, "loan_id": loanWithdrawData?.loan_id || '', "Status": "Success", "Product category": productcode, "Segment": cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name: "LOC_Draw", event_property });
        
        setTimeout(() => {
          setCtdata({ event_name: "loc_pull", event_property });
          logFirebaseEvent({ eventName: "LOC_Draw", eventParams: event_property });
          logFirebaseEvent({ eventName: "loc_pull", eventParams: event_property });
        }, 100)
        
        setLoader(false);

        dispatch(setPageSuccess(response.mainData.data.msg));
        setTimeout(async () => {
          router.push('/success')
          // const commonData = await fetchCommonApi();
          // store.dispatch(setCommonData(commonData));
        }, 1000);
      } else {
        setLoader(false)
        dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
        errorEvent({ errorMessage: response.mainData?.error_message || 'Unexpected error occurred.' })
        setTimeout(() => {
          router.push(`/rejected?postDisbursal=${fromPostDisbursal}`)
        }, 1000);
      }
    } catch (error) {
      setLoader(false)

      dispatch(setPageError('Error in withdrawing amount'));
      errorEvent({ errorMessage: 'Error in withdrawing amount' })
      setTimeout(() => {
        router.push('/rejected')
      }, 1000);
    }
  }
  useEffect(() => {
    fetchUserBank();
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code")

    const event_property = { "Screen Name": 'LOC_Offer Details', "Product category": productcode, "Segment": cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source }
    setCtdata({ event_name, event_property })
  }, [])
  useEffect(() => {
    fetchLocWithdraw()
    setLoaded(false)
  }, [pcrOpt, creditShieldOpt])
  const formatAccountNumber = (accountNumber: any) => {
    return accountNumber?.replace(/\d{4}(?=\d)/g, '$& ');
  };
  const handleProtectionCheck = (e: any, forButton?: boolean) => {
    debugger;
    if (!forButton) {
      // if (e.target) {
      // if (e !== false)  {
      //   setCreditShieldOpt(true)
      // } else {
      //   setCreditShieldOpt(false)
      // }
      // }
      if (e == true) {
        setCreditShieldOpt(true)
      }
      if (e !== true) {
        setCreditShieldOpt(false)
      }
    } else {
      setCreditShieldOpt(true)
    }


  }
  const handleCibilCheck = (e: any, forButton?: boolean) => {
    debugger;
    if (!forButton) {
      if (e !== false) {
        setPcrOpt(true)
      } else {
        setPcrOpt(false)
      }
    } else {
      setPcrOpt(true)
    }
  }
  const uncheckCibil = () => {
    setPcrOpt(false)
  }
  return (
    <>{!loader ?
      <div>
        <>
          <div className={styles.header}>
            <PageHeader title="Loan details" gating={false} />
            <div className={styles.headercont}>

              <div className={styles.select}>

                <p>Disbursal Amount</p>
                <span>₹{formatCurrency(Number(loanWithdrawData?.final_disbursal_amount))}</span>
              </div>
              <div className={styles.icon}></div>
            </div>
          </div>
          <div className={styles.body}>
            <div className={styles.temsbox}>
              {loanWithdrawData?.emi_card_description && loanWithdrawData?.emi_card_description.includes(':') ? <span style={{ fontWeight: 600 }}>{loanWithdrawData?.emi_card_description.split(':')[0] + ': '}</span> : ''}
              {loanWithdrawData?.emi_card_description && loanWithdrawData?.emi_card_description.includes(':') ? loanWithdrawData?.emi_card_description.split(':')[1] : loanWithdrawData?.emi_card_description}
            </div>
            <div className={styles.loanamntbox}>
              <div className={styles.collapseChev} onClick={() => dispatch(toggleCollapse('cl-1'))} style={{ transform: collapsedItems['cl-1'] ? 'rotate(0deg)' : '' }}></div>
              <div>
                <div className={styles.amounttxt}>
                  {loanWithdrawData?.loan_amount_l}
                </div>
                <div className={styles.amount}>
                  ₹{formatCurrency(Number(loanWithdrawData?.approved_amount))}
                </div>
              </div>
              {collapsedItems['cl-1'] && (<>
                <div className={styles.pointer}>
                  <div
                    className={styles.amounttxt}
                    onClick={() => toggleAccordion("fee-charges")}
                  >
                    {loanWithdrawData?.fee_and_charges_l}
                    {openAccordion === "fee-charges" ?
                      <Image src={upperCarrot} alt="" />
                      : <Image src={downCarrot} alt="" />
                    }
                  </div>
                  <div className={styles.amount}>₹{formatCurrency(Number(loanWithdrawData?.fee_and_charges))}</div>
                  {openAccordion === "fee-charges" && (
                    <div className={styles.open}>
                      <div>
                        <div>{loanWithdrawData?.trans_fee_plus_gst_l}</div>
                        <div>₹{formatCurrency(Number(loanWithdrawData?.trans_fee_plus_gst))}</div>
                      </div>
                      <div>
                        <div>{loanWithdrawData?.processing_fee_plus_gst_l}</div>
                        <div>{formatCurrency(Number(loanWithdrawData?.processing_fee_plus_gst))}</div>
                      </div>
                      <div>
                        <div>{loanWithdrawData?.upfront_interest_l}</div>
                        <div>₹{formatCurrency(Number(loanWithdrawData?.upfront_interest))}</div>
                      </div>
                      <div className={styles.disclaimer}>
                        <div onClick={() => {
                          /*openPopup("Select Additional Benefits", {
                            popupname: "Select Additional Benefits",
                            content: {
                              sections: [
                                {
                                  heading: "CIBIL Report",
                                  content:
                                    "A summary of your credit health, creditworthiness, and utilization, including past lenders, loan details, and lender inquiries.",
                                  img: "../images/creditscore.png",
                                  amount: [{ crossed: `₹${formatCurrency(Number(loanWithdrawData?.credit_report_actual_fees))}` }, { current: formatCurrency(Number(loanWithdrawData?.credit_report_fees)) }],
                                },
                                {
                                  heading: "Loan Protection Plan",
                                  content:
                                    "A summary of your credit health, creditworthiness, and utilization, including past lenders, loan details, and lender inquiries.",
                                  img: "../images/loanprotectionshield.png",
                                  amount: [{ current: '₹' + formatCurrency(Number(loanWithdrawData?.max_amount)) }],
                                },
                              ],
                              buttons: [{ label: "Add To My Loan", action: "learnMore" }],
                            },
                          })*/
                        }
                        }>
                          <Image src={info} alt="" width={16} height={16} />
                        </div>
                        {loanWithdrawData?.upfront_interest_description}
                      </div>
                    </div>
                  )}
                </div>
                <div className={`${styles.pointer} ${styles.hiddencheckbox}`}>
                  <div
                    className={styles.amounttxt}
                    onClick={() => toggleAccordion("add-ons")}
                  >
                    {loanWithdrawData?.add_on_l}
                    {openAccordion === "add-ons" ?
                      <Image src={upperCarrot} alt="" />
                      : <Image src={downCarrot} alt="" />
                    }
                  </div>
                  <div className={`${styles.amount}`} onClick={() => toggleAccordion("add-ons")}>₹{formatCurrency(creditShieldOpt && pcrOpt ? (Number(loanWithdrawData?.max_amount || 0) + Number(loanWithdrawData?.credit_report_fees || 0)) : pcrOpt ? Number(loanWithdrawData?.credit_report_fees || 0) : creditShieldOpt ? Number(loanWithdrawData?.max_amount || 0) : 0)}</div>
                  {openAccordion === "add-ons" && (
                    <div className={styles.open}>
                      <div>
                        <div><input type="checkbox" name="" id="cibil" onChange={handleCibilCheck} checked={pcrOpt} /><label htmlFor="cibil"><span></span>
                          {/* {loanWithdrawData?.credit_report_fees_l} */}
                          Credit Report @&nbsp;<strong><span>₹{formatCurrency(Number(loanWithdrawData?.credit_report_actual_fees))}</span> ₹{formatCurrency(Number(loanWithdrawData?.credit_report_fees))}</strong>

                        </label> <div onClick={() => { openPopup("What is Credit Report?", jsondata.cibilreport) }}><Image src={info} alt="" width={16} height={16} /></div></div>
                        {/* <div> <span>₹{formatCurrency(Number(loanWithdrawData?.credit_report_actual_fees))}</span> ₹{formatCurrency(Number(loanWithdrawData?.credit_report_fees))}</div> */}
                      </div>
                      <div>
                        <div><input type="checkbox" name="" id="loanprotection" onChange={handleProtectionCheck} checked={creditShieldOpt} /><label htmlFor="loanprotection"><span></span>
                          {/* {loanWithdrawData?.insurance_l} */}
                          Credit Shield @&nbsp;<strong>₹{formatCurrency(Number(loanWithdrawData?.max_amount))}</strong>
                        </label> <div onClick={() => openPopup("Why is this Protection important", { content: { sections: loanWithdrawData?.insurance_desc, buttons: [{ label: "Protect Me", action: "" }] } })}><Image src={info} alt="" width={16} height={16} /></div></div>
                        {/* <div>₹{formatCurrency(Number(loanWithdrawData?.max_amount))}</div> */}
                      </div>
                    </div>
                  )}
                </div>
                <div>
                  <div className={styles.amounttxt}>
                    {'Amount you will get'}
                  </div>
                  <div className={styles.amount}>
                    ₹{formatCurrency(Number(loanWithdrawData?.final_disbursal_amount))}
                  </div>
                </div>
                <div>
                  <div className={styles.amounttxt}>
                    {loanWithdrawData?.first_emi_date_l}
                  </div>
                  <div className={styles.amount}>
                    {loanWithdrawData?.first_emi_date}
                  </div>
                </div>
                <div>
                  <div className={styles.amounttxt}>
                    {loanWithdrawData?.number_of_emi_l}
                  </div>
                  <div className={styles.amount}>
                    {loanWithdrawData?.tenure}
                  </div>
                </div>
              </>)}
              <div>
                <div className={styles.amounttxt}>
                  {loanWithdrawData?.emi_amount_l}
                  <div onClick={() => {
                    dispatch(toggleEmiModal(true))
                    //openPopup("EMI Amount", emidetails)
                  }}>
                    <Image src={info} alt="" width={20} height={20} />
                  </div>

                </div>
                <div className={styles.amount}>
                  ₹{formatCurrency(Number(loanWithdrawData?.emi_amount))}
                </div>
              </div>
            </div>

            <div className={styles.alternatevlybox}>
              <Image src={percentimg} alt="" />
              {loanWithdrawData?.bill_card_description}
            </div>

            <div className={`${styles.loanamntbox} ${styles.secondbox}`}>
              <div className={styles.collapseChev} onClick={() => dispatch(toggleCollapse('cl-2'))} style={{ transform: collapsedItems['cl-2'] ? 'rotate(0deg)' : '' }}></div>

              <div>
                <div className={styles.amounttxt}>
                  {loanWithdrawData?.disbursed_amount_l}
                </div>
                <div className={styles.amount}>
                  ₹{formatCurrency(Number(loanWithdrawData?.final_disbursal_amount))}
                </div>
              </div>
              {collapsedItems['cl-2'] && (<>
                <div>
                  <div className={styles.amounttxt}>
                    {loanWithdrawData?.trans_fee_plus_gst_l}
                  </div>
                  <div className={styles.amount}>
                    ₹{formatCurrency(Number(loanWithdrawData?.transaction_fee_bill_amount))}
                  </div>
                </div>
              </>)}
              <div className={`${styles.pointer} ${styles.hiddencheckbox}`}>
                <div
                  className={styles.amounttxt}
                  onClick={() => toggleAccordion("add-ons1")}
                >
                  {loanWithdrawData?.add_on_l}
                  {openAccordion === "add-ons1" ?
                    <Image src={upperCarrot} alt="" />
                    : <Image src={downCarrot} alt="" />
                  }
                </div>
                <div className={styles.amount} onClick={() => toggleAccordion("add-ons1")}>
                  ₹{formatCurrency(creditShieldOpt && pcrOpt ? (Number(loanWithdrawData?.max_amount || 0) + Number(loanWithdrawData?.credit_report_fees || 0)) : pcrOpt ? Number(loanWithdrawData?.credit_report_fees || 0) : creditShieldOpt ? Number(loanWithdrawData?.max_amount || 0) : 0)}
                </div>
                {openAccordion === "add-ons1" && (
                  <div className={styles.open}>
                    <div>
                      <div><input type="checkbox" name="" id="cibil" onChange={handleCibilCheck} checked={pcrOpt} /><label htmlFor="cibil"><span></span>
                        {/* {loanWithdrawData?.credit_report_fees_l} */}
                        Credit Report @&nbsp;<strong><span>₹{loanWithdrawData?.max_amount}</span>   ₹{loanWithdrawData?.credit_report_fees}</strong>
                      </label> <div onClick={() => openPopup("What is Credit Report?", jsondata.cibilreport)}><Image src={info} alt="" width={16} height={16} /></div></div>
                      {/* <div> <span>₹{loanWithdrawData?.max_amount}</span>   ₹{loanWithdrawData?.credit_report_fees}</div> */}
                    </div>
                    <div>
                      <div><input type="checkbox" name="" id="loanprotection" onChange={handleProtectionCheck} checked={creditShieldOpt} /><label htmlFor="loanprotection"><span></span>
                        {/* {loanWithdrawData?.insurance_l} */}
                        Credit Shield @&nbsp;<strong>₹{formatCurrency(Number(loanWithdrawData?.max_amount || 0))}</strong>
                      </label> <div onClick={() => openPopup("Why is this Protection important", { content: { sections: loanWithdrawData?.insurance_desc, buttons: [{ label: "Protect Me", action: "" }] } })}><Image src={info} alt="" width={16} height={16} /></div></div>
                      {/* <div>₹{formatCurrency(Number(loanWithdrawData?.max_amount || 0))}</div> */}
                    </div>
                  </div>
                )}
              </div>
              {collapsedItems['cl-2'] && (<>
                <div>
                  <div className={styles.amounttxt}>
                    {loanWithdrawData?.loan_amount_l}
                  </div>
                  <div className={styles.amount}>
                    ₹{formatCurrency(Number(loanWithdrawData?.bill_amount))}
                  </div>
                </div>
              </>)}
            </div>
            <div className={styles.bankdetail}>
              <p>EMI to be deducted from below account</p>
              {customerBankDetails && customerBankDetails.length ? <div className={styles.bankbox}>
                <div>
                  <p>{customerBankDetails[0]?.bank_name}</p>
                  <span>{customerBankDetails[0]?.account_number}</span>
                </div>
                {/* <Image src={customerBankDetails[0]?.bank_logo_url || defaultbank} alt="" /> */}

                <BankImage bankName={customerBankDetails[0]?.bank_name} ratioHeight={48} />
              </div> : null}
            </div>
            <div className={`consent-check ${styles.termsbox}`}><label><div className={`checkbox ${isChecked ? 'checked' : ''}`}>
              <input type="checkbox" checked={isChecked} onChange={() => {
                dispatch(setIsChecked(!isChecked))
              }} />
              <div className={`circle ${!isChecked ? 'animate' : ''}`}></div>

            </div>
              <p>I have read, understood and accept all the terms in the <Link href={loanWithdrawData?.financing_documents || ''} target="_blank">Financing Documents</Link> (including Loan Agreement, KFS, and Sanction Letter).</p></label></div>
            <div className="bottom-footer p-0">
              <button className={`btn btn-primary ${isChecked ? '' : 'disabled'}`} onClick={transferLoanAmount}>Transfer Loan Amount Now</button>
              <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
            </div>
          </div>
          {isOpen && popupData && (
            <LoanDetailPopup popupname={popupName} data={popupData} onClose={closePopup} isProtectionChecked={creditShieldOpt} handleProtectionCheck={handleProtectionCheck} handleCibilCheck={handleCibilCheck} uncheckCibil={uncheckCibil} isCibilChecked={pcrOpt} />
          )}
          <BottomPopup
            isOpen={emiModal}
            onClose={() => dispatch(toggleEmiModal(false))}
            title={loanWithdrawData?.emi_amount_l}
            buttons={[]}
          >
            <EmiPopup />
          </BottomPopup>
        </>
        {!loaded &&
          <LoadingComp faded={true} />
        }

      </div>
      :
      <LoadingComp faded={!loaded ? true : false} />

    }
      <CtEvents data={ctData} />
      {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

    </>
  )
}

export default LoanDetailPage
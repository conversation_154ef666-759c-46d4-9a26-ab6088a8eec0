.header {
    background: #F5F5F5;
    padding-left: 16px;
    padding-right: 16px;

    h2 {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 24px;
        line-height: 36px;
        color: #000;
        margin-top: 20px;
    }

    .headercont {
        padding-top: 0;
        display: flex;
        padding-bottom: 40px;
        justify-content: space-between;
        align-items: flex-end;


        >div {
            &.select {

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 25px;
                    color: var(--black);
                    padding-right: 30px;
                    margin-bottom: 10px;
                }

                span {
                    font-family: var(--font-mona-sans);
                    font-weight: 700;
                    font-size: 36px;
                    line-height: 35px;
                    color: #000;
                }
            }

            &.icon {
                min-width: 90px;
                height: 103px;
                display: flex;
                background: url(../images/gift.png) no-repeat;
                background-size: 100%;
            }
        }
    }

    header {
        min-height: auto;
        background: transparent;
        padding: 0;
        margin-bottom: 0;
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;

    a {
        text-decoration: none;
    }

    .temsbox {
        border: 1px solid #E9E9E9;
        border-radius: 8px;
        background: #fff;
        padding: 15px 12px;
        margin-top: -18px;
        font-family: var(--font-mona-sans);
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: var(--black);

        span {
            font-weight: 600;
        }

    }

    .alternatevlybox {
        background: #FFF8E0;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        padding: 18px 16px;
        display: flex;
        align-items: center;
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 21px;
        color: #090909;
        margin-top: 28px;
        z-index: -1;
        position: relative;
        margin-top: 40px;

        img {
            width: 32px;
            height: 32px;
            margin-right: 12px;
        }
    }

    .bankdetail {
        padding: 16px;
        border: 1px solid #E9E9E9;
        border-radius: 8px;
        background: #fff;
        margin-top: 36px;

        >p {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #000;
        }

        .bankbox {
            display: flex;
            justify-content: space-between;
            margin-top: 13px;

            >div {

                p,
                span {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 12px;
                    line-height: 20px;
                    color: #000;
                }

                span {
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 24px;
                }
            }

            img {
                width: 48px;
                height: 48px;
                border-radius: 50%;
            }
        }
    }

    .powerdby {
        margin-top: 18px;
        text-align: center;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #E9E9E9;
        flex-flow: column;
        padding-bottom: 5px;

        p {
            font-family: var(--font-mona-sans);
            font-weight: 400;
            font-size: 10px;
            line-height: 17px;
            color: #5A5A5A;
        }

        span {
            font-family: var(--font-mona-sans);
            font-weight: 400;
            font-size: 10px;
            line-height: 17px;
            color: #5A5A5A;
        }
    }

    .termsbox {
        margin-top: 13px;
        display: flex;
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        margin-top: 20px;
        margin-bottom: 20px;

        a {
            color: var(--primary-color);
            display: inline;
        }

        input {
            display: none;

            &+label {
                display: flex;
                align-items: center;

                span {
                    display: flex;
                    width: 18px;
                    height: 18px;
                    background: url(../images/blackuncheck.png) no-repeat;
                    margin-right: 5px;
                    background-size: 100%;
                }

                &+img {
                    margin-left: 5px;
                    cursor: pointer;
                }
            }


            &:checked {
                &+label {
                    span {
                        background: url(../images/blackcheck.png) no-repeat;
                        background-size: 100%;

                    }
                }
            }
        }
    }

    .pointer {
        cursor: pointer;

        &.hiddencheckbox {
            >div {
                &:nth-child(3) {
                    >div {
                        label {
                            pointer-events: none !important;

                            span {
                                width: 0 !important;
                                height: 0 !important;
                                overflow: hidden !important;
                            }
                        }
                    }
                }
            }
        }
    }

    .modal {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: flex-end;
        justify-content: center;
        z-index: 99;
    }

    .modalContent {
        background: #fff;
        padding: 20px 20px 12px 20px;
        border-radius: 20px 20px 0 0;
        width: 100%;
        max-width: 500px;
        position: relative;

        h2 {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 20px;
            line-height: 28px;
            margin-bottom: 24px;
            color: var(--black);
            padding-right: 40px;
        }

        >div {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            position: relative;

            &::after {
                content: "";
                background-color: #e9e9e9;
                width: calc(100% - 60px);
                height: 1px;
                position: absolute;
                bottom: -1px;
                right: 0;
            }

            >img {
                width: 48px;
                height: 48px;
                margin-right: 12px;
            }

            >div {
                padding-bottom: 12px;

                h3,
                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--black);
                }

                h3 {
                    display: flex;
                    justify-content: space-between;

                    div {
                        span {
                            font-family: var(--font-mona-sans);
                            font-weight: 600;
                            font-size: 14px;
                            line-height: 20px;
                            color: var(--black);

                            &.cross {
                                color: #757575;
                                text-decoration: line-through;
                                margin-right: 2px;
                            }
                        }
                    }

                }

                p {
                    font-weight: 400;
                }

                &.loanamtrow {
                    width: 100%;
                }
            }

            &.noborder {
                >div {
                    border: none !important;
                }
            }

            &.getcreditbox {
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: var(--font-mona-sans);
                font-weight: 500;
                font-size: 14px;
                line-height: 16px;
                color: var(--black);

                >div {
                    border: none;
                    padding: 0;
                    margin-left: 12px;

                }

                &:after {
                    display: none !important;
                }

                img {
                    width: 22px;
                    height: 24px;
                    margin-right: 10px;
                }
            }
        }

    }

    .closeButton {
        position: absolute;
        top: 16px;
        right: 16px;
        border: none;
        background: none;
        font-size: 18px;
        cursor: pointer;

        img {
            width: 24px;
            height: 24px;
        }
    }

    .buttons {
        margin-top: 20px;
        display: flex;
        gap: 10px;
        flex-flow: row wrap;

        button {
            width: 100%;
            height: 52px;
            background: var(--black);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 16px;

            &.skip {
                margin-top: 12px;
                background: transparent;
                border: 1px solid var(--black);
                color: var(--black);
            }
        }
    }
}




.loanamntbox {
    flex-flow: column;

    &::after {
        display: none;
    }
}

.btoomtxt {
    font-family: var(--font-mona-sans);
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: var(--black);
    display: inline-block;
    text-align: left;
    margin-top: 20px;

    a {
        color: var(--primary-color);
    }
}

.loanamntbox {
    padding: 8px 16px;
    border: 1px solid #E9E9E9;
    border-radius: 8px;
    background: #fff;
    margin-top: 12px;
    position: relative;

    .collapseChev {
        content: "";
        width: 35px;
        height: 35px;
        display: flex;
        background: url(../images/loanbottomicon.svg) no-repeat;
        background-size: 100%;
        position: absolute;
        bottom: -18px;
        right: 0;
        left: 0;
        margin: 0 auto;
        z-index: 9;
        border: none;
        transform: rotate(180deg);
    }

    &.secondbox {
        margin-top: -8px;
        // border: none;
    }

    >div {
        display: flex;
        justify-content: space-between;
        padding-top: 17px;
        padding-bottom: 17px;
        border-bottom: 1px solid #E9E9E9;
        flex-flow: row wrap;
        position: relative;

        &:last-child {
            border: none;
        }

        .amounttxt {
            font-family: var(--font-mona-sans);
            font-weight: 400;
            font-size: 14px;
            line-height: 15px;
            color: var(--black);
            display: flex;
            align-items: center;

            img {
                width: 24px;
                height: 24px;
                margin-left: 5px;

                &.info {
                    width: 20px;
                    height: 20px;
                }
            }
        }

        .amount {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 14px;
            line-height: 15px;
            color: var(--black);
        }

        .open {
            padding: 2px 12px 12px 12px;
            border-radius: 8px;
            background: #f5f5f5;
            width: 100%;
            margin-top: 12px;

            >div {
                display: flex;
                justify-content: space-between;
                padding: 7px 0;
                align-items: center;

                >div {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 12px;
                    line-height: 15px;
                    color: var(--black);
                    display: flex;
                    align-items: center;

                    input {
                        display: none;

                        &+label {
                            display: flex;
                            align-items: center;

                            span {
                                display: flex;
                                width: 18px;
                                height: 18px;
                                background: url(../images/blackuncheck.png) no-repeat;
                                margin-right: 5px;
                                background-size: 100%;
                            }

                            &+div>img {
                                margin-left: 5px;
                                cursor: pointer;
                            }
                        }


                        &:checked {
                            &+label {
                                span {
                                    background: url(../images/blackcheck.png) no-repeat;
                                    background-size: 100%;

                                }
                            }
                        }
                    }

                    >span {
                        text-decoration: line-through;
                        color: #909090;
                        margin-right: 10px;
                    }
                }

                &.disclaimer {
                    display: flex;
                    align-items: flex-start;
                    font-family: var(--font-mona-sans);
                    font-weight: 400;
                    font-size: 10px;
                    line-height: 14px;
                    color: var(--black);

                    img {
                        margin-right: 4px;
                    }
                }
            }
        }
    }
}

.consent {
    margin-top: 42px;
    margin-bottom: 24px;
}

.kycContent {
    text-align: center;
    margin-top: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .loader {
        width: 120px;
        height: 20px;
        -webkit-mask: radial-gradient(circle closest-side, #000 94%, #0000) left/20% 100%;
        background: linear-gradient(#FF002B 0 0) left/0% 100% no-repeat #ddd;
        margin: 0 auto;
        margin-bottom: 40px;
        animation: l17 1s infinite alternate steps(600);
    }

    @keyframes l17 {
        100% {
            background-size: 120% 100%
        }
    }
}

.modal {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    z-index: 99;
}

.modalContent {
    background: #fff;
    padding: 20px 20px 12px 20px;
    border-radius: 20px 20px 0 0;
    width: 100%;
    max-width: 500px;
    position: relative;

    h2 {
        font-family: var(--font-mona-sans);
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
        margin-bottom: 24px;
        color: var(--black);
        padding-right: 40px;
    }

    >div {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        position: relative;

        &::after {
            content: "";
            background-color: #e9e9e9;
            width: calc(100% - 60px);
            height: 1px;
            position: absolute;
            bottom: -1px;
            right: 0;
        }

        >img {
            width: 48px;
            height: 48px;
            margin-right: 12px;
        }

        >div {
            padding-bottom: 12px;

            h3,
            p {
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
            }

            h3 {
                display: flex;
                justify-content: space-between;

                div {
                    span {
                        font-family: var(--font-mona-sans);
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 20px;
                        color: var(--black);

                        &.cross {
                            color: #757575;
                            text-decoration: line-through;
                            margin-right: 2px;
                        }
                    }
                }

            }

            p {
                font-weight: 400;
            }

            &.loanamtrow {
                width: 100%;
            }
        }

        &.noborder {
            >div {
                border: none !important;
            }
        }

        &.getcreditbox {
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            color: var(--black);

            >div {
                border: none;
                padding: 0;
                margin-left: 12px;
            }

            img {
                width: 22px;
                height: 22px;
                margin-right: 10px;
            }

            &:after {
                display: none;
            }
        }
    }

    .termsbox {
        margin-top: 13px;
        display: flex;
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        margin-top: 20px;
        margin-bottom: 20px;

        a {
            color: var(--primary-color);
            display: inline;
        }

        input {
            display: none;

            &+label {
                display: flex;
                align-items: center;

                span {
                    display: flex;
                    width: 18px;
                    height: 18px;
                    background: url(../images/blackuncheck.png) no-repeat;
                    margin-right: 5px;
                    background-size: 100%;
                }

                &+img {
                    margin-left: 5px;
                    cursor: pointer;
                }
            }


            &:checked {
                &+label {
                    span {
                        background: url(../images/blackcheck.png) no-repeat;
                        background-size: 100%;

                    }
                }
            }
        }

    }

}

.closeButton {
    position: absolute;
    top: 16px;
    right: 16px;
    border: none;
    background: none;
    font-size: 18px;
    cursor: pointer;

    img {
        width: 24px;
        height: 24px;
    }
}

.buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-flow: row wrap;

    button {
        width: 100%;
        height: 52px;
        background: var(--black);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        font-size: 16px;

        &.skip {
            margin-top: 12px;
            background: transparent;
            border: 1px solid var(--black);
            color: var(--black);
        }
    }
}

.protectsliderparent {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    font-size: 14px !important;
    font-weight: 500 !important;

    img {
        width: 22px;
        height: 14px;
        margin-right: 12px;
    }

    .slidercheckboxdiv {
        margin-left: 10px;
    }
}

.noborder.termsbox.noborder1,
.noborder.buttons.noborder1 {
   &::after {
    display: none;
   }
}
.uncheckedboxparent {
    background: #F5F5F5;
    border-radius: 20px;
    padding: 23px 16px 12px 17px;
    width: 100%;
    p {
        display: flex;
        font-family: var(--font-mona-sans);
        font-size: 14px;
        font-weight: 500 !important;
        flex-flow: row wrap;
        &:before {
            content: "";
            display: flex;
            width: 21px;
            height: 19px;
            margin-right: 12px;
            background: url(../images/caution.png) no-repeat;
            background-size: 100%;
            position: relative;
            top: 3px;
        }
        div {
            width: calc(100% - 35px);
        }
    }
}
.skipbtnsparent {
    display: flex;
    align-items: center;
    margin-top: 18px;
    button {
        margin: 0 !important;
    }
    .skip {
        max-width: 90px;
        margin: 0 !important;
        margin-right: 20px !important;
    }
}
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface LoanState {
  pageerror: string | null;
  pagesuccess: string | null;
  loanWithdrawData: any;
  collapsedItems: Record<string, boolean>;
  isChecked: boolean;
  emiModal: boolean;
  customerBankDetails: any;
  cibilCheck: boolean;
  protectionCheck: boolean;
}

const initialState: LoanState = {
  pageerror: null,
  pagesuccess: null,
  loanWithdrawData: null,
  collapsedItems: {},
  isChecked: false,
  emiModal: false,
  customerBankDetails: null,
  cibilCheck: false,
  protectionCheck: false,
};

const loanDetailsSlice = createSlice({
  name: 'loandetails',
  initialState,
  reducers: {
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setLoanWithdrawData: (state, action: PayloadAction<string>) => {
      state.loanWithdrawData = action.payload;
    },
    toggleCollapse: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      state.collapsedItems[id] = !state.collapsedItems[id]; // Toggle collapse state
    },
    setIsChecked: (state, action: PayloadAction<boolean>) => {
      state.isChecked = action.payload;
    },
    setCibilChecked: (state, action: PayloadAction<boolean>) => {
      state.cibilCheck = action.payload;
    },
    setProtectionChecked: (state, action: PayloadAction<boolean>) => {
      state.protectionCheck = action.payload;
    },
    toggleEmiModal: (state, action: PayloadAction<boolean>) => {
      state.emiModal = action.payload;
    },
    setCustomerBankDetails: (state, action: PayloadAction<boolean>) => {
      state.customerBankDetails = action.payload;
    },
  },
});

export const {
  setPageError,
  setPageSuccess,
  setLoanWithdrawData,
  toggleCollapse,
  setIsChecked,
  toggleEmiModal,
  setCustomerBankDetails,
  setCibilChecked,
  setProtectionChecked,
} = loanDetailsSlice.actions;

export default loanDetailsSlice.reducer;

"use client"
import styles from './loandetail.module.scss'
import Image from "next/image";
import { useEffect, useState } from "react";
import modalclose from '../images/modalclose.png'
import creditmeter from '../images/creditmeter.png'
import shield from '../images/shield.png'
import SliderCheckbox from "../components/slidercheckbox";
import React from 'react';
interface Amount {
    current?: string;
    crossed?: string;
}
interface Section {
    heading: string;
    content: string;
    img: string;
    amount: Amount[];
    name: string;
    charges: string;
    desc?: string;
    icon?: string;
    title?: string;
}
interface emi {
    name: string;
    amount: string;
}

interface PopupContent {
    popupname: string;
    content: {
        sections: Section[];
        buttons?: { label: string; action: string }[];
    },
}
interface content {
    name: string,
    charges: string
}

interface LoanDetailPopupProps {
    popupname: string;
    data: PopupContent | null;
    onClose: () => void;
    handleProtectionCheck: any;
    handleCibilCheck: any;
    isProtectionChecked: boolean;
    isCibilChecked: boolean;
    uncheckCibil: any
}


const LoanDetailPopup = ({ popupname, data, onClose, handleProtectionCheck, isProtectionChecked, isCibilChecked, handleCibilCheck, uncheckCibil }: LoanDetailPopupProps) => {
    const [isChecked, setIsChecked] = useState(false);
    const [localCibilChecked, setLocalCibilChecked] = useState(isCibilChecked);
    const [localProtectionChecked, setLocalProtectionChecked] = useState(isProtectionChecked);
debugger;
    const handleConfirm = (e: any, isConfirmed: boolean) => {
        if (popupname === 'What is Credit Report?') {
            handleCibilCheck({ target: { checked: true } }, true);
            onClose();
        }

        if (popupname === 'Why is this Protection important') {

            handleProtectionCheck({ target: { checked: true } }, true);

            onClose();
        }
    };

    const handleLocalCibilToggle = (checked: boolean) => {
        setLocalCibilChecked(checked);
    };

    // Call this on Protection toggle
    const handleLocalProtectionToggle = (checked: boolean) => {
        setLocalProtectionChecked(checked);
    };

    const handleSliderChange = (checked: boolean) => {
        setIsChecked(checked);
        //console.log("Slider toggled:", checked);
        if (checked) {
            handleCibilCheck('', true)
        } else {
            uncheckCibil()
        }
    };
    if (!data) return null;
    const { sections, buttons } = data.content;

    // const handleProtectionCheckProp = (e: any, forButton?: boolean) => {
    //     if (popupname === 'What is Credit Report?') {
    //         handleCibilCheck(e, forButton)
    //         onClose()
    //     }
    //     if (popupname === 'Why is this Protection important') {
    //         handleProtectionCheck(e, forButton)
    //         onClose()
    //     }
    // }

    return (
        <div className={styles.modal}>
            {/* {data.content && */}
            <div className={styles.modalContent}>
                <button className={styles.closeButton} onClick={onClose}>
                    <Image src={modalclose} alt='' />
                </button>
                <h2>{popupname}</h2>
                {popupname !== 'EMI Amount' &&
                    <>
                        {sections?.map((section, index) => (
                            <div key={index + 'a'} className={index === sections.length - 1 ? `${styles.noborder}` : ''}>
                                <img src={section.img || section.icon} />
                                <div>
                                    <h3>{section.heading || section.title}
                                        {section.amount &&
                                            <div>
                                                {section.amount.map((amt, i) => (
                                                    <>
                                                        <span className={styles.cross}>{amt.crossed}</span>
                                                        <span>{amt.current}</span>
                                                    </>
                                                ))}
                                            </div>
                                        }
                                    </h3>
                                    <p>{section.content || section.desc}</p>
                                </div>
                            </div>
                        ))}
                    </>
                }
                {popupname === 'Why is this Protection important' &&
                    <div className={styles.getcreditbox}>
                        <Image src={shield} alt='' />
                        Protected by Credit Shield  
                        <SliderCheckbox
                            initialChecked={isProtectionChecked}
                            onChange={handleLocalProtectionToggle}
                        />
                    </div>
                }
                {popupname === 'What is Credit Report?' &&
                    <div className={`${styles.termsbox} ${styles.noborder} ${styles.noborder1}`}>
                        <div className={styles.protectsliderparent}>
                            {/* <input type="checkbox" id="termsp" name='termsp' checked={isProtectionChecked} onClick={handleProtectionCheck} />*/}
                            {/* <label htmlFor="termsp"> */}
                            {/* <span></span> */}
                            <Image src={creditmeter} alt='' />
                            <div>Get my Credit Report  </div>
                            {/* </label>  */}
                            <div className={styles.slidercheckboxdiv}>
                                <SliderCheckbox
                                    initialChecked={isCibilChecked}
                                    onChange={handleLocalCibilToggle}
                                />
                            </div>
                        </div>
                    </div>
                }
                <div className={`${styles.buttons} ${styles.noborder} ${styles.noborder1}`}>
                    {popupname === 'What is Credit Report?' ? (
                        localCibilChecked ? (
                            buttons?.map((btn, idx) => (
                                <button
                                    key={idx}
                                    className="btn btn-primary"
                                    onClick={(e) => handleConfirm(e, true)}
                                >
                                    {btn.label}
                                </button>
                            ))
                        ) : (
                            <div className={styles.uncheckedboxparent}>
                                <p>
                                    <div>
                                        <strong>Grow financially</strong> <br />
                                        <span>See how to improve your score and get better loan rates</span>
                                    </div>
                                </p>
                                <div className={styles.skipbtnsparent}>
                                    <button className={styles.skip} onClick={(e) => { handleCibilCheck(false, ''); onClose() }}>
                                        Skip
                                    </button>
                                    <>
                                        {buttons?.map((btn, idx) => (
                                            <button
                                                key={idx}
                                                className="btn btn-primary"
                                                onClick={(e) => handleConfirm(e, true)}
                                            >
                                                {btn.label}
                                            </button>
                                        ))}
                                    </>
                                </div>
                            </div>
                        )
                    ) : popupname === 'Why is this Protection important' ? (
                        localProtectionChecked ? (
                            buttons?.map((btn, idx) => (
                                <button
                                    key={idx}
                                    className="btn btn-primary"
                                    onClick={(e) => handleConfirm(e, true)}
                                >
                                    {btn.label}
                                </button>
                            ))
                        ) : (
                            <div className={styles.uncheckedboxparent}>
                                
                                <p>
                                    <div>
                                        <span>
                                            Skipping out may leave your family responsible for the full loan in unforeseen events.
                                        </span>
                                    </div>
                                </p>
                                <div className={styles.skipbtnsparent}>
                                    <button className={styles.skip} onClick={(e) => {
                                        handleProtectionCheck(false, '');
                                        onClose();
                                    }}>
                                        Skip
                                    </button>
                                    <>
                                        {buttons?.map((btn, idx) => (
                                            <button
                                                key={idx}
                                                className="btn btn-primary"
                                                onClick={(e) => handleConfirm(e, true)}

                                            >
                                                {btn.label}
                                            </button>
                                        ))}
                                    </>
                                </div>
                            </div>
                        )
                    ) : (
                        // For Select Additional Benefits or other popups
                        <>
                            {buttons?.map((btn, idx) => (
                                <button
                                    key={idx}
                                    className="btn btn-primary"
                                    onClick={(e) => handleConfirm(e, true)}

                                >
                                    {btn.label}
                                </button>
                            ))}
                        </>
                    )}

                    {popupname === 'Select Additional Benefits' && (
                        <button className={styles.skip} onClick={onClose}>
                            Skip
                        </button>
                    )}
                </div>
                {popupname === 'EMI Amount' &&
                    <>
                        <div className={styles.loanamntbox}>
                            {sections?.map((section, index) => (
                                <div className={styles.loanamtrow}>
                                    <div className={styles.amounttxt}>
                                        {section.name}
                                    </div>
                                    <div className={styles.amount}>
                                        {section.name === 'Annualised Percentage Rate' || section.name === 'Annualised Interest Rate' ?
                                            <>{section.charges}%</>
                                            : <>₹{section.charges}</>
                                        }

                                    </div>
                                </div>
                            ))}
                        </div>
                        <div className={styles.btoomtxt}>
                            Please refer to <a href="">Financing documents</a> for details of applicable interest and charges.
                        </div>
                    </>
                }
            </div>
            {/* } */}
        </div>
    );
};

export default LoanDetailPopup
"use client";
import React from "react";
import styles from './enachdet.module.scss'
import Image from "next/image";
import creditmeter from '../images/creditmeter.png'
import SliderToggle from '../component/SliderToggle';
interface CreditPopupProps {
  isChecked: boolean;
  onToggle: (val: boolean) => void;
  onGetReport: () => void; // 👈 new prop
}
const pageData = [
  {
    title: "Credit Score and Summary",
    description: "An overview of your credit health, creditworthiness and credit utilization.",
    icon: "https://static.stashfin.com/Android/cr_pop_1.svg"
  },
  {
    title: "Account Information",
    description: "Displays previous leaders and loan related details with them.",
    icon: "https://static.stashfin.com/Android/cr_pop_2.svg"
  },
  {
    title: "Enquiry Information",
    description: "Provide details of all the enquiries made by the lenders.",
    icon: "https://static.stashfin.com/Android/cr_pop_3.svg"
  }
];

const CreditPopup: React.FC<CreditPopupProps> = ({ isChecked, onToggle, onGetReport }) => {
  function handleSliderChange(checked: boolean): void {
    throw new Error("Function not implemented.");
  }

  return (
    <div className={styles.modalContent}>
      <div className={styles.modalItems}>
        {pageData.map((item, index) => (
          <div className={styles.modalItem} key={index}>
            <Image src={item.icon} alt={item.title} width={30} height={30} />
            <div>
              <h3>{item.title}</h3>
              <p>{item.description}</p>
            </div>
          </div>
        ))}
      </div>
      <div className={styles.toggleWrapper}>
        <Image src={creditmeter} alt=""/>
        <span>Get my Credit Report</span>
        <SliderToggle checked={isChecked} onChange={onToggle} />
      </div>
      <button disabled={!isChecked} className="btn btn-primary" onClick={onGetReport}>Get Report</button>
    </div>
  );
};

export default CreditPopup;

"use client";
import React, { useEffect, useState } from "react";
//import CryptoJS from 'crypto-js';
import styles from './enachdet.module.scss'
import Image from "next/image";
import SliderCheckbox from "../components/slidercheckbox";
import hasslefree from '../images/hasslefree.svg'
import accidentdeath from '../images/accidentdeath.svg'

interface AdditionalBenefitsPopupProps {
  //handleProtectionCheck: (e: any, forButton?: boolean) => void;
  //handleCibilCheck: (e: any, forButton?: boolean) => void;
}

const pageData = [
  {
    title: "Hassle-free & Paperless",
    description: "Get insured instantly with premium deducted from loan",
    icon: hasslefree
  },
  {
    title: "Accidental Death Benefit",
    description: "Stashfin loan amount repaid in case of accidental death",
    icon: accidentdeath
  }
];


const ProtectionPopup = ({ }: AdditionalBenefitsPopupProps) => {
  const [isCibilChecked, setIsCibilChecked] = useState<boolean>(false)
  const [checked, setchecked] = useState(false)

  //const [data, setData] = useState<any>(null)

  /*const generateChecksum = (body: any) => {
    const finalString = Object.keys(body)
      .filter(key => key !== "checksum")
      .map(key => `${key}=${body[key]}`)
      .join("&");

    const secretKey = process.env.NEXT_PUBLIC_SECRET_WEB_KEY;

    if (!secretKey) {
      throw new Error("Secret key is missing");
    }

    return CryptoJS.HmacSHA256(finalString, secretKey).toString();
  };*/

  /*const GetProtectionInfo = async () => {
    debugger
    try {
      const checksum = generateChecksum({ mode: 'getInsuranceDetails' })
      const payload = {
        mode: 'getInsuranceDetails',
        checksum
      }
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.cbInsuranceDetail, payload);
      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
        setData(response.mainData.data)
      } else {
        if (response.mainData && response.mainData.error_message) {
        }
        else if (response.mainData && response.mainData?.success?.toString().toLowerCase() === "false") {
        } else if (response.error) {
          // debugger;
        } else {
        }
      }
    } catch (error) {
    }
  }

  useEffect(() => {
    GetProtectionInfo()
  }, [])*/

  return (
    <div className={styles.modalContent}>
      <div className={styles.modalItems}>
        {pageData.map((item, index) => <div className={styles.modalItem} key={index}>
          <Image src={item.icon} alt={item.title} width={30} height={30} />
          <div>
            <h3>{item.title}</h3>
            <p>{item.description}</p>
          </div>
        </div>)}
      </div>
      <div className={`consent-check ${styles.consent}`} style={{ marginTop: '20px' }}>
                    <label htmlFor='termsprotect'>
                        <div className={!checked ? `checkbox` : `checkbox ${styles.checked}`}>
                            <input id='termsprotect' type="checkbox" onChange={() => {
                                setchecked(!checked)
                            }} />
                        </div>
                        <div>
                            {/* {data?.agreement?.length > 0 && ( */}
                                <p style={{ marginBottom: '20px', textAlign:'left' }}>By Clicking on "Protect Me" I agree to <span className='link'
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        // window.open(data.agreement[0]?.credit_line_detail_sheet, '_blank');
                                    }}
                                >Terms & Conditions</span></p>
                            {/* )} */}
                        </div>
                    </label>
                </div>
      <button className="btn btn-primary">Protect My Loan</button>
      <button className={`btn ${styles.loanprotectskip}`} style={{ background:'transparent !important', color:'#000', border:'1px solid #000'}}>Skip</button>
      {/*<div className={styles.creditReportText}>Get my Credit Report <SliderCheckbox initialChecked={isCibilChecked} onChange={() => setIsCibilChecked(!isCibilChecked)} /></div>*/}
    </div>
  );
};

export default ProtectionPopup;
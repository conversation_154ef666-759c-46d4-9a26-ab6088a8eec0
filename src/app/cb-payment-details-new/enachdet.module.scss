@import '../scss/variable.scss';

.pageContent {
    min-height: calc(100vh - 140px);
}

.enachDetails {
    list-style: none;
    margin: 0;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 400;
    border-radius: 8px;
    border: 1px solid #eeeeee;
    padding: 0px 16px;

    li {
        padding: 15px 0px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:not(:last-of-type) {
            border-bottom: 1px solid #eeeeee;
        }

        &:last-of-type {
            font-weight: 700;
        }

        span:nth-of-type(2) {
            font-weight: 700;
        }
    }
}

.checked {
    &:before {
        background-image: url('../images/bluecheck.svg');
        background-size: 24px;
        background-position: -3px;
        border: none;
    }
}

p.changepage {
    font-family: var(--font-mona-sans);
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    padding: 16px 0;
    cursor: pointer;
}

.modalItems {
    .modalItem {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        position: relative;
        text-align: left;

        &:after {
            content: "";
            background-color: #e9e9e9;
            width: calc(100% - 60px);
            height: 1px;
            position: absolute;
            bottom: -1px;
            right: 0;
        }

        >img {
            width: 48px;
            height: 48px;
            margin-right: 12px;
        }

        >div {
            padding-bottom: 12px;

            h3 {
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
            }

            p {
                font-family: var(--font-mona-sans);
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
            }
        }

        &:last-child {
            &::after {
                display: none;
            }
        }
    }
}

.creditReportText {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: var(--font-mona-sans);
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    color: var(--black);
}

.toggleWrapper {
    display: flex;
    margin: 24px 0;
    padding-left: 60px;
    align-items: center;
    font-family: var(--font-mona-sans);
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    color: var(--black);

    img {
        margin-right: 10px;
        width: 23px;
        height: 14px;
    }

    label {
        margin-left: 15px
    }
}

.gradientCircle {
    position: absolute;
    top: 0;
    left: 0;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(144deg, rgba(255, 203, 203, 1) 30%, rgba(255, 255, 255, 1) 0%);
    filter: blur(69.4px); // Figma blur value
    z-index: -1;
    pointer-events: none;
}

.planidheader {
    background: #000000;
    display: flex;
    align-items: center;
    display: flex;
    align-items: center;
    background: #000;
    height: 44px;
    padding: 0 15px;
    border-radius: 12px 12px 0 0;
    color: #fff;
    font-family: var(--font-mona-sans);
    font-weight: 700;
    font-size: 16px;

    img {
        margin-right: 12px;
    }
}

.newpaymentdetails {
    border-radius: 0 0 12px 12px;
    border-top: none;
    margin-top: 0;
    border-color: #000;

    li {
        span {
            span {
                color: #1F1F1F80;
            }
        }

        &:nth-last-child(2) {
            border-color: #000;
        }
    }

}

.recommended {
    background: #FFDA50;
    padding: 4px 7px;
    border-radius: 4px;
    color: #000;
    font-family: var(--font-mona-sans);
    font-weight: 700;
    font-size: 10px;
    border: none;
    text-transform: uppercase;
    margin-left: 10px;
}

.popular {
    color: #000000;
    padding: 4px 7px;
    border-radius: 4px;
    background: #FFDA50;
    font-family: var(--font-mona-sans);
    font-weight: 700;
    font-size: 10px;
    border: none;
    text-transform: uppercase;
    margin-left: 10px;
}

.base {
    color: #000;
    padding: 4px 7px;
    border-radius: 4px;
    background: #FFDA50;
    font-family: var(--font-mona-sans);
    font-weight: 700;
    font-size: 10px;
    border: none;
    text-transform: uppercase;
    margin-left: 10px;
}

.loanprotectskip {
    background: transparent !important;
    max-width: 100%;
    width: 100%;
    height: 52px;
    background-color: var(--text-color);
    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    border: none;

    &:hover {
        background: transparent !important;
        background-color: transparent !important;
    }
}

.secondlastemibox {
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    background: #FFF8E0;
    border-radius: 12px;
    margin-top: 16px;
    padding: 0 12px;
    font-family: var(--font-mona-sans);
    font-weight: 500;
    font-size: 14px;
    color: var(--black);

    img {
        margin-right: 10px;
    }
    span {
        font-weight: 600;
        margin-left: auto;
    }
}
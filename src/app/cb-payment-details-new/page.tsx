'use client';

import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import styles from './enachdet.module.scss'
import infoIcon from '../images/infoblack.svg'
import Image from 'next/image';
import { apiRequest } from "../utils/api"
import { ENDPOINTS } from "../utils/endpoints"
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from "../store/store"
import { setPageError, setPageSuccess } from '../register/register.slice';
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { initiatePayment } from '../utils/bridge';
import { useRouter } from "next/navigation";
import { formatCurrency } from '../store/middleware/currencyFormatter';
import BottomPopup from '../components/popups/BottomPopup';
import ProtectionPopup from './ProtectionPopup';
import CreditPopup from './CreditPopup';
import BuilderPopup from './BuilderPopup';
import CtEvents from '../utils/Ctevents';
import selectedplan from "../images/selectedplan.svg";
import users from "../images/users.svg";
import crown from "../images/crown.svg";
import secondemicalender from "../images/secondemicalender.svg";




const EnachDetails = () => {
    const dispatch = useDispatch();
    const [checked, setchecked] = useState(true)
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [creditfee, setcreditfee] = useState(0)
    const [isCreditReportIncluded, setIsCreditReportIncluded] = useState(true);
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [data, setData] = useState<any>([])
    const [modalType, setModalType] = useState<string>('')
    const router = useRouter()
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);;
    const [planid, setPlanid] = useState<any>('')



    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        landing_page: string,
        profile_pic_upload_url: string,
        status: any
    }
    useEffect(() => {
        const planid = typeof window !== "undefined" ? localStorage.getItem('cb_plan_id') : null;

        setPlanid(planid)
        const GetPaymentSummary = async () => {
            try {
                const planid = typeof window !== "undefined" ? localStorage.getItem('cb_plan_id') : null;
                const planIdNumber = planid ? parseInt(planid, 10) : 0;

                // ✅ Redirect if planIdNumber is not 1, 2, or 3
                // if (![1, 2, 3].includes(planIdNumber)) {
                //     router.push('/cbbuy-new');
                //     return; // exit early to prevent further execution
                // }
                const payload = {
                    "plan_id": planid ? parseInt(planid, 10) : 1
                };
                const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.cb_payment_summary, payload);
                if (response.mainData?.success?.toString().toLowerCase() === "true") {
                    setData(response.mainData.data);
                } else {
                    dispatch(setPageError(response.mainData?.error_message || "An error occurred"));
                }
            } catch (error) {
                //console.error("Error fetching payment summary:", error);
            }
        };

        GetPaymentSummary();
    }, []);
    const handleGetReportClick = () => {
        setIsModalOpen(false)
        // You can trigger analytics, API calls, or UI updates here
    };
    useEffect(() => {
        if (
            data?.payment &&
            Array.isArray(data.payment) &&
            data.payment.length > 0 &&
            data.payment[0]?.plan_details
        ) {
            if (isCreditReportIncluded) {
                setcreditfee(0);
            } else {
                setcreditfee(data.payment[0].plan_details.cr_amount || 0);
            }
        }
    }, [isCreditReportIncluded, data]);
    const MakeCbPayment = () => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")
        const buyamount = localStorage.getItem('buyamount')

        const event_property = { "Screen Name": "cb_payment_summary", "Product category": 'cb', "CTA": "confrim and continue", "plan": buyamount, "Builder Fee": data.payment[0]?.plan_details?.platform_fee, "EMI": data.payment[0]?.plan_details?.no_of_emi, "total Payable amount": data.payment[0]?.plan_details?.convenience_fee - creditfee, "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })

        initiatePayment({
            amount: data.payment[0]?.plan_details?.convenience_fee - creditfee,
            paymentFlag: 33,
            redirection_url_after_poll: `https://${window.location.hostname}/loading`,
            bill_id: data.payment[0].plan_id,
            cr_amount: data.payment[0].plan_details.cr_amount,
            cr_plan_id: data.payment[0].plan_details.cr_plan_id,
            loan_id: data.payment[0].loan_id,
        })
    }
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_payment_summary", "Product category": 'cb', "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    // utils/dateHelpers.ts (optional file if reused elsewhere)
    // OR just add this above your component

    const getFormattedDate = (): string => {
        const today = new Date();
        const day = today.getDate();

        const getMonthName = (date: Date) =>
            date.toLocaleString("default", { month: "long" });

        const getOrdinal = (n: number): string => {
            const s = ["th", "st", "nd", "rd"];
            const v = n % 100;
            return n + (s[(v - 20) % 10] || s[v] || s[0]);
        };

        let targetDate: Date;

        if (day > 28) {
            const month = today.getMonth();
            const year = today.getFullYear();
            targetDate = new Date(year, month + 2, 1); // 1st of month after next
        } else {
            targetDate = new Date(today);
            targetDate.setDate(today.getDate() + 30);
        }

        const formattedDay = getOrdinal(targetDate.getDate());
        const formattedMonth = getMonthName(targetDate);
        const formattedYear = targetDate.getFullYear();

        return `${formattedDay} ${formattedMonth} ${formattedYear}`;
    };


    return (
        <div className={`external-wrapper`} style={{ position: 'relative' }}>
            <div className={styles.gradientCircle}></div>

            <PageHeader title='Payment Summary' nobg={true} call={true} />
            <div className={`page-content ${styles.pageContent}`}>
                <div className={styles.planidheader}>
                    <Image src={selectedplan} alt="" className={styles.selectedplan} />
                    Plan {planid} {' '}
                    {planid == 1 &&
                        <>
                            <div className={styles.recommended}>Recommended</div>
                        </>
                    }
                    {planid == 2 &&
                        <>
                            <div className={styles.popular}>Popular</div>

                        </>
                    }
                    {planid == 3 &&
                        <>
                            <div className={styles.base}>Base Plan</div>

                        </>
                    }
                </div>
                {data?.payment?.length > 0 && (
                    <ul className={`${styles.enachDetails} ${styles.newpaymentdetails}`}>
                        <li><span>1st EMI <span>(Refundable)</span></span><span>₹{formatCurrency(Number(data.payment[0]?.plan_details?.emi_amount || 0))}</span></li>

                        <li><span>CIBIL Score Improvement Fee <br /><span>Why am I being charged?&nbsp;<Image src={infoIcon} alt='Info' style={{ cursor: 'pointer' }} onClick={() => {
                            setModalType('builder')
                            setIsModalOpen(true)
                        }} /></span></span><span>₹{formatCurrency(Number(data.payment[0]?.plan_details?.platform_fee))}</span></li>
                        {/* <li><span>Loan Protection Plan <Image src={infoIcon} alt='Info' style={{ cursor: 'pointer' }} onClick={() => {
                            setModalType('insurance')
                            setIsModalOpen(true)
                        }} /></span><span>₹</span></li> */}
                        <li><span>Credit health Report <Image src={infoIcon} style={{ cursor: 'pointer' }} alt='Info' onClick={() => {
                            setModalType('credit')
                            setIsModalOpen(true)
                        }} /></span>
                            <span>
                                {isCreditReportIncluded && (
                                    <span style={{ textDecoration: 'line-through' }}>
                                        ₹{formatCurrency(Number(data.payment[0]?.plan_details?.cr_cross_amount || 0))}
                                    </span>
                                )}
                                {isCreditReportIncluded ? (
                                    <span style={{ color: '#000' }}> ₹{formatCurrency(Number(data.payment[0]?.plan_details?.cr_amount || 0))} </span>
                                ) : (
                                    <span>N/A</span>
                                )
                                }

                            </span>
                        </li>
                        <li><span>{data.payment[0]?.plan_details?.gst_title || ''}</span><span>₹{formatCurrency(Number(data.payment[0]?.plan_details?.gst_amount || 0))}</span></li>
                        {/* <li><span>Insurance worth ₹1,000 <Image src={infoIcon} style={{ cursor: 'pointer' }} alt='Info' onClick={() => {
                            setModalType('insurance')
                            setIsModalOpen(true)
                        }} /></span><span>₹{formatCurrency(Number(30))}</span></li> */}

                        <li><span>Total Payable Amount</span>
                            <span>
                                ₹{formatCurrency(Number(isCreditReportIncluded ? data.payment[0]?.plan_details?.convenience_fee : data.payment[0]?.plan_details?.convenience_fee - data.payment[0]?.plan_details?.cr_amount))}
                            </span>
                        </li>
                    </ul>
                )}
                <div className={styles.secondlastemibox}>
                    <Image src={secondemicalender} alt='' />
                    {data?.payment?.length > 0 && (
                        <>
                            2nd (Last) EMI of ₹{Number(data.payment[0].plan_details?.balance_amount)} Date
                        </>
                    )}
                    <span>{getFormattedDate()}</span>
                </div>

                <div className={`consent-check ${styles.consent}`} style={{ marginTop: '20px' }}>
                    <label htmlFor='terms'>
                        <div className={!checked ? `checkbox` : `checkbox ${styles.checked}`}>
                            <input id='terms' type="checkbox" onChange={() => {
                                setchecked(!checked)
                            }} />
                        </div>
                        <div>
                            {data?.agreement?.length > 0 && (
                                <p style={{ marginBottom: '20px' }}>I have read and agreed to the <span className='link'
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        window.open(data.agreement[0]?.credit_line_detail_sheet, '_blank');
                                    }}
                                >Credit Line Details Sheet,</span> <span className='link' onClick={() => window.open(data.agreement[0].payment_undertaking, '_blank')}>Payment Undertaking</span>, <span className='link' onClick={() => window.open(data.agreement[0].tc_online_disbursal, '_blank')}>T&C <span>for</span> Online Disbursement Request,</span> <span className='link' onClick={() => window.open(data.agreement[0].schedule_for_charges, '_blank')}>Schedule of Charges</span>, <span className='link' onClick={() => window.open(data.agreement[0].acknowledge_by_borrower, '_blank')}>Acknowledgement by Borrower</span> and <span className='link' onClick={() => window.open(data.agreement[0].venacular_declaration, '_blank')}>Vernacular Declaration.</span></p>
                            )}
                        </div>
                    </label>
                </div>
                <div className='bottom-footer mt-auto'>
                    <button type="button" className={`btn btn-primary`} disabled={!checked} onClick={() => MakeCbPayment()}>Continue</button>
                    <p className={`${styles.changepage} pb-0`} onClick={() => router.push('/cbbuy-new')}>Want to change your plan?</p>
                </div>
            </div>
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
            <BottomPopup
                isOpen={isModalOpen}
                onClose={() => {
                    setIsModalOpen(false);
                }}
                title={modalType === 'insurance' ? 'Add Loan Protection Plan' : modalType === 'builder' ? 'What is Credit Repair Fee ?' : modalType === 'credit' ? 'What\'s in your Credit Report?' : ''}
            >
                <div>
                    {/*<AdditionalBenefitsPopup />
                    <ProtectionPopup />
                    <CibilPopup />*/}
                    {modalType === 'insurance' ? <ProtectionPopup /> : null}
                    {modalType === 'builder' ? <BuilderPopup /> : null}
                    {modalType === 'credit' ? <CreditPopup
                        isChecked={isCreditReportIncluded}
                        onToggle={(val) => setIsCreditReportIncluded(val)}
                        onGetReport={handleGetReportClick}
                    /> : null}
                </div>
            </BottomPopup>
            <CtEvents data={ctData} />

        </div>
    );
};

export default EnachDetails;
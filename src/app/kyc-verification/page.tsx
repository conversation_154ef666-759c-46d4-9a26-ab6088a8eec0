'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import kycImg from '../images/kyc-verification.svg'
import ShieldIcon from '../images/shield-icon.svg'
import styles from './kyc.module.scss'
import '../scss/form.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import { apiRequest } from '../utils/api'; // Common API call file
import { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { setPageError } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { ENDPOINTS } from '../utils/endpoints';
import { setIsChecked, setReadMore } from './kyc.slice';
import PageWrapper from '../components/PageWrapper';
import LoadingComp from '../component/loader';
import CtEvents from '../utils/Ctevents';
import { useRouter } from "next/navigation";


function KYCVerification() {
    const router = useRouter()
    const dispatch = useDispatch();
    const { pageerror, pagesuccess, readMore, isChecked } = useSelector((state: RootState) => state.kyc);
    const [loading, setLoading] = useState(false)
    const [loaded, setLoaded] = useState<boolean>(false)
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);


    interface SaveBasicDetailsResponse {
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        redirection_url: string
    }
    const StartKyc = async () => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "CTA": "Continue", "Page Name": "KYC Initiated", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP" }
        setCtdata({ event_name, event_property })
        setLoading(true)
        setLoaded(false)
        try {
            const payload = {
                "okycType": "aadhaar"
            }
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.generateokycurl, payload);
            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                const event_name = 'KYC Initiated'
                const productcode = localStorage.getItem("product_code")
                const event_property = { "Product category": productcode, "Flow": "Digilocker", "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
                setCtdata({ event_name, event_property })
                localStorage.setItem("digilocker_session_id", response.mainData.data.sessionId)
                localStorage.setItem("digilocker_auth_token", response.mainData.data.auth_token)
                window.location.assign(response.mainData.data.redirect_url)
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }

                else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
                    dispatch(setPageError(response.mainData.error_message as string | "null"))
                } else if (response.error) {
                } else {
                }
            }
        } catch (error) {
            setLoaded(true)
            setLoading(false)
        }
    }
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "KYC Initiated", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
        console.log("Component re-rendered!");

    }, [])
    const readmoreclick = () => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "CTA": "Read More", "Page Name": "KYC Initiated", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP" }
        setCtdata({ event_name, event_property })
    }
    const checkclick = () => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "CTA": "Checkbox", "Page Name": "KYC Initiated", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP" }
        setCtdata({ event_name, event_property })
    }

    return (
        <PageWrapper>
            {loaded ?
                <div className='external-wrapper'>
                    <PageHeader title='Instant KYC verification' para='Government regulations require your PAN & Aadhaar identity to be verified to process your request' />

                    <div className='page-content'>
                        <div className={styles.kycContent}>
                            <div className={styles.kycCard}>
                                <Image src={kycImg} alt="Congratulations" />
                                <h3>Aadhaar KYC</h3>
                                <p>Your Aadhaar KYC, mandated by UIDAI under RBI guidelines, ensures quick identity verification and compliance, preventing fraud while protecting your eligibility.{!readMore ? '..' : ''} {!readMore && <span className={`link ${styles.readmore}`} onClick={() => {
                                    dispatch(setReadMore(true));
                                    readmoreclick();
                                }}>Read More</span>}{readMore ? <span> It eliminates paperwork, enables instant verification from anywhere, and speeds up loan approvals. We capture minimal data, never store Aadhaar plainly, and use encryption to keep your information secure at all times. </span> : null}{readMore && <span className={`link ${styles.readmore}`} onClick={() => dispatch(setReadMore(false))}>Read Less</span>}</p>
                            </div>
                        </div>

                        {/* <div className={`consent-check ${styles.consent}`}>
                            <label>
                                <div className={`checkbox ${isChecked ? 'checked' : ''}`}>
                                    <input type="checkbox" checked={isChecked} onChange={() => {
                                        dispatch(setIsChecked(!isChecked));
                                        checkclick()
                                    }} />
                                </div>
                                <div>
                                    <p>I consent to Akara initiating and completing the KYC checks as per applicable regulatory guidelines.</p>
                                </div>
                            </label>
                        </div> */}
                        <div className="bottom-footer p-0">
                            <div className={`consent-check ${styles.consent}`}>
                                <label>
                                    <div className={`checkbox ${isChecked ? 'checked' : ''}`}>
                                        <input type="checkbox" checked={isChecked} onChange={() => {
                                            dispatch(setIsChecked(!isChecked));
                                            checkclick()
                                        }} />
                                    </div>
                                    <div>
                                        <p>I consent to Akara initiating and completing the KYC checks as per applicable regulatory guidelines.</p>
                                    </div>
                                </label>
                            </div>
                            <p className="secure-tag">
                                <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                            </p>
                            <button type="submit" className={`btn btn-primary ${isChecked && !loading ? '' : 'disabled'}`} onClick={StartKyc}>Continue</button>
                        </div>
                    </div>

                </div>
                :

                <LoadingComp />
            }
            <CtEvents data={ctData} />
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
}

export default KYCVerification;

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface kycState {
    pageerror: string | null;
    pagesuccess: string | null;
    readMore: boolean;
    isChecked: boolean
}

// Define the initial state
const initialState: kycState = {
    pageerror: null,
    pagesuccess: null,
    readMore: false,
    isChecked: true,
};

// Create the slice
const kycSlice = createSlice({
    name: 'kyc',
    initialState,
    reducers: {
        setPageError: (state, action: PayloadAction<string | null>) => {
            state.pageerror = action.payload;
        },
        setPageSuccess: (state, action: PayloadAction<string | null>) => {
            state.pagesuccess = action.payload;
        },
        setReadMore(state, action: PayloadAction<boolean>) {
            state.readMore = action.payload;
        },
        setIsChecked(state, action: PayloadAction<boolean>) {
            state.isChecked = action.payload;
        },
    },
});

// Export actions
export const {
    setPageError,
    setPageSuccess,
    setReadMore,
    setIsChecked
} = kycSlice.actions;

// Export the reducer
export default kycSlice.reducer;

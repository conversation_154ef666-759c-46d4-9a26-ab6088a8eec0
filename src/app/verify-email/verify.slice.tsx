import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface VerifyState {
    otp: string | null;
    pageerror: string | null;
    pagesuccess: string | null;
    isOTPModalOpen: boolean;
    timer: number;
    email: string;
    otpSent: boolean;
    otperror: string | null;
    otpsuccess: string | null;
}

// Define the initial state
const initialState: VerifyState = {
    otp: null,
    pageerror: null,
    pagesuccess: null,
    isOTPModalOpen: false,
    email: '',
    timer: 30,
    otpSent: false,
    otperror: '',
    otpsuccess: ''
};

// Create the slice
const otpSlice = createSlice({
    name: 'otp',
    initialState,
    reducers: {
        setPageError: (state, action: PayloadAction<string | null>) => {
            state.pageerror = action.payload;
        },
        setPageSuccess: (state, action: PayloadAction<string | null>) => {
            state.pagesuccess = action.payload;
        },
        openOTPModal: (state) => {
            state.isOTPModalOpen = true;
        },
        closeOTPModal: (state) => {
            state.isOTPModalOpen = false;
        },
        setTimer: (state, action: PayloadAction<number>) => {
            state.timer = action.payload;
        },
        setOtpSent: (state, action: PayloadAction<boolean>) => {
            state.otpSent = action.payload;
        },
        setEmail: (state, action: PayloadAction<string>) => {
            state.email = action.payload;
        },
        setOtp: (state, action: PayloadAction<string | null>) => {
            state.otp = action.payload;
        },
        setOtpError: (state, action: PayloadAction<string | null>) => {
            state.otperror = action.payload;
        },
        setOtpSuccess: (state, action: PayloadAction<string | null>) => {
            state.otpsuccess = action.payload;
        },
    },
});

// Export actions
export const {
    setPageError,
    setPageSuccess,
    openOTPModal,
    closeOTPModal,
    setTimer,
    setOtpSent,
    setEmail,
    setOtp,
    setOtpError,
    setOtpSuccess
} = otpSlice.actions;

// Export the reducer
export default otpSlice.reducer;

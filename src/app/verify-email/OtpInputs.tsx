import React, { useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setOtp, setOtpError, setOtpSent, setPageError, setTimer } from "./verify.slice";
import { RootState } from "../store/store";
import styles from './verify.module.scss'

const OtpInputs: React.FC<{ resendOtp: (email: string) => void }> = ({ resendOtp }) => {
    const dispatch = useDispatch();
    const { otp, timer, otpSent, otperror, email } = useSelector((state: RootState) => state.otp);

    const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(6).fill(null));

    // Ensure OTP always has 4 characters (empty if not set)
    const otpArray = Array.from({ length: 6 }, (_, i) => otp?.[i] || "");

    // Handle OTP input changes
    const handleChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/[^0-9]/g, ""); // Allow only numbers
        if (!value) return;

        const newOtp = [...otpArray];
        newOtp[index] = value.charAt(0); // Store only one digit
        dispatch(setOtp(newOtp.join("")));

        // Move focus to the next field
        if (index < 5) {
            inputRefs.current[index + 1]?.focus();
        }
    };

    // Handle Backspace: Clear input and move focus back
    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        dispatch(setOtpError(''))
        if (e.key === "Backspace") {
            const newOtp = [...otpArray];

            if (otpArray[index]) {
                // Clear current field
                newOtp[index] = "";
                dispatch(setOtp(newOtp.join("")));
            } else if (index > 0) {
                // Move focus to previous field and clear it
                newOtp[index - 1] = "";
                dispatch(setOtp(newOtp.join("")));
                inputRefs.current[index - 1]?.focus();
            }
        }
    };

    // Handle Resend OTP
    const handleResend = () => {
        dispatch(setOtp(null)); // Clear Redux OTP state
        dispatch(setPageError(null));
        dispatch(setOtpSent(true));
        dispatch(setTimer(30)); // Reset timer
        // Call your OTP generation API here
        resendOtp(email)
    };

    // Timer countdown logic
    useEffect(() => {
        if (timer <= 0) {
            dispatch(setOtpSent(false)); // Enable Resend button
            return;
        }

        const countdown = setInterval(() => {
            dispatch(setTimer(Math.max(timer - 1, 0)));
        }, 1000);

        return () => clearInterval(countdown);
    }, [timer, dispatch]);

    return (
        <div className="otp-container">
            <p className={styles.headerPara}>We have sent you an OTP on your work email</p>
            <div className={`${styles.otpInputs} ${otperror ? styles.invalid : ''}`}>
                {otpArray.map((digit, index) => (
                    <input
                        key={index}
                        type="text"
                        value={digit}
                        maxLength={1}
                        inputMode="numeric"
                        placeholder=" "
                        className={`${styles.otpInput} form-control`}
                        ref={(el) => {
                            if (el) (inputRefs.current[index] = el)
                        }}
                        onChange={(e) => handleChange(index, e)}
                        onKeyDown={(e) => handleKeyDown(index, e)}
                    />
                ))}
            </div>

            {/* Timer & Resend OTP */}
            <div className={styles.timer}>
                {timer > 0 ? (
                    <span className={styles.time}>Resend OTP in <span>00:{timer}</span></span>
                ) : (
                    <span
                        onClick={handleResend}
                        className={`link ${styles.link} ${otpSent ? "disabled" : ""}`}
                    >
                        Resend
                    </span>
                )}
                {otperror ? <div className={styles.otpError}>{otperror}</div> : null}
            </div>
        </div>
    );
};

export default OtpInputs;

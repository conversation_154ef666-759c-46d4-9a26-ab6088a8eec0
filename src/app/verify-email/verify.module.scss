@import '../scss/variable.scss';

.verifyEmailPage{
    header{
        margin-bottom: 25px;
    }
}
.pageContent{
    min-height: calc(100vh - 210px);
}
.pageForm{
    min-height: calc(100vh - 210px);
    display: flex;
    flex-direction: column;
}
.otpInputs {
    display: flex;
    justify-content: flex-start;
    gap: 8px;
    .otpInput {
        width: 44px;
        height: 52px;
        text-align: center;
    }
    &.invalid{
        .otpInput{
            border-color: $red;
        }
    }
}
.timer {
    font-size: 12px;
    font-weight: 500;
    text-align: left;
    margin-top: 18px;
    color: $color-black;
    display: flex;
    justify-content: space-between;
    max-width: 200px;
    .otpError{
        color: $red;
    }
    .time{
        span{
            color: $color1;
        }
    }
    .link {
        cursor: pointer;
    }
}
.headerPara{
    margin-top: -10px;
    margin-bottom: 10px;
    font-weight: 500;
    text-align: left;
}
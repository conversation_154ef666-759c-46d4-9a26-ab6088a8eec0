'use client';

import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import ShieldIcon from '../images/shield-icon.svg';
import Image from 'next/image';
import BottomPopup from '../components/popups/BottomPopup';
import { closeOTPModal, openOTPModal, setEmail, setOtp, setOtpError, setOtpSuccess, setPageError, setPageSuccess } from './verify.slice';
import { useDispatch, useSelector } from 'react-redux';
import store, { AppDispatch, RootState } from '../store/store';
import OtpInputs from './OtpInputs';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { ApiResponse } from '../store/interface/apiInterface';
import { apiRequest, fetchCommonApi } from '../utils/api';
import React, { useEffect, useState } from 'react';
import { ENDPOINTS } from '../utils/endpoints';
import styles from './verify.module.scss'
import PageWrapper from '../components/PageWrapper';
import { setCommonData } from "../store/slices/commonSlice";
import LoadingComp from '../component/loader';
import CtEvents from '../utils/Ctevents';
import { useRouter } from "next/navigation";


function VerifyEmail() {
    const router = useRouter()

    const dispatch = useDispatch<AppDispatch>();
    const { isOTPModalOpen, otp, otpsuccess, email, pageerror, pagesuccess } = useSelector((state: RootState) => state.otp);
    const [loaded, setLoaded] = useState<boolean>(false)
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);


    const validationSchema = Yup.object({
        emailId: Yup.string()
            .nullable()
            .matches(
                /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                "Enter a valid email ID"
            )
            .required("Email ID is required")
    });

    const formik = useFormik({
        initialValues: {
            emailId: ''
        },
        validationSchema,
        onSubmit: async (values) => {
            sendOtp(values.emailId.toLowerCase())
        },
    });
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "Work Email", "User flow": cteventsfromstorage.isNonSTP ? "Non STP" : "STP", "Product category": productcode, "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    const sendOtp = async (email: string): Promise<void> => {
        setLoaded(false)
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))
        try {
            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.sendemailotp, { email });
            if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success' || response.mainData.status?.toString().toLowerCase() === 'true')) {

                dispatch(setPageSuccess(response.mainData.data.message || 'OTP sent successfully'))
                dispatch(setEmail(email))
                dispatch(openOTPModal())
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
               else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                    dispatch(setPageError(response.mainData.error_message || 'Error in sending OTP'))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
        } catch (error) {

            dispatch(setPageError('Error in sending OTP'))
        } finally{
            setLoaded(true)
        }
    };

    const verifyOtp = async () => {
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))
        setLoaded(false)


        try {
            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.verifyemailotp, { email, otp });
            if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success' || response.mainData.status?.toString().toLowerCase() === 'true')) {
                const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                const event_name = 'Button Clicked'
                const event_property = { "Screen Name": "Work Email Verified", "Work email": email, "OTP verified": true, "CTA": "SEND OTP", "Source": cteventsfromstorage?.source }
                setCtdata({ event_name, event_property })
                dispatch(setPageSuccess(response.mainData.data.message || 'OTP verified successfully'))
                dispatch(closeOTPModal())
                dispatch(setOtp(''))
                dispatch(setEmail(''))
                formik.setFieldValue('emailId', '')
                formik.setFieldTouched('emailId', false)

                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                    dispatch(setPageError(response.mainData.error_message || 'Error in verification'))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
        } catch (error) {
            dispatch(setPageError('Error in sending OTP'))
        } finally{
            setLoaded(true)
        }
    };

    const handleVerifyOTP = async () => {
        
        if (!otp) {
            dispatch(setOtpError('Please provide OTP'))
        } else if (otp && otp.length !== 6) {
            dispatch(setOtpError('Incorrect OTP'))
        } else {
            //dispatch(setOtpSuccess('OTP Success'))
            await verifyOtp()
        }
    }
    const skipPage = async () => {
        setLoaded(false)

        const payload = {
            is_skipped: true
        }
        try {
            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.sendemailotp, payload);
            if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success' || response.mainData.status?.toString().toLowerCase() === 'true')) {
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                    dispatch(setPageError(response.mainData.error_message || 'Error in sending OTP'))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
        } catch (error) {
            dispatch(setPageError('Error in sending OTP'))
        } finally{
            setTimeout(() => {
                setLoaded(true)
            }, 2000)
        }
    }
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    return (
        <PageWrapper>
            {loaded ?
                <div className={`registration-page ${styles.verifyEmailPage}`}>
                    <PageHeader title="Verify your work email for better offers" para="We will send an OTP to verify your work email" />
                    <div className={`page-content ${styles.pageContent}`}>
                        <form onSubmit={formik.handleSubmit} className={styles.pageForm}>

                            {/* Email ID */}
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    autoComplete="off"
                                    name="emailId"
                                    placeholder=" "
                                    value={formik.values.emailId}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        const formattedValue = value.replace(/[^A-Za-z0-9@._-]/g, "");
                                        formik.setFieldValue("emailId", formattedValue);
                                    }}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>Work email</label>
                                {!formik.errors.emailId && formik.touched.emailId && (
                                    <span className="valid"></span>
                                )}
                                {formik.errors.emailId && formik.touched.emailId && (
                                    <div className="error">{formik.errors.emailId}</div>
                                )}
                            </div>

                            {/* Footer */}
                            <div className="bottom-footer p-0 mt-auto">
                                <p className="secure-tag">
                                    <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                </p>
                                <button type="submit" className={`btn btn-primary ${!formik.isValid || !formik.dirty ? 'disabled' : ''}`}>Send OTP</button>
                                <button type="button" className="btn btn-primary-outline mb-0" onClick={skipPage} style={{ color: "#000" }}>Skip</button>
                                <div className='powered-by mb-0'>
                                    <span>Powered by Akara Capital Advisors Private Limited</span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <BottomPopup
                        className='otp-modal'
                        isOpen={isOTPModalOpen}
                        onClose={() => {
                            dispatch(closeOTPModal())
                        }}
                        title="Verify OTP"
                        buttons={[
                            { label: 'Verify', onClick: handleVerifyOTP, className: `mb-0 ${otp?.length === 6 ? '' : 'disabled'}` }
                        ]}
                    >
                        <OtpInputs resendOtp={sendOtp} />
                    </BottomPopup>
                </div>
                :

                <LoadingComp />
            }
            <CtEvents data={ctData} />
            {pageerror || pagesuccess || otpsuccess ? <ToastMessage color={pagesuccess || otpsuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : otpsuccess ? otpsuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
}

export default VerifyEmail;

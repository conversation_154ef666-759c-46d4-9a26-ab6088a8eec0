import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface AddressState {
  pageerror: string | null;
  pagesuccess: string | null;
  pincodeValid: boolean;
  addressList: any;
  isChecked: boolean;
}

// Define the initial state
const initialState: AddressState = {
  pageerror: null,
  pagesuccess: null,
  pincodeValid: false,
  addressList: null,
  isChecked: false
};

// Create the slice
const comAddressSlice = createSlice({
  name: 'comaddress',
  initialState,
  reducers: {
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setPincodeValid: (state, action: PayloadAction<boolean>) => {
      state.pincodeValid = action.payload;
    },
    setAddressList: (state, action: PayloadAction<string>) => {
      state.addressList = action.payload;
    },
    setIsChecked: (state, action: PayloadAction<boolean>) => {
      state.isChecked = action.payload;
    },
  },
});

// Export actions
export const {
  setPageError,
  setPageSuccess,
  setPincodeValid,
  setAddressList,
  setIsChecked
} = comAddressSlice.actions;

// Export the reducer
export default comAddressSlice.reducer;

'use client'
import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import styles from './register.module.scss'
import PageHeader from '../components/PageHeader';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store/store';
import Image from 'next/image';
import HeaderIcon from '../images/register-icon.svg'
import ShieldIcon from '../images/shield-icon.svg'
import CrossIcon from '../images/cross-icon.svg'
import { differenceInYears, isValid, parse } from 'date-fns';
import { setIsChecked, setPageError, setPincodeValid } from './registercb.slice';
import { apiRequest } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';

function RegistrationForm() {
    const dispatch = useDispatch<AppDispatch>();
    const { isChecked, pincodeValid } = useSelector((state: RootState) => state.registercb);

    const validationSchema = Yup.object({
        fullName: Yup.string()
            .nullable()
            .test(
                "fullName-not-na",
                "Full Name is required",
                (value) => value === "N/A" || !!value
            )
            .test(
                "fullName-alphabetic",
                "Full Name must contain only alphabetic characters",
                (value) => {
                    if (!value || value === "N/A") return true;
                    return /^[A-Za-z\s]+$/.test(value);
                }
            ),

        panCardNumber: Yup.string()
            .nullable()
            .test(
                "is-not-blank",
                "PAN Card number cannot be blank",
                (value) => value === "N/A" || !!value
            )
            .test(
                "is-valid-length",
                "PAN Card number must be exactly 10 characters long",
                (value) => value === "N/A" || (value?.length === 10)
            )
            .test(
                "no-special-characters",
                "PAN Card number must not contain special characters",
                (value) => value === "N/A" || /^[A-Za-z0-9]*$/.test(value || "")
            )
            .test(
                "valid-pan-format",
                "Invalid PAN Card format",
                (value) =>
                    value === "N/A" ||
                    /^[A-Z]{3}P[A-Z][0-9]{4}[A-Z]{1}$/.test(value || "")
            ),
        dateOfBirth: Yup.string()
            .nullable()
            /*.test(
                "is-not-blank",
                "Date of Birth cannot be blank",
                (value) => value === "N/A" || !!value
            )*/
            .test(
                "valid-format",
                "Date of Birth must be in DD-MM-YYYY format",
                (value) => {
                    if (!value || value === "N/A") return true;
                    const regex = /^\d{2}-\d{2}-\d{4}$/;
                    return regex.test(value); // Validate format
                }
            )
            .test(
                "valid-date",
                "Invalid Date. Date of Birth must be in DD-MM-YYYY format",
                (value) => {
                    if (!value || value === "N/A") return true;
                    const parsedDate = parse(value, "dd-MM-yyyy", new Date());
                    return isValid(parsedDate); // Check if it's a real date
                }
            )
            .test(
                "valid-age",
                "You must be at least 18 years old",
                (value) => {
                    if (!value || value === "N/A") return true;
                    const parsedDate = parse(value, "dd-MM-yyyy", new Date());
                    if (!isValid(parsedDate)) return false;
                    return differenceInYears(new Date(), parsedDate) >= 18; // Ensure age >= 18
                }
            ),
        pinCode: Yup.string()
            .nullable()
            .test(
                "is-not-blank",
                "PIN Code cannot be blank",
                (value) => value === "N/A" || !!value
            )
            .test(
                "is-six-digits",
                "PIN Code must be exactly 6 digits long",
                (value) => !value || /^\d{3} \d{3}$/.test(value)
            )
            .test(
                "only-digits",
                "PIN Code must only contain numeric digits",
                (value) => !value || /^\d{3}( \d{3})*$/.test(value)
            )
            .test(
                "no-leading-zero",
                "PIN Code must not start with zero",
                (value) => !value || /^[1-9]/.test(value)
            )
    });

    const formik = useFormik({
        initialValues: {
            fullName: '',
            panCardNumber: '',
            dateOfBirth: '',
            pinCode: '',
        },
        validationSchema,
        onSubmit: async (values) => {
            if (!pincodeValid) {
                dispatch(setPageError('Please enter valid pincode number.'))
                return
            }
        },
    });

    const verifyPincode = async (pincode: string | undefined) => {
        dispatch(setPageError(''))

        try {
            if (pincode && pincode.length === 6) {
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.pincode, { pincode });
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success')) {
                    dispatch(setPincodeValid(true))
                } else {
                    
                    dispatch(setPincodeValid(false))
                    if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        }
    };

    return (
        <div className={`registration-page ${styles.registerCb}`}>
            <PageHeader title="Please provide details to get started" icon={<Image src={HeaderIcon} alt='Header' />} />
            <div className='page-content'>
                <form onSubmit={formik.handleSubmit}>

                    <div className="input-wrapper">
                        <input
                            type="text"
                            autoComplete='off'
                            name="panCardNumber"
                            minLength={10}
                            maxLength={10}
                            placeholder=" "
                            value={formik.values.panCardNumber.toUpperCase()}
                            onChange={(e) => {
                                const uppercaseValue = e.target.value.toUpperCase();
                                const formattedValue = uppercaseValue.replace(/[^A-Z0-9]/g, "").slice(0, 10);
                                formik.setFieldValue("panCardNumber", formattedValue);
                                if (formattedValue?.length === 10) {
                                    setTimeout(() => e.target.blur(), 0)
                                }
                            }}
                            onBlur={formik.handleBlur}
                            className={`form-control`}
                        />
                        <label>PAN</label>
                        {formik.errors.panCardNumber && formik.touched.panCardNumber && (
                            <div className="error">{formik.errors.panCardNumber}</div>
                        )}
                    </div>
                    <div className="input-wrapper">
                        <input
                            type="text"
                            autoComplete="off"
                            name="fullName"
                            placeholder=" "
                            value={formik.values.fullName}
                            onChange={(e) => {
                                const value = e.target.value;
                                const formattedValue = value.replace(/[^A-Za-z\s]/g, "");
                                formik.setFieldValue("fullName", formattedValue);
                            }}
                            onBlur={formik.handleBlur}
                            className={`form-control`}
                        />

                        <label>Name as per PAN</label>
                        {formik.errors.fullName && formik.touched.fullName && (
                            <div className="error">{formik.errors.fullName}</div>
                        )}
                    </div>
                    <div className={`input-wrapper date-picker ${formik.values.dateOfBirth ? 'active' : ''}`}>

                        <input
                            type="text"
                            autoComplete='off'
                            name="dateOfBirth"
                            minLength={10}
                            maxLength={10}
                            inputMode="numeric"
                            placeholder=" "
                            value={formik.values.dateOfBirth}
                            onChange={(e) => {
                                let value = e.target.value.replace(/[^0-9]/g, ""); // Remove non-numeric characters

                                // Format the value into DD-MM-YYYY
                                if (value.length > 2) value = `${value.slice(0, 2)}-${value.slice(2)}`;
                                if (value.length > 5) value = `${value.slice(0, 5)}-${value.slice(5, 9)}`;

                                // Ensure the value doesn't exceed 10 characters
                                value = value.slice(0, 10)
                                if (/^\d{2}-\d{2}-\d{4}$/.test(value) || value === "N/A") {
                                    formik.setFieldValue("dateOfBirth", value);
                                }
                                if (value?.length === 10) {
                                    setTimeout(() => e.target.blur(), 0)
                                }
                            }}
                            onBlur={formik.handleBlur}
                            className={`form-control`}
                            disabled={formik.values.dateOfBirth === 'N/A'}
                        />
                        <label>Date of Birth (DD-MM-YYYY)</label>
                        {formik.errors.dateOfBirth && formik.touched.dateOfBirth && (
                            <div className="error">{formik.errors.dateOfBirth}</div>
                        )}
                    </div>

                    <div className="input-wrapper">
                        <input
                            type="text"
                            autoComplete='off'
                            name="pinCode"
                            placeholder=" "
                            inputMode="numeric"
                            maxLength={7}
                            minLength={6}
                            value={formik.values.pinCode}
                            onChange={async (e) => {
                                const value = e.target.value ? e.target.value.replace(/[^0-9]/g, "") : '';
                                const formattedValue = value ? value
                                    .match(/.{1,3}/g)
                                    ?.join(" ")
                                    .slice(0, 7) : '';
                                formik.setFieldValue("pinCode", formattedValue);
                                await verifyPincode(formattedValue?.split(' ').join(''))
                                if (formattedValue?.length === 7) {
                                    setTimeout(() => e.target.blur(), 0)
                                }
                            }}
                            onBlur={formik.handleBlur}
                            className={`form-control`}
                        />
                        <label>PIN Code of current address</label>
                        {formik.errors.pinCode && formik.touched.pinCode && (
                            <div className="error">{formik.errors.pinCode}</div>
                        )}
                    </div>
                    <p className={`link ${styles.referalLink}`}>Do you have a referral code?</p>
                    {/*<div className='referal-tag'><span><strong>STASH5645 -</strong> Referral code applied</span> <Image src={CrossIcon} alt='Cross' /></div>*/}
                    <div className={`consent-check ${styles.consent}`}>
                        <label>
                            <div className={`checkbox ${isChecked ? 'checked' : ''}`}>
                                <input type="checkbox" checked={isChecked} onChange={() => {
                                    dispatch(setIsChecked(!isChecked))
                                }} />
                            </div>
                            <div>
                                <p style={{ marginBottom: '15px' }}><strong style={{ fontSize: '14px', color: 'var(--text-color)' }}>Receive offers and notifications on Whatsapp</strong><br />We'll only send welcome or regulatory messages. You may mute/unmute them later on your Stashfin App!</p>
                                <p>The receipt and provision of such information is based upon your earlier acceptance of our privacy policy.</p>
                            </div>
                        </label>
                    </div>
                    <div className='bottom-footer p-0'>
                        <p className='secure-tag'><Image src={ShieldIcon} alt='Shield' /> Your data is 100% safe & secure</p>
                        <button type="submit" className={`btn btn-primary ${!formik.isValid || !formik.dirty || !pincodeValid ? 'disabled' : ''}`}>Continue</button>
                        <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
                    </div>
                </form>
            </div>
        </div>
    );
}

export default RegistrationForm;

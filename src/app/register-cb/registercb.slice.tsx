import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface RegisterCBState {
    startCapture: boolean;
    capturedImage: string | null;
    missingDetails: string | null;
    pageerror: string | null;
    fileerror: string | null;
    fileData: string | null;
    selectedDocType: string | null;
    isForceModalOpen: boolean;
    uploadedImage: string;
    isChecked: boolean;
    defenceCheck: boolean;
    isUploadModalOpen: boolean;
    pincodeValid: boolean;
}

// Define the initial state
const initialState: RegisterCBState = {
    startCapture: false,
    capturedImage: null,
    missingDetails: null,
    pageerror: null,
    fileerror: null,
    fileData: null,
    selectedDocType: null,
    isForceModalOpen: false,
    uploadedImage: '',
    isChecked: false,
    defenceCheck: false,
    isUploadModalOpen: false,
    pincodeValid: false
};

// Create the slice
const registercbSlice = createSlice({
    name: 'registercb',
    initialState,
    reducers: {
        setStartCapture: (state, action: PayloadAction<boolean>) => {
            state.startCapture = action.payload;
        },
        setCapturedImage: (state, action: PayloadAction<string | null>) => {
            state.capturedImage = action.payload;
        },
        setPageError: (state, action: PayloadAction<string | null>) => {
            state.pageerror = action.payload;
        },
        setMissingDetails: (state, action: PayloadAction<string | null>) => {
            state.missingDetails = action.payload;
        },
        setSelectedDocType: (state, action: PayloadAction<string | null>) => {
            state.selectedDocType = action.payload;
        },
        openForceModal: (state) => {
            state.isForceModalOpen = true;
        },
        closeForceModal: (state) => {
            state.isForceModalOpen = false;
        },
        openUploadModal: (state) => {
            state.isUploadModalOpen = true;
        },
        closeUploadModal: (state) => {
            state.fileData = '';
            state.isUploadModalOpen = false;
        },
        setFileError: (state, action: PayloadAction<string | null>) => {
            state.fileerror = action.payload;
        },
        setFileData: (state, action: PayloadAction<string | null>) => {
            state.fileData = action.payload;
        },
        setUploadedFile: (state, action: PayloadAction<string>) => {
            state.uploadedImage = action.payload;
        },
        setIsChecked(state, action: PayloadAction<boolean>) {
            state.isChecked = action.payload;
        },
        setDefenceCheck(state, action: PayloadAction<boolean>) {
            state.defenceCheck = action.payload;
        },
        setPincodeValid(state, action: PayloadAction<boolean>) {
            state.pincodeValid = action.payload;
        },
    },
});

// Export actions
export const {
    setStartCapture,
    setCapturedImage,
    setPageError,
    setMissingDetails,
    setSelectedDocType,
    openForceModal,
    closeForceModal,
    openUploadModal,
    closeUploadModal,
    setFileError,
    setFileData,
    setUploadedFile,
    setIsChecked,
    setDefenceCheck,
    setPincodeValid
} = registercbSlice.actions;

// Export the reducer
export default registercbSlice.reducer;

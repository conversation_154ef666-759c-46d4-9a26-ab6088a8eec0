@import '../scss/variable.scss';

.externalWrapper{
    h1{
        font-size: 20px !important;
    }
    header{
        padding-bottom: 20px;
        margin-bottom: 0;
    }
}
.pageContent{
    min-height: calc(100vh - 180px);
}
.notEligibleContent{
    text-align: left;
    margin-top: 40px;
}
.creditbuilderbox {
    padding: 24px 10px 30px 16px;
    background: #F5F5F5;
    background-position: bottom 15px right 16px;
    border-radius: 12px;
    margin-top: 25px;

    >img {
        width: 205px;
        height: 80px;
    }

    .title {
        font-family: var(--font-mona-sans);
        font-weight: 600;
        line-height: 30px;
        font-size: 16px;
        color: #101010;
        padding-right: 50px;
    }

    >p {
        margin-top: 9px;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        padding-right: 50px;
        line-height: 14px;
        font-size: 12px;
        color: #101010;
        margin-top: 24px;
    }

    .steps {
        display: flex;
        flex-flow: column;
        margin-top: 10px;

        >div {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            &:last-child {
                margin-bottom: 0;
            }

            img {
                width: 32px;
                height: 32px;
                margin-right: 12px;
            }

            p {
                font-family: var(--font-mona-sans);
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
            }
        }
    }

    .circularbtn {
        margin-top: 24px;
        max-width: 121px;
    }
}
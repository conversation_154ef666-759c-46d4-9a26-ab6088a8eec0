'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import HeaderIcon from '../images/recheck.svg'
import inrNote from '../images/inrnote.svg'
import copyDocs from '../images/copy-documents.svg'
import fileIcon from '../images/file-icon.svg'
import styles from './noteligible.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import { apiRequest } from "../utils/api"
import { ENDPOINTS } from "../utils/endpoints"
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from "../store/store"
import { setPageError, setPageSuccess } from '../register/register.slice';
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { initiatePayment } from '../utils/bridge';
import CtEvents from '../utils/Ctevents';
function RecheckProfile() {
    const dispatch = useDispatch();
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [feeData, setfeeData] = useState<any>([])
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        landing_page: string,
        profile_pic_upload_url: string,
        cb_fee: any
    }
    const GetRejectedFee = async () => {
        try {
            const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.cb_rejected_fee);
            if (response.mainData && response.mainData.cb_fee !== '') {
                setfeeData(response.mainData.data)
            } else {

                dispatch(setPageError(response.mainData?.error_message as string | "null"))

            }
        } catch (error) {
        }
    }
    useEffect(() => {
        GetRejectedFee()
    }, [])
    const MakeCbPayment = () => {
        debugger;
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_rejection_payment_failed", "Product category": 'cb', "CTA": "Try Again", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
        initiatePayment({
            amount: feeData.cb_fee,
            paymentFlag: 33,
            redirection_url_after_poll: `https://${window.location.hostname}/loading`,
        })
    }
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_rejection_payment_failed", "Product category": 'cb', "amount": 385, "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    return (
        <div className={`external-wrapper ${styles.externalWrapper}`}>
            <PageHeader title='Oops, looks like we need to further check your profile' call={false} nobg={true} bgcolor="#FFE7E7" icon={<Image src={HeaderIcon} alt='Header' />} />
            <div className={`${styles.pageContent} page-content`}>
                <div className={styles.notEligibleContent}>
                    <div className={styles.creditbuilderbox}>
                        <div className={styles.title}>
                            Steps
                        </div>
                        <div className={styles.steps}>
                            <div>
                                <Image src={inrNote} alt="" />
                                <p>Pay ₹ {feeData.cb_fee}+ tax for manual check</p>
                            </div>
                            <div>
                                <Image src={copyDocs} alt="" />
                                <p>Our team will check your documents & get back within 72 hours</p>
                            </div>
                            <div>
                                <Image src={fileIcon} alt="" />
                                <p>After approval, you can get the credit builder loan</p>
                            </div>
                        </div>

                    </div>
                </div>
                <div className="bottom-footer p-0 mt-auto">
                    <button type="button" className="btn btn-primary" onClick={() => MakeCbPayment()}>Pay Now</button>
                    <p className='powered-by mb-0'>Powered by Akara Capital Advisors Private Limited</p>
                </div>
            </div>
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
            <CtEvents data={ctData} />

        </div>
    );
}

export default RecheckProfile;

.container {
    min-height: 100vh;
    background-color: #FFFFFF;
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    overflow: hidden;
}

.successIcon {
    margin-bottom: 16px;
    animation: scaleIn 0.5s ease-out;
}

.title {
    color: #1F1F1F;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 12px;
    text-align: center;
    animation: fadeInUp 0.5s ease-out 0.3s both;
}

.subtitle {
    color: #1F1F1F;
    font-weight: 500;
    font-size: 18px;
    margin-bottom: 8px;
    text-align: center;
    animation: fadeInUp 0.5s ease-out 0.4s both;
}

.amount {
    color: #1F1F1F;
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 8px;
    text-align: center;
    animation: fadeInUp 0.5s ease-out 0.5s both;
}

.ctaContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: auto;
    animation: fadeInUp 0.5s ease-out 0.6s both;
}

.applyButton {
    width: 100%;
    padding: 1rem 0;
    background-color: #1A1A1A;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: #000000;
    }
}

.creditRepairButton {
    width: 100%;
    padding: 0.75rem 0;
    background: none;
    border: none;
    color: #000000;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: opacity 0.2s;

    &:hover {
        opacity: 0.8;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0);
        opacity: 0;
    }

    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}
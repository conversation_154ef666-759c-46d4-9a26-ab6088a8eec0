'use client';

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation';

import { getCRConfirmationScreen, confirmCRConfirmationScreen, GetCRConfirmationScreenResponse, ConfirmCRConfirmationScreenPayload } from '@/app/utils/tapapi';

import { GetOffer } from '@/app/utils/fetchData';
import UserConsentComponent from './UserConsentComponent'

const CRConfirmationPage = () => {
    const [firebaseData, setFirebaseData] = useState<any>(null);
    const [crConfirmationScreenData, setCRConfirmationScreenData] = useState<GetCRConfirmationScreenResponse['data'] | null>(null);

    const router = useRouter();

    const redirectToLoading = () => {
        router.push('/loading');
    }

    const fetchLoanAmount = async () => {
        try {
            const result = await GetOffer();

            if (result) {
                setFirebaseData(result);
            } else {
                throw new Error('Failed to fetch loan amount from Firebase:' + result);
            }
        } catch (error) {
            console.error('Error fetching loan amount from Firebase:' + error as string);
        }
    };

    const fetchCRConfirmationScreen = async () => {
        try {
            const data = await getCRConfirmationScreen();

            if (data?.status) {
                setCRConfirmationScreenData(data?.data);
            } else {
                throw new Error("Failed to fetch CR Confirmation Screen:" + data?.status);
            }
        } catch (error) {
            console.error("Error fetching CR Confirmation Screen:", error);
        }
    }

    const handleConfirmCRConfirmationScreen = async () => {
        try {
            const payload: ConfirmCRConfirmationScreenPayload = {
                apply_now: false,
                proceed_to_cr: true
            }

            const data = await confirmCRConfirmationScreen(payload);

            if (data?.status) {
                redirectToLoading();
            } else {
                throw new Error("Failed to confirm CR Confirmation Screen:" + data?.status);
            }
        } catch (error) {
            console.error("Error confirming CR Confirmation Screen:", error);
        }
    }

    const handleApplyNowClick = async () => {
        redirectToLoading();
    }

    useEffect(() => {
        // fetch CR Confirmation Screen data
        fetchCRConfirmationScreen();

        // fetch LOAN amount from Firebase
        fetchLoanAmount();
    }, []);

    return (
        <UserConsentComponent
            showApplyNow={crConfirmationScreenData?.apply_now}
            showApplyCreditRepair={crConfirmationScreenData?.proceed_to_cr}
            onApplyNow={handleApplyNowClick}
            onApplyCreditRepair={handleConfirmCRConfirmationScreen}
            loanAmount={firebaseData?.OfferPrice ? Number(firebaseData.OfferPrice).toLocaleString("en-IN") : ""}
        />
    )
}

export default CRConfirmationPage
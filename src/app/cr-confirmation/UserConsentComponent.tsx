import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';

import styles from './UserConsentComponent.module.scss';

// Dynamically import Confetti with no SSR
const Confetti = dynamic(() => import('react-confetti'), {
    ssr: false
});

interface UserConsentComponentProps {
    showApplyNow?: boolean;
    showApplyCreditRepair?: boolean;
    onApplyNow?: () => void;
    onApplyCreditRepair?: () => void;
    loanAmount?: string;
}

const UserConsentComponentNew: React.FC<UserConsentComponentProps> = ({
    showApplyNow = true,
    showApplyCreditRepair = true,
    onApplyNow = () => { },
    onApplyCreditRepair = () => { },
    loanAmount = ""
}) => {
    const [windowSize, setWindowSize] = useState<{ width: number; height: number }>({
        width: 0,
        height: 0
    });

    useEffect(() => {
        // Set initial size
        setWindowSize({
            width: window.innerWidth,
            height: window.innerHeight
        });

        // Add resize handler
        const handleResize = () => {
            setWindowSize({
                width: window.innerWidth,
                height: window.innerHeight
            });
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <div className={styles.container}>
            {/* Confetti Animation - Only render on client side when we have window dimensions */}
            {windowSize.width > 0 && windowSize.height > 0 && (
                <Confetti
                    width={windowSize.width}
                    height={windowSize.height}
                    numberOfPieces={200}
                    recycle={false}
                    colors={['#FF4B4B', '#4CAF50', '#2196F3', '#FFC107', '#9C27B0']}
                />
            )}

            {/* Success Icon */}
            <div className={styles.successIcon}>
                <Image src="/tick-icon.png" alt="Success Icon" width={227} height={106} />
            </div>

            {/* Title */}
            <h2 className={styles.title}>
                Congratulations!
            </h2>
            <p className={styles.subtitle}>
                You can get a loan up to
            </p>
            {loanAmount && <div className={styles.amount}>
                ₹ {loanAmount}
            </div>}

            {/* CTAs */}
            <div className={styles.ctaContainer}>
                {showApplyNow && (
                    <button
                        onClick={onApplyNow}
                        className={styles.applyButton}
                    >
                        Apply Now
                    </button>
                )}
                {showApplyCreditRepair && (
                    <button
                        onClick={onApplyCreditRepair}
                        className={styles.creditRepairButton}
                    >
                        I want Credit Repair
                    </button>
                )}
            </div>
        </div>
    );
};

export default UserConsentComponentNew; 
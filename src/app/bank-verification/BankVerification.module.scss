.externalWrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.pageContent {
  min-height: calc(100vh - 400px);
}

.mainSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 24px;
}

.buttonWrapper {
  padding: 0 16px;
}

.card {
  position: relative;
  background: #fff;
  border: 1px solid #e9e9e9;
  border-radius: 12px;
  width: 100%;
  overflow: visible;
  padding: 60px 16px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: 154px;
}

.circleContainer {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
}

.circle {
  width: 48px;
  height: 48px;
  background-color: #fff;
  border: 1px solid #e9e9e9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.circle + .circle {
  margin-left: -12px;
}

.title {
  margin-top: 16px;
  font-size: 16px;
  font-weight: 700;
  line-height: 16px;
  text-align: center;
  color: var(--black);
  letter-spacing: 0;
}

.description {
  max-width: 265px;
  margin-top: 16px;
  line-height: 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
  text-align: center;
  margin-bottom: 36px;
  letter-spacing: 0;
}

.noteContainer {
  margin-top: -12px;
  width: 100%;
  background-color: #fef4dc;
  padding: 12px 16px;
  border-radius: 0 0 12px 12px;
  border-top: none;
  box-sizing: border-box;

  p {
    margin-top: 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 150%;
    letter-spacing: 0;
    span {
      font-size: 12px;
      color: #ff7948;
      font-weight: 600;
      line-height: 150%;
      letter-spacing: 0;
    }
  }
}

.bottomText {
  font-size: 16px;
  color: var(--text-color);
  text-align: center;
  font-weight: 600;
  margin-top: 16px;
  text-decoration: none;
  display: block;
  cursor: pointer;
}

"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

import styles from "./BankVerification.module.scss";
import shield from "../images/shield-icon.svg";
import { upiIcons, UpiIcon } from "../utils/upiIcons";
import { apiRequest } from "../utils/api";
import { ENDPOINTS } from "../utils/endpoints";
import { setPageError, setPageSuccess } from "../register/register.slice";
import ToastMessage from "../components/ToastMessage/ToastMessage";
import LoadingComp from "../component/loader";
import { RootState } from "../store/store";
import CtEvents from "../utils/Ctevents"; // Added import

const PageWrapper = dynamic(() => import("../components/PageWrapper"), {
  ssr: false,
});
const PageHeader = dynamic(() => import("../components/PageHeader"), {
  ssr: false,
});

interface SaveBasicDetailsResponse {
  success: boolean | string;
  error_message?: string;
  data: Record<string, any>;
  shortUrl: string;
  upiLink: string;
}

export default function BankVerificationPage() {
  const verificationAmount: number = 1;
  const router = useRouter();
  const dispatch = useDispatch();
  const { pageerror, pagesuccess } = useSelector(
    (state: RootState) => state.register
  );
  const [loaded, setLoaded] = useState<boolean>(false);
  // Added state for CT events
  const [ctData, setCtdata] = useState<{
    event_name: string;
    event_property: Record<string, any>;
  } | null>(null);

  const handleRPDClick = async () => {
    // --- CT Event for 'Open UPI App' Click ---
    const cteventsfromstorage = JSON.parse(
      localStorage.getItem("eventsdata") || "null"
    );
    const productcode = localStorage.getItem("product_code");
    const event_name = "Button Clicked";
    const event_property = {
      pagename: "Bank Verification",
      CTA: "Open UPI App",
      "Product category": productcode,
      Source: cteventsfromstorage?.source,
    };
    setCtdata({ event_name, event_property });
    // --- End of CT Event ---

    setLoaded(false);
    try {
      const payload = {};
      const response = await apiRequest<SaveBasicDetailsResponse>(
        "POST",
        ENDPOINTS.rpd,
        payload
      );

      if (response.mainData && response.mainData.upiLink) {
        window.location.assign(response.mainData.upiLink);
        router.push("/rpdpolling");
      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message));
        }
      }
    } catch (error) {
      dispatch(setPageError("Error initiating UPI verification"));
    } finally {
      setTimeout(() => {
        setLoaded(true);
      }, 2000);
    }
  };

  // --- Handler for 'Enter Bank Account Details' Click ---
  const handleEnterDetailsClick = () => {
    const cteventsfromstorage = JSON.parse(
      localStorage.getItem("eventsdata") || "null"
    );
    const productcode = localStorage.getItem("product_code");
    const event_name = "Button Clicked";
    const event_property = {
      pagename: "Bank Verification",
      CTA: "Enter Bank Account Details",
      "Product category": productcode,
      Source: cteventsfromstorage?.source,
    };
    setCtdata({ event_name, event_property });
  };

  useEffect(() => {
    // --- CT Event for 'Screen View' ---
    const cteventsfromstorage = JSON.parse(
      localStorage.getItem("eventsdata") || "null"
    );
    const productcode = localStorage.getItem("product_code");
    const event_name = "Screen View";
    const event_property = {
      "Screen Name": "Bank Verification",
      "Product category": productcode,
      Source: cteventsfromstorage?.source,
    };
    setCtdata({ event_name, event_property });
    // --- End of CT Event ---

    const handleStart = () => {
      document.body.classList.add("loading");
      setLoaded(false);
    };
    const handleComplete = () => {
      document.body.classList.remove("loading");
      setLoaded(true);
    };
    handleStart();
    const timer = setTimeout(handleComplete, 1000);
    return () => {
      clearTimeout(timer);
      handleComplete();
    };
  }, []);

  return (
    <PageWrapper>
      {loaded ? (
        <div className={styles.externalWrapper}>
          <PageHeader
            title="Verify salary bank account to receive your loan"
            para="Your loan goes into your verified salary account, and EMIs are auto-debited from this account."
            call={true}
            back={true}
          />

          <div className={`page-content ${styles.pageContent}`}>
            <div className={styles.mainSection}>
              <div className={styles.card}>
                <div className={styles.circleContainer}>
                  {upiIcons.map((icon: UpiIcon, index: number) => (
                    <div className={styles.circle} key={index}>
                      <Image src={icon.src} alt={icon.alt} loading="lazy" />
                    </div>
                  ))}
                </div>

                <h2 className={styles.title}>Quick UPI Verification</h2>
                <p className={styles.description}>
                  Select your bank account instantly via your preferred UPI app
                </p>
              </div>

              <div className={styles.noteContainer}>
                <p>
                  <span>Note:</span> ₹ {verificationAmount} will be debited for
                  verification and refunded automatically in 48 hrs
                </p>
              </div>
            </div>
          </div>

          <div className="bottom-footer mt-auto">
            <p className="secure-tag">
              <Image src={shield} alt="Shield icon" loading="lazy" /> Your data
              is 100% safe & secure
            </p>
            <div className={styles.buttonWrapper}>
              <button className="btn btn-primary" onClick={handleRPDClick}>
                Open UPI App
              </button>
            </div>
            {/* Added onClick handler to the Link */}
            <Link
              href="/aa-failed"
              className={styles.bottomText}
              onClick={handleEnterDetailsClick}
            >
              Enter Bank Account Details
            </Link>
          </div>

          {(pageerror || pagesuccess) && (
            <ToastMessage color={pagesuccess ? "green" : "red"}>
              {pagesuccess || pageerror}
            </ToastMessage>
          )}
        </div>
      ) : (
        <LoadingComp />
      )}
      {/* Added CtEvents component to render */}
      <CtEvents data={ctData} />
    </PageWrapper>
  );
}

@import './variable.scss';

.btn {
    margin-bottom: 10px;
    &.btn-primary {
        max-width: 100%;
        width: 100%;
        height: 52px;
        background-color: $color-black;
        border-radius: 8px;
        color: $color-white;
        font-size: 16px;
        font-weight: 600;
        border: none;

        &:hover,
        &:active,
        &:focus {
            background-color: $color-black !important;
        }
        &.disabled{
            background-color: rgba(31, 31, 31, 0.22);
            border-color: rgba(31, 31, 31, 0.22);
        }
    }
    &.btn-primary-outline {
        max-width: 100%;
        width: 100%;
        height: 52px;
        background-color: $color-white;
        border-radius: 8px;
        color: $color-black;
        font-size: 16px;
        font-weight: 600;
        border: 1px solid $color-black;

        &:hover,
        &:active,
        &:focus {
            border-color: $color-black !important;
            // color: $color-white;
        }
        &.disabled{
            background-color: rgba(31, 31, 31, 0.22);
            border-color: rgba(31, 31, 31, 0.2);
            color: $color-white;
        }
    }
    &.btn-rounded {
        border-radius: 30px;
    }
}
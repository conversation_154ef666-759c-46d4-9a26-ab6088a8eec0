@import './variable.scss';

.modal-overlay {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0.11, 0.09, 0.09, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    .modal-content {
        max-width: 100%;
        width: 323px;
        background: #ffffff;
        border: none;
        border-radius: 10px;
        position: relative;
        padding: 54px 21px 15px;
        text-align: center;
        max-height: calc(100vh - 40px);
        overflow: hidden;
        overflow-y: auto;
        width: 100%;
        max-width: 420px;

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: $light-gray;
            border-radius: 10px;
        }

        &::-webkit-scrollbar-thumb {
            background: $color-gray;
            border-radius: 10px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: $color-gray;
        }
    }

    .title-icon {
        margin-right: 10px;
    }

    .modal-icon {
        margin-bottom: 25px;
    }

    .modal-close {
        position: absolute;
        right: 16px;
        top: 16px;
        z-index: 2;
        cursor: pointer;
    }

    p {
        font-size: 14px;
        line-height: 22px;
        color: $color-black;
        font-weight: 400;
        word-break: break-word;
    }

    &.bottom-modal {
        align-items: flex-end;

        .modal-content {
            border-radius: 20px;
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
            padding: 0;
            width: 100%;
            max-width: 420px;

            .modal-header {
                padding: 27px 15px 5px;
                font-size: 20px;
                font-weight: 600;
                line-height: 25px;
                text-align: left;
                max-width: calc(100% - 50px);
            }

            .modal-body {
                padding: 15px;
            }

            .modal-footer {
                padding: 10px 15px;
            }
        }

        &.close-app-modal {
            .modal-body {
                padding: 34px 19px 24px;

                .close-para {
                    font-weight: 400;
                    line-height: 20px !important;
                    width: 300px;
                    max-width: 100%;
                    margin: 0 auto;
                    margin-top: 25px;
                }
            }

            .modal-footer {
                display: flex;
                justify-content: space-between;

                .btn {
                    max-width: calc(50% - 5px) !important;
                    margin-bottom: 0;
                }
            }
        }
    }

    .modal-footer {
        .buttons {
            max-width: 100%;
            width: 100%;
            display: flex;
            justify-content: space-between;

            &.multi-btns {
                .btn {
                    max-width: 100%;
                    width: 125px;
                }
            }
        }
    }
}

.otp-modal {
    .modal-body {
        border-bottom: 1px solid $color-light;
    }
}
@import './variable.scss';

.input-wrapper {
  margin-bottom: 22px;
  position: relative;
  max-width: 100%;
  width: 390px;

  &.referal-input {
    margin-bottom: 45px;
  }

  label {
    &:not(.noabs) {
      position: absolute;
      top: 26px;
      left: 6px;
      color: $color-gray;
      pointer-events: none;
      transform: translateY(-50%);
    }

    font-size: 14px;
    font-weight: 500;
    background-color: $color-white;
    color: $color-black;
    padding: 0 5px;
    transition: 0.3s ease;
  }

  .icon {
    display: flex;
  }

  .right-icon {
    position: absolute;
    right: 18px;
    top: 24px;
    z-index: 2;
    transform: translateY(-50%);
    background-color: $color-white;
  }

  &.iconic-picker {
    label {
      left: 43px;
    }
  }
}

.date-picker {
  &.active {
    label {
      top: 0;
      left: 10px;
      color: $color-black;
      font-size: 14px;
      font-weight: 500;
    }
  }

  &.has-icon {
    input.form-control {
      padding-left: 16px;
    }

    .icon {
      position: absolute;
      right: 16px;
      z-index: 2;
      top: 16px;
      pointer-events: none;
    }

    label {
      left: 10px;
    }

    &.active {
      label {
        left: 10px;
      }
    }
  }

  input.form-control {

    &:focus,
    &:not(:placeholder-shown) {
      border: 1px solid #909090;
    }
  }

  &.active {
    input.form-control {
      border-color: #909090;
    }
  }
}

input.form-control {
  border-radius: 8px;
  border: 1px solid $color-gray;
  width: 390px;
  max-width: 100%;
  height: 52px;
  color: $color-black;
  font-size: 14px;
  font-weight: 400;
  padding: 15px 10px;

  &:disabled {
    border: 1px solid $color-gray !important;
    background-color: #f9f9f9;
  }

  &.has-icon {
    padding-left: 43px;

    +label {
      left: 16px;
    }

    &:focus,
    &:not(:placeholder-shown) {

      +label {
        left: 10px;
      }
    }
  }

  &:focus,
  &:not(:placeholder-shown) {
    outline: none;
    box-shadow: none;
    border-color: #909090;

    +label {
      top: 0;
      color: $color-gray;
      font-weight: 500;
    }
  }

  &::placeholder {
    color: $color-gray;
  }
}

.business-doc-input {
  input.form-control {
    +label {
      display: none;
    }

    &:not(:placeholder-shown) {

      +label {
        display: block;
      }
    }
  }
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.otp-container {

  .otp {
    position: relative;

    .completed {
      position: absolute;
      right: 15px;
      top: 30px;
      display: block;
      width: 18px;
      height: 18px;
      transform: translateY(-50%);

      svg {
        vertical-align: top;
      }
    }

    .otp-input {
      width: 100%;
      letter-spacing: 25px;
      padding: 10px 18px;

      &:focus,
      &:not(:placeholder-shown) {
        outline: none;
        box-shadow: none;
        border-color: #909090;

        +label {
          top: 0;
          color: $color-black;
          font-size: 10px;
          font-weight: 500;
        }
      }
    }
  }

  label {
    position: absolute;
    top: 30px;
    left: 18px;
    font-size: 14px;
    font-weight: 500;
    transform: translateY(-50%);
    background-color: $color-white;
    color: $color-gray;
    pointer-events: none;
    transition: 0.3s ease;
  }

  .timer {
    font-size: 11px;
    font-weight: 500;
    line-height: 15px;
    color: $color-gray;
    margin-top: 10px;

    /*.time {
      color: $color1;
      font-size: 11px;
    }*/

    .link {
      cursor: pointer;
      color: $color1;
      font-weight: 600;
    }
  }
}

.checkbox {
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  input[type="checkbox"] {
    display: none;
  }

  &:before {
    content: "";
    width: 18px;
    height: 18px;
    display: inline-block;
    background-repeat: no-repeat;
    margin-right: 8px;
    background-size: 100%;
    border: 1px solid $color-black;
    border-radius: 3px;
  }

  &.checked {
    &:before {
      background-image: url('../images/checkbox.svg');
      background-size: 18px;
      background-position: -1px;
    }
  }
}

.custom-select {
  position: relative;

  .select-header {
    border-radius: 8px;
    border-color: $color-gray;
    color: $color-black;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    padding: 18px;
    position: relative;
    height: 52px;
    line-height: 1;

    &:after {
      content: "";
      width: 20px;
      height: 20px;
      position: absolute;
      right: 12px;
      top: 14px;
      background-repeat: no-repeat;
      background-image: url('../images/chev-down.svg');
    }

    label {
      margin-bottom: 0;
    }
  }

  &.selected {
    .select-header {
      border-color: #909090;
    }

    .form-label {
      top: 0;
    }
  }
}

.select-options {
  list-style: none;
  margin: 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 5px 15px;
  position: absolute;
  left: 0;
  width: 100%;
  max-height: 226px;
  background: $color-white;
  z-index: 10;
  overflow-y: overlay;

  li {
    color: $color-black;
    font-size: 14px;
    cursor: pointer;
    align-items: center;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    padding: 10px 0px;

    &:not(:last-of-type) {
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    input[type="radio"] {
      &:before {
        background-color: transparent;
      }

      &:checked {
        border-color: $color-black;

        &::before {
          background-color: $color-black;
        }
      }
    }
  }

  &::-webkit-scrollbar {
    width: 7px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

input[type="radio"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid $color-gray;
  border-radius: 50%;
  position: relative;
  cursor: pointer;

  &:before {
    content: '';
    width: 7.2px;
    height: 7.2px;
    background-color: transparent;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &:checked {
    border-color: $color-black;
    background-color: transparent;

    &::before {
      background-color: $color-black;
    }
  }
}

.react-datepicker-wrapper {
  max-width: 100%;
}

.radio-wrapper {
  margin-bottom: 22px;

  .radio-label {
    position: static !important;
    transform: none !important;
    padding: 0;
    color: $color-black !important;
    margin-bottom: 17px;
  }

  .radios {
    color: $color-black;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    line-height: 1;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: flex-start;

    .radio {
      display: flex;
      align-items: center;
      flex-shrink: 1;
      min-width: 34%;
    }

    input {
      margin-right: 7px;
    }
  }
}

.otp-email {
  position: relative;

  .form-control {
    padding-right: 100px;

    &.otp-input {
      letter-spacing: 25px;
    }
  }

  .btn.btn-primary {
    position: absolute;
    right: 8.5px;
    top: 8.5px;
    width: 82px;
    border-radius: 7px;
    height: 43px;
    padding: 8px;
  }
}

.upload-button {
  border: 1px solid $color-black;
  border-radius: 24px;
  height: 40px;
  width: 114px;
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px 10px 20px;
  cursor: pointer;
  margin-top: 12px;

  .text {
    font-size: 14px;
    font-weight: 500;
    color: $color-black;
    line-height: 1.2;
  }

  .icon {
    margin-top: -2px;
  }

  &.uploaded {
    width: fit-content;

    .icon {
      margin-left: 10px;
    }

    +.reupload {
      color: $color1;
      font-weight: 600;
      font-size: 14px;
      width: fit-content;
      margin-top: 10px;
      cursor: pointer;
    }
  }
}

.validation-input {
  position: relative;

  .completed {
    position: absolute;
    right: 16px;
    top: 16px;
  }
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 19px;

  input {
    opacity: 0;
    width: 0;
    height: 0;

    &:checked+.toggle-slider {
      background-color: $color-black;
    }

    &:checked+.toggle-slider:before {
      transform: translateX(17px);
      background-size: 17px;
      background-position: top left;
      background-image: url('../images/toggle-slider-check.svg');
    }
  }
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #909090;
  transition: 0.4s;
  border-radius: 34px;

  &:before {
    position: absolute;
    content: "";
    height: 17px;
    width: 17px;
    left: 1px;
    bottom: 1px;
    background-color: $color-white;
    transition: 0.4s;
    border-radius: 50%;
  }
}

.switch-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;

  p {
    font-size: 12px;
    font-weight: 500;
    color: $color-gray;
    margin-right: 10px;
  }
}
.header {
    background: transparent;
    padding-left: 16px;
    padding-right: 16px;

    header {
        padding: 0 !important;
        margin-bottom: 15px;
    }

    .headercont {
        display: flex;
        padding-bottom: 30px;
        justify-content: space-between;

        >div {
            &.select {
                padding-top: 0;

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--black);
                    padding-right: 30px;

                    &+p {
                        margin-top: 7px;
                        width: 100%;
                        max-width: 216px;
                        br {
                            display: none;
                        }
                    }

                    &.congotxt {
                        font-family: var(--font-mona-sans);
                        font-weight: 700;
                        font-size: 18px;
                        line-height: 28px;
                        color: var(--black);
                        width: 100%;
                        max-width: 216px;
                        br {
                            display: none;
                        }
                    }
                }


            }

            &.icon {
                max-width: 113px;
                padding-top: 10px;

                img {
                    width: 100%;
                    height: auto
                }

                ;
            }
        }
    }

    header {
        min-height: auto;
        padding: 0;
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 10px;
        background-size: 100% calc(100% - 5px);
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;
    min-height: calc(100vh - 182px);
    display: flex;
    flex-flow: column;

    .choosebox {

        p {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: var(--black);
        }

        >div {
            display: flex;
            margin-top: 15px;

            >div {
                width: 106px;
                height: 42px;
                border: 1px solid #DFDFDF;
                border-radius: 74px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 16px;
                margin-right: 10px;
                cursor: pointer;

                &.recommended {
                    position: relative;

                    &:before {
                        content: "Recommended";
                        display: flex;
                        width: 81px;
                        height: 16px;
                        background: #FFC121;
                        border-radius: 14px;
                        align-items: center;
                        justify-content: center;
                        font-family: var(--font-mona-sans);
                        font-weight: 400;
                        font-size: 10px;
                        line-height: 12px;
                        position: absolute;
                        top: -8px;
                        margin: 0 auto;
                    }
                }
            }
        }
    }

    .loanamount {
        font-weight: 600;
        font-size: 16px;
    }

    .whatucantitle {
        font-family: var(--font-mona-sans);
        padding: 25px 0 16px 0;
        font-weight: 700;
        line-height: 24px;
        font-size: 16px;
        color: #000;
    }

    .creditbuilderbox {
        padding: 20px 15px;
        background: #F5F5F5;
        background-position: bottom 15px right 16px;
        border-radius: 12px;
        margin-top: 25px;

        >img {
            width: 205px;
            height: 80px;
        }

        .title {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            line-height: 30px;
            font-size: 16px;
            color: #101010;
            padding-right: 50px;
            line-height: 1;
        }

        >p {
            margin-top: 9px;
            font-family: var(--font-mona-sans);
            font-weight: 600;
            padding-right: 50px;
            line-height: 14px;
            font-size: 12px;
            color: #101010;
            margin-top: 24px;
        }

        .steps {
            display: flex;
            flex-flow: column;
            margin-top: 15px;

            >div {
                display: flex;
                align-items: center;
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }

                img {
                    width: 32px;
                    height: 32px;
                    margin-right: 12px;
                }

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--black);
                }
            }
        }

        .circularbtn {
            margin-top: 24px;
            max-width: 121px;
        }
    }

    .higher {
        margin-top: 15px;
        margin-bottom: 15px;
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 12px;
        line-height: 18px;
        color: #4A9E42;
        text-align: center;
    }

    .footerbottombtn {
        margin-bottom: 12px;
        margin-top: 20px;
        bottom: 0;
    }
}

.tabs {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 10px;
}

.tab {
    padding: 10px 15px;
    background: #f3f3f3;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
}

.tab:hover {
    background: #ddd;
    border-color: var(--black) !important;
}

.recommended {
    border: 2px solid #ff9800;
    /* Highlight recommended plan */
    position: relative;
}

.recommended span {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #ff9800;
    color: white;
    padding: 5px;
    font-size: 10px;
    border-radius: 5px;
}

.active {
    background: #ddd;
    color: var(--black);
    border-color: var(--black) !important;
}

.container {
    position: relative;
}

.gradientCircle {
    position: absolute;
    top: 0;
    left: 0;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(144deg, rgba(255, 203, 203, 1) 30%, rgba(255, 255, 255, 1) 0%);
    filter: blur(69.4px); // Figma blur value
    z-index: -1;
    pointer-events: none;
}

.planSelection {
    .planHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        span {
            &:first-child {
                color: var(--black);
                font-family: var(--font-mona-sans);
                font-weight: 700;
                font-size: 20px;
            }

            &:last-child {
                color: #1D23AE;
                font-family: var(--font-mona-sans);
                font-weight: 600;
                cursor: pointer;
                font-size: 12px;
            }
        }
    }
}

.planCard {
    border: 2px solid #E9E9E9;
    border-radius: 14px;
    margin-bottom: 20px;

    .planheader {
        display: flex;
        align-items: center;
        background: #ECE9E4;
        height: 44px;
        padding: 0 15px;
        border-radius: 12px 12px 0 0;

        .planname {
            color: var(--black);
            font-family: var(--font-mona-sans);
            font-weight: 700;
            font-size: 16px;
        }

        img {
            width: 20px;
            height: 20px;
            margin-right: 12px;
        }

        span {
            margin-left: auto;

            &.fee {
                color: var(--black);
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 16px;

                span {}
            }
        }

        .recommended {
            background: #000000;
            padding: 4px 7px;
            border-radius: 4px;
            color: #FFDA50;
            font-family: var(--font-mona-sans);
            font-weight: 700;
            font-size: 10px;
            border: none;
            text-transform: uppercase;
            margin-left: 10px;
        }

        .popular {
            color: #000000;
            padding: 4px 7px;
            border-radius: 4px;
            background: #FFDA50;
            font-family: var(--font-mona-sans);
            font-weight: 700;
            font-size: 10px;
            border: none;
            text-transform: uppercase;
            margin-left: 10px;
        }

        .base {
            color: #fff;
            padding: 4px 7px;
            border-radius: 4px;
            background: #000;
            font-family: var(--font-mona-sans);
            font-weight: 700;
            font-size: 10px;
            border: none;
            text-transform: uppercase;
            margin-left: 10px;
        }
        .selectedplan {
            display: none;
        }
        .unselected {
            display: flex;
        }
    }

    .planinner {
        padding: 18px 15px;

        .refundableparent {
            display: flex;

            .amount {
                font-family: var(--font-mona-sans);
                font-weight: 700;
                font-size: 28px;
                color: var(--black);
            }

            .label {
                font-family: var(--font-mona-sans);
                font-weight: 500;
                font-size: 16px;
                color: var(--black);
            }

            .improvement {
                display: flex;
                flex-flow: column;
                margin-left: auto;
                font-family: var(--font-mona-sans);
                font-weight: 500;
                font-size: 12px;
                color: var(--black);
                margin-top: auto;

                span {
                    font-weight: 700;
                }
            }
        }

        .emiCount {
            margin-top: 24px;
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 12px;
            color: var(--black);

            span {
                font-weight: 700;

            }
        }
    }
}

.active {
    background: transparent;
    border: 2px solid #1F1F1F;

    .planheader {
        background: #1F1F1F;

        .planname {
            color: #fff;
        }

        span {
            color: #fff;
        }

        .recommended,
        .base,
        .popular {
            color: #000;
            background: #FFDA50;
        }
        .selectedplan {
            display: flex;
        }
        .unselected {
            display: none;
        }
    }
}

.planfooteter {
    height: 44px;
    padding: 0 15px;
    border-radius: 0 0 12px 12px;
    background: #FFF8E0;
    display: flex;
    align-items: center;
    font-family: var(--font-mona-sans);
    font-weight: 500;
    font-size: 12px;
    color: var(--black);
    img {
        margin-right: 10px;
    }
}
.howitworksec {
    padding: 24px 16px;
    background: #fff;
    border:  1px solid #E9E9E9;
}
.arrow1 {
    width: calc(100% - 40px);
    margin-top: 13px;
    margin-bottom: 13px;
    img {
        width: 100%;
        height: auto;
    }
}
.arrow2 {
    width: calc(100% - 155px);
    margin-top: 0;
    margin-bottom: 13px;
    margin-left: 54px;

    img {
        width: 100%;
        height: auto;
    }
}
.arrow3 {
    width: calc(100% - 155px);
    margin-top: 0;
    margin-left: 54px;
    position: relative;
    top: -50px;

    img {
        width: 100%;
        height: auto;
    }
}
.arrow4 {
    
    width: calc(100% - 159px);
    margin-top: 0;
    position: absolute;
    right: 45px;
    top: 40px;

    img {
        width: 100%;
        height: auto;
    }
}
@media screen and (min-width:0) and (max-width:360px) {
    .planname {
        font-size: 14px !important;
    }
    .recommended, .popular, .base {
        font-size: 8px !important;
    }
    .fee * {
        font-size: 14px !important;
    }
    .arrow4 {
        width: calc(100% - 147px) !important;
        top: 59px !important;
    }
}
"use client"

import Image from "next/image";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import PageHeader from "../components/PageHeader";
import ToastMessage from "../components/ToastMessage/ToastMessage";
import { useThemeColor } from "../hooks/useThemeColor";
import { apiRequest, fetchCommonApi } from "../utils/api";
import { ENDPOINTS } from "../utils/endpoints";
import { setCommonData } from "../store/slices/commonSlice";
import { setPageError } from "../register/register.slice";
import CtEvents from "../utils/Ctevents";
import store, { RootState } from "../store/store";

import styles from "./cbbuy.module.scss";
import cashblack from "../images/cashback.svg";
import flash from "../images/flash.svg";
import lowcibil from "../images/lowcibilcb.svg";
import calenderoutline from "../images/calendaroutline.svg";
import document from "../images/document.svg";
import unselectedplan from "../images/unselectedplan.svg";
import selectedplan from "../images/selectedplan.svg";
import users from "../images/users.svg";
import crown from "../images/crown.svg";
import mobilebanner1 from "../images/mobile-banner-1.png";
import mobilebanner2 from "../images/mobile-banner-2.png";
import mobilebanner3 from "../images/mobile-banner-3.png";
import home from "../images/home.png";
import dot1 from "../images//dot-1.png";
import dot2 from "../images//dot-2.png";
import dot3 from "../images//dot-3.png";
import dot4 from "../images//dot-4.png";
import PageWrapper from "../components/PageWrapper";

interface SaveBasicDetailsResponse {
  success: boolean | string;
  error_message?: string;
  data: Record<string, any>;
  landing_page: string;
  status: any;
  plan_id: string;
  amount: string;
  is_recommended: string;
  recommended_text: string;
  emi: string;
  fee: string;
  what_to_do_points: string[];
}

function CbLandingPage() {
  const dispatch = useDispatch();
  const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
  const [plans, setPlans] = useState<SaveBasicDetailsResponse[]>([]);
  const [activePlan, setActivePlan] = useState<SaveBasicDetailsResponse | null>(null);
  const [showAll, setShowAll] = useState(false);
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<number>(4000);
  const refundPlanData: Record<number, { A: number; B: number; C: number; D: number; E: number; F: number; G: number }> = {
    4000: { A: 2000, B: 400, C: 72, D: 2472, E: 2000, F: 2000, G: 2000 },
    5600: { A: 2800, B: 560, C: 101, D: 3461, E: 2800, F: 2800, G: 2800 },
    12500: { A: 6250, B: 1250, C: 225, D: 7725, E: 6250, F: 6250, G: 6250 }
  };

  const plan = refundPlanData[selectedPlan];

  useThemeColor("#FFE7E7");

  useEffect(() => {
    GetCbBuyPlan();
    const ctevents = JSON.parse(localStorage.getItem("eventsdata") || "null");
    setCtdata({
      event_name: "Screen View",
      event_property: {
        "Screen Name": "cb_plan_screen",
        "Product category": "cb",
        Source: ctevents?.source,
      },
    });
  }, []);

  // Whenever activePlan changes, update selectedPlan state
  useEffect(() => {
    if (activePlan) {
      const amount = parseInt(activePlan.amount, 10);
      if ([4000, 5600, 12500].includes(amount)) {
        setSelectedPlan(amount);
      }
    }
  }, [activePlan]);

  // const plan = refundPlanData[selectedPlan];


  const GetCbBuyPlan = async () => {
    try {
      const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.get_cb_plan);
      if (response.mainData?.success?.toString().toLowerCase() === "true") {
        const allPlans = response.mainData.data.plans.sort((a: { amount: string | number; }, b: { amount: string | number; }) => +b.amount - +a.amount);
        const popularPlan = allPlans[0]; // 2nd plan after sorting = "Popular"
        setPlans(allPlans);
        setActivePlan(popularPlan);
      } else {
        dispatch(setPageError(response.mainData?.error_message || "Something went wrong"));
      }
    } catch (error) {
      dispatch(setPageError("Failed to fetch plans"));
    }
  };

  const SaveCbPlan = async () => {
    if (!activePlan) return;
    const payload = { plan_id: +activePlan.plan_id, amount: +activePlan.amount };
    localStorage.setItem("cb_plan_id", activePlan.plan_id);
    localStorage.setItem("buyamount", activePlan.amount);

    try {
      const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.save_cb_plan, payload);
      if (response.mainData?.success?.toString().toLowerCase() === "true") {
        const ctevents = JSON.parse(localStorage.getItem("eventsdata") || "null");
        setCtdata({
          event_name: "Button Clicked",
          event_property: {
            "Screen Name": "cb_plan_screen",
            "Product category": "cb",
            CTA: "Buy Credit Builder",
            plan: +activePlan.amount,
            Source: ctevents?.source,
          },
        });
        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
      } else {
        dispatch(setPageError(response.mainData?.error_message || "Could not save plan"));
      }
    } catch {
      dispatch(setPageError("Failed to save selected plan"));
    }
  };

  const icons = [cashblack, flash, calenderoutline, document];

  const displayedPlans = showAll ? plans : plans.slice(0, 2);
  const getFormattedDate = (): string => {
    const today = new Date();
    const day = today.getDate();

    const getMonthName = (date: Date) =>
      date.toLocaleString("default", { month: "long" });

    const getOrdinal = (n: number): string => {
      const s = ["th", "st", "nd", "rd"];
      const v = n % 100;
      return n + (s[(v - 20) % 10] || s[v] || s[0]);
    };

    let targetDate: Date;

    if (day > 28) {
      const month = today.getMonth();
      const year = today.getFullYear();
      targetDate = new Date(year, month + 2, 1); // 1st of month after next
    } else {
      targetDate = new Date(today);
      targetDate.setDate(today.getDate() + 30);
    }

    const formattedDay = getOrdinal(targetDate.getDate());
    const formattedMonth = getMonthName(targetDate);
    const formattedYear = targetDate.getFullYear();

    return `${formattedDay} ${formattedMonth} ${formattedYear}`;
  };

  const getTodayFormatted = (): string => {
    const today = new Date();

    return today.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  };


  return (
    <PageWrapper>

      <div className={styles.container}>
        <div className={styles.gradientCircle}></div>
        <div className={styles.header}>
          <PageHeader title="" />
          <div className={styles.headercont}>
            <div className={styles.select}>
              <p className={styles.congotxt}>
                Start improving your<br /> CIBIL score today!
                {/* ₹{Math.round(Number(activePlan?.fee) || 0)} */}
              </p>
              <p style={{ color: '#7B7B7B' }}>We offer a 2 EMI credit score<br /> repair plan</p>
            </div>
            <div className={styles.icon}>
              <Image src={lowcibil} alt="" />
            </div>
          </div>
        </div>
        {/* <PageHeader title="" /> */}
        <div className={styles.body}>

          <div className={styles.planSelection}>
            <div className={styles.planHeader}>
              <span>Select a plan</span>
              <span className={styles.allPlans} onClick={() => setShowAll(!showAll)}>
                {showAll ? "Hide Plans" : "All Plans"}
              </span>
            </div>

            {[...displayedPlans]
              .sort((a, b) => +b.amount - +a.amount) // sort descending by amount
              .map((plan, index, sortedPlans) => {
                const amount = +plan.amount;

                // Determine label by position in sorted list
                let planLabel = '';
                if (index === 0) planLabel = 'Recommended';
                else if (index === 1) planLabel = 'Popular';
                else if (index === sortedPlans.length - 1) planLabel = 'Base Plan';

                // Determine improvement text
                let improvementText = '';
                if (amount >= 12500) improvementText = 'up to 60 pts';
                else if (amount >= 5600) improvementText = 'up to 40 pts';
                else improvementText = 'up to 20 pts';

                return (
                  <div
                    key={plan.plan_id}
                    className={`${styles.planCard} ${activePlan?.plan_id === plan.plan_id ? styles.active : ""}`}
                    onClick={() => setActivePlan(plan)}
                  >
                    <div className={styles.planBody}>
                      <div>
                        <div className={styles.planheader}>
                          <Image src={unselectedplan} alt="" className={styles.unselected} />
                          <Image src={selectedplan} alt="" className={styles.selectedplan} />
                          <div className={styles.planname}>Plan {plan.plan_id}</div>
                          {planLabel && <div className={styles[planLabel.split(' ')[0].toLowerCase()]}>{planLabel}</div>}
                          <span className={styles.fee}>
                            {plan?.amount == '4000' &&
                              // <span>Fee <span> ₹{Math.round(Number(plan?.fee) || 0).toLocaleString("en-IN")}</span></span>
                              <span>Fee <span> ₹{Math.round(Number(400) || 0).toLocaleString("en-IN")}</span></span>
                            }
                            {plan?.amount == '5600' &&
                              // <span>Fee <span> ₹{Math.round(Number(plan?.fee) || 0).toLocaleString("en-IN")}</span></span>
                              <span>Fee <span> ₹{Math.round(Number(560) || 0).toLocaleString("en-IN")}</span></span>
                            }
                            {plan?.amount == '12500' &&
                              // <span>Fee <span> ₹{Math.round(Number(plan?.fee) || 0).toLocaleString("en-IN")}</span></span>
                              <span>Fee <span> ₹{Math.round(Number(1250) || 0).toLocaleString("en-IN")}</span></span>
                            }
                          </span>
                        </div>
                        <div className={styles.planinner}>
                          <div className={styles.refundableparent}>
                            <div>
                              <div className={styles.amount}>₹{Number(plan.emi).toLocaleString("en-IN")}</div>
                              <div className={styles.label}>as refundable EMI</div>
                            </div>
                            <div className={styles.improvement}>Improves score <span>{improvementText}</span></div>
                          </div>
                          <div className={styles.emiCount}>No of refundable EMIs: <span>2</span></div>

                        </div>
                        {planLabel == 'Recommended' &&
                          <>
                            <div className={styles.planfooteter}>
                              <Image src={crown} alt="" />
                              Higher EMIs can boost your CIBIL score faster

                            </div>
                          </>
                        }
                        {planLabel == 'Popular' &&
                          <>
                            <div className={styles.planfooteter}>
                              <Image src={users} alt="" />
                              4 Lakh user choose this plan
                            </div>
                          </>
                        }
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>

          {/* {activePlan && (
            <div className={styles.timelineSection}>
              <h3>How it works?</h3>
              <div className={styles.steps}>
                {activePlan.what_to_do_points.map((point, index) => {
                  let formatted = point.replace(/₹([\d,]+)/g, (_, num) => `₹${Number(num.replace(/,/g, "")).toLocaleString("en-IN")}`);
                  return (
                    <div key={index} className={styles.step}>
                      <Image src={icons[index % icons.length]} alt="step icon" />
                      <p dangerouslySetInnerHTML={{ __html: formatted }}></p>
                    </div>
                  );
                })}
              </div>
            </div>
          )} */}

          {activePlan && (
            <div className="py-4 card-section">
              <h2 className="pb-12 fw-bolder">How it works?</h2>
              <p className="subheading mb-0">Let’s say you select plan {activePlan.plan_id} of refundable EMI ₹{plan.A.toLocaleString("en-IN")}</p>

              <div className="d-flex-section pt-24">
                <div className="mobile-banner">
                  <Image src={mobilebanner1} alt="Mobile Banner 1" />
                </div>
                <div className="right-content">
                  <h3 className="subheding-2 fw-bolder date">{getTodayFormatted()}</h3>
                  <p className="plan-pra fw-bold mb-1">First EMI Payment</p>
                  <p className="plan-pra">
                    You pay ₹{Number(plan.D - plan.C).toLocaleString("en-IN")} (₹{plan.A.toLocaleString("en-IN")} as EMI + ₹{plan.B.toLocaleString("en-IN")} as processing fee)
                  </p>
                  <div className={styles.arrow1}>
                    <Image src={dot1} alt="" />
                  </div>
                </div>
              </div>

              <div className="position-relative">
                <div className="left-content">
                  <div className={styles.arrow2}>
                    <Image src={dot2} alt="" />
                  </div>
                  <div className="mobile-banner">
                    <Image src={mobilebanner2} alt="Mobile Banner 2" />
                  </div>
                  <div className="plan-pra tpl2">
                    You receive an <span className="fw-bold">EMI refund</span> of <span className="fw-bold">₹{plan.E.toLocaleString("en-IN")}</span>
                  </div>
                </div>
                <div className="home-banner">
                  <Image src={home} alt="Home Banner" />
                </div>
              </div>

              <div className="position-relative">
                <div className="left-content-2">
                  <div className="tpl1">
                    <h3 className="subheding-2 fw-bolder date">{getFormattedDate()}</h3>
                    <p className="plan-pra">
                      <span className="fw-bold">Second EMI Payment</span><br />
                      You pay ₹{plan.F.toLocaleString("en-IN")} as EMI amount
                    </p>
                  </div>
                  <div className={styles.arrow3}>
                    <Image src={dot3} alt="" />
                  </div>
                </div>
                <div className="home-banner pt-44">
                  <Image src={home} alt="Home Banner" />
                </div>
              </div>

              <div className="position-relative pt-4">
                <div className="left-content-3 d-flex">
                  <div className="mobile-banner">
                    <Image src={mobilebanner3} alt="Mobile Banner 3" />
                  </div>
                  <div className="plan-pra tpl3">
                    You receive an <span className="fw-bold">EMI <br />refund</span> of <span className="fw-bold">₹{plan.G.toLocaleString("en-IN")}</span>
                  </div>
                  <div className={styles.arrow4}>
                    <Image src={dot4} alt="" />
                  </div>
                </div>
              </div>

              <div className="-mt-24">
                <div className="total-refund">
                  <div className="checkmark-circle">✓</div>
                  <span>Total EMI refund received: <strong>₹{Number(activePlan.amount || 0).toLocaleString("en-IN")}</strong></span>
                </div>

                <div className="disclaimer">
                  <p>Credit score improvement is based on assumptions and depends on your credit bureau report and other external factors</p>
                  <p>Note: Credit Score Improvement fee is non refundable</p>
                </div>
              </div>
            </div>
          )}

          {/* <div className={styles.note}>Higher EMIs can boost your CIBIL score faster</div> */}

          <div className={`footerbottombtn ${styles.footerbottombtn}`} onClick={SaveCbPlan}>
            Proceed
          </div>

          {(pageerror || pagesuccess) && (
            <ToastMessage color={pagesuccess ? "green" : "red"}>{pagesuccess || pageerror}</ToastMessage>
          )}

          <CtEvents data={ctData} />
        </div>
      </div>
    </PageWrapper>
  );
}

export default CbLandingPage;
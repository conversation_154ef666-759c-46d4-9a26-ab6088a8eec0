'use client'
import React, { useEffect } from 'react';
import '../scss/button.scss';
import styles from '../kyc-loading/kycloading.module.scss'
import dynamic from "next/dynamic";
import loadingAnimation from "../../../public/Loader_Red.json"

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

function LoadingComp({faded} : {faded?: boolean}) {
    return (
        <div className={`external-wrapper ${styles.commonLoader}`} style={{background: faded ? 'rgba(255, 255, 255, 0.8)' : ''}}>
            {/* <div className={`lds-ellipsis`}><div></div><div></div><div></div><div></div></div> */}
            <Lottie
                animationData={loadingAnimation}
                loop={true}
                style={{ height: 100, width: 100 }} // Adjust size as needed
            />
        </div>
    );
}

export default LoadingComp;
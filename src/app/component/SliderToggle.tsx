import React from 'react';
import styles from './SliderToggle.module.scss';

interface SliderToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

const SliderToggle: React.FC<SliderToggleProps> = ({ checked, onChange }) => {
  return (
    <label className={styles.switch}>
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
      />
      <span className={styles.slider}></span>
    </label>
  );
};

export default SliderToggle;

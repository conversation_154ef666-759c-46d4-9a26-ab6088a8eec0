.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
  
    input {
      opacity: 0;
      width: 0;
      height: 0;
    }
  
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: 0.4s;
      border-radius: 34px;
  
      &::before {
        position: absolute;
        content: "";
        height: 20px;
        width: 20px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
        background: url(../images/checkslider.png) no-repeat;
        background-size: 100%;
      }
    }
  
    input:checked + .slider {
      background-color: var(--black);
    }
  
    input:checked + .slider::before {
      transform: translateX(24px);
    }
  }
  
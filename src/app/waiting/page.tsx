"use client"
import PageHeader from "../components/PageHeader"
import styles from './waiting.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import upperCarrot from '../images/uppercarrot.png';
import downCarrot from '../images/downcarrot.png';
import Image from "next/image";
import CtEvents from "../utils/Ctevents";

function WaitingPage() {
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

  const accordionData = [
    {
      "question": "Why I am getting “Application Submitted” Status from last many days?",
      "answer": 'The "Application Submitted" Status indicates that the team is evaluating your documents and working towards approving your application. While there may be occasional procedural delays, rest assured that the team is actively working on updating your status. Please be patient, and your application status will be updated as soon as the evaluation is complete. Till the time your application is being evaluated you can explore more products like CHR/PCR'
    },
    {
      "question": "How do I know if my application is approved?",
      "answer": "Once your application is approved you will receive an email and a notification on your mobile phone. You may also receive a notification if we require any further details to approve your application. You will also be able to check the status of your loan on the Stashfin App after logging in with your registered number."
    },
    {
      "question": "What are the interest rates offered?",
      "answer": "Stashfin loans start from 11.99% annually on reducing balance and a low processing fee. We do not charge service fees, prepayment fees, or any other hidden fees. Interest rates may vary from a case to case basis and the rates offered will depend on our assessment of your credit profile."
    }
  ];
  const [openAccordion, setOpenAccordion] = useState<string | null>(null);
  useThemeColor('#FFF3E4')
  const router = useRouter();
  const productCode = useMemo(() => {
    return typeof window !== "undefined" ? localStorage.getItem("product_code") ?? "" : "";
  }, []);
  const isFreedomUser = productCode && productCode === 'FREEDOM';
  const returnBackToApp = () => {
    // debugger;
    window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
  };
  const toggleAccordion = (key: string) => {
    setOpenAccordion((prev) => (prev === key ? null : key));
  };
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code")

    const event_property = { "Screen Name": 'Under Review', "Action": "", "CTA Shown": "Back to Home", "Sanction Amount": "150000", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source, "CTA": "", "Page shown":"Under Review"}
    setCtdata({ event_name, event_property })
  }, [])
  return (
    <div>
      <div className={styles.header}>
        <PageHeader title="" call={false} />
        <div className={styles.headercont}>
          <div className={styles.select}>
            <p className={styles.congotxt}>Application under process</p>
            <p>Your application has been successfully submitted. You will be notified within {isFreedomUser ? (<strong>7 Days</strong>) : (<><strong>24</strong> to <strong>48</strong> hours</>)}</p>
          </div>
          <div className={styles.icon}></div>
        </div>
      </div>
      <div className={styles.body}>
        {/* <div className={styles.creditbuilderbox}>
          <div className={styles.title}>
            cash against mutual funds
          </div>
          <h2>Use your mutual funds to get instant cash</h2>
          <p>Use your mutual funds to get instant cash</p>
          <div className={styles.circularbtn}>Check now</div>
        </div> */}
        <div className={styles.faqsparent}>
          <div className={styles.faqstitle}>
            Frequently Asked Questions (FAQs)
          </div>
          <div className={styles.accordion}>
            {accordionData.map((item, index) => {
              const itemKey = `${index}`;
              const isOpen = openAccordion === itemKey;

              return (
                <div key={itemKey} className={styles.accordion}>
                  <div
                    className={isOpen ? styles.accordionHeaderActive : styles.accordionHeader}
                    onClick={() => toggleAccordion(itemKey)}
                  >
                    <h2>
                      {item.question}
                      {isOpen ? (
                        <Image src={upperCarrot} alt="" />
                      ) : (
                        <Image src={downCarrot} alt="" />
                      )}
                    </h2>
                  </div>
                  {isOpen && (
                    <div className={styles.accordionContent}>
                      <div className={styles.faqItem}>
                        <p className={styles.faqAnswer}>{item.answer}</p>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
        <div className="bottom-footer p-0 mt-auto">
          <div className={`footerbottombtn ${styles.footerbottombtn}`} onClick={() => returnBackToApp()}>Back to Home</div>
        </div>
      </div>
      <CtEvents data={ctData} />

    </div>

  )
}

export default WaitingPage
.header {
    background: #FFF3E4;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 0;
    header{
        margin-bottom: 0;
    }

    .headercont {
        padding-top: 15px;
        display: flex;
        padding-bottom: 54px;

        >div {
            &.select {
                padding-top: 0;

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 25px;
                    color: var(--black);
                    padding-right: 10px;

                    &.congotxt {
                        font-family: var(--font-mona-sans);
                        font-weight: 700;
                        font-size: 20px;
                        margin-bottom: 5px;
                        line-height: 28px;
                        color: #F5932A;
                    }
                }


            }

            &.icon {
                min-width: 72px;
                height: 80px;
                display: flex;
                background: url(../images/waiting.png) no-repeat;
                background-size: 100%;
            }
        }
    }
    header {
        min-height: auto;
        background: transparent;
        padding: 0;
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;

    .circularbtn {
        padding: 8px 16px;
        border-radius: 66px;
        border: 1px solid #000000;
        background: #fff;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        line-height: 24px;
        font-size: 16px;
        color: #000;
        cursor: pointer;
    }

    .whatucantitle {
        font-family: var(--font-mona-sans);
        padding: 25px 0 16px 0;
        font-weight: 700;
        line-height: 24px;
        font-size: 16px;
        color: #000;
    }

    .creditbuilderbox {
        padding: 26px 12px 121px 12px;
        background: #E4FFD9 url(../images/creditmetericon.png) no-repeat;
        background-position: bottom 15px right 16px;
        border-radius: 12px;
        margin-top: 30px;

        .title {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            line-height: 18px;
            font-size: 12px;
            color: #000000;
            text-transform: uppercase;
        }

        h2 {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            line-height: 34px;
            font-size: 24px;
            color: var(--black);
            margin-top: 9px;
        }

        p {
            margin-top: 0;
            font-family: var(--font-mona-sans);
            font-weight: 400;
            padding-right: 50px;
            line-height: 24px;
            font-size: 16px;
            color: #000;
        }

        .circularbtn {
            margin-top: 35px;
            max-width: 121px;
        }
    }
    .faqsparent {
        margin-top: 30px;

        .faqstitle {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            line-height: 24px;
            font-size: 16px;
            color: #000;

            &+div.accordion {
                margin-top: 16px;

                .accordionHeader {
                    cursor: pointer;

                    h2 {
                        display: flex;
                        justify-content: space-between;
                        font-family: var(--font-mona-sans);
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 22px;
                        color: var(--black);

                        img {
                            width: 24px;
                            height: 24px;
                        }
                    }
                }

                .accordionHeaderActive {
                    cursor: pointer;

                    h2 {
                        display: flex;
                        justify-content: space-between;
                        font-family: var(--font-mona-sans);
                        font-weight: 600;
                        font-size: 14px;
                        color: var(--black);
                        line-height: 17px;

                        img {
                            width: 24px;
                            height: 24px;
                        }
                    }

                }

                >div {
                    margin-bottom: 16px;

                    p {
                        font-family: var(--font-mona-sans);
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 18px;
                        color: var(--black);
                    }
                }
            }
        }

    }
    .footerbottombtn {
        margin-bottom: 12px;
    }
}
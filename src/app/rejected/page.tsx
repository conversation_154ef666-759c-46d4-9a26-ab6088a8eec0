"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './rejected.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import { useEffect, useState } from "react"
import upperCarrot from '../images/uppercarrot.png';
import downCarrot from '../images/downcarrot.png';
import '../scss/button.scss'
import React from "react"
import { useRouter, useSearchParams } from "next/navigation";
import CtEvents from "../utils/Ctevents"
import { navigateToPage } from "../utils/bridge"

function RejectedPage() {
  useThemeColor('#FFE7E7')
  const router = useRouter();
  const searchParams = useSearchParams();
  const postDisbursal = searchParams.get('postDisbursal');
  const accordionData = [
    {
      "question": "Why my application is rejected?",
      "answer": "Your application is evaluated based on several factors. If any of these criteria are not met, your application will get rejected. This gives you the opportunity to improve aspects of your profile, such as your credit history, which will help strengthen your reapplication. For now, the loan cannot be approved, but we encourage you to work on these areas to reapply in the future when you feel any positive changes on your profile."
    },
    {
      "question": "Can I re-apply if I have been previously rejected for a loan?",
      "answer": "Yes, you can reapply after a period of 45 days."
    },
    {
      "question": "How is having higher credit score beneficial?",
      "answer": "With High Credit score you have higher chances of getting an approval for a Higher Credit Line with best Interest Rates. You can increase your Credit Score by ensuring timely EMI payment of all your loans."
    }
  ];
  const [openAccordion, setOpenAccordion] = useState<string | null>(null);
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

  const toggleAccordion = (key: string) => {
    setOpenAccordion((prev) => (prev === key ? null : key));
  };
  // useEffect(() => {
  //   const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
  //   const event_name = 'Screen View'
  //   const productcode = localStorage.getItem("product_code")

  //   const event_property = { "Screen Name": 'LOC_Rejected', "Action": "", "Mandate Amount": "", "CTA Shown": "Back to Home", "Sanction Amount": "", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP, "Source": cteventsfromstorage?.source }
  //   setCtdata({ event_name, event_property })
  // }, [])
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code") || "n/a"

    const event_property = { "Screen Name": 'LOC_Rejected', "Action": "", "CTA Shown": "Back to Home", "Sanction Amount": "150000", "Product category": productcode, "Segment": cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source, "CTA": "", "Page shown":"LOC_Rejected"}
    setCtdata({ event_name, event_property })
  }, [])
  const returnBackToApp = () => {
    // debugger;
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Button Clicked'
    const productcode = localStorage.getItem("product_code") || "n/a"

    const event_property = { "CTA": 'Back to Home', "Page Shown": "Congrats Screen", "Banner": "LAMF", "Product category": productcode, "Segment": cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP", }
    setCtdata({ event_name, event_property })
    window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
  };
  const commonCTa = (ctaname: string, bannername: string) => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Button Clicked'
    const event_property = { "Page shown": 'LOC_Rejected', "CTA": ctaname, "Banner": bannername }
    setCtdata({ event_name, event_property })
  }
  return (
    //onClick={() => commonCTa('Refer Now', 'Referral')}
    /*onClick={() => {
      navigateToPage({
        type: "page",
        landingPage: "MF_form"
      })
    }}*/
    <div>
      <div className={styles.header} >
        <PageHeader title="" />
        <div className={styles.headercont}>
          <div className={styles.reject}>
            <p className={styles.congotxt}>
              {postDisbursal ? "We are unable to process your fund request" : "Application unsuccessful"}
            </p>
            <p>{postDisbursal ? "Sorry, we can’t transfer the funds, as it goes against our credit policy. We recommend trying again after 30 days." : "Thank you for applying. Regrettably, we're unable to offer a loan today. We invite you to reapply in <strong>90 days</strong>, and we hope to serve you then."}</p>
            {/* <span>You are welcome to reapply after <strong>90</strong> days.</span> */}
          </div>
          <div className={styles.icon}></div>
        </div>
      </div>
      <div className={styles.body} style={{height:"calc(100vh - 268px)", display:'flex', flexFlow:"column"}}>
        {/* <div className={styles.whatucantitle}>
          What you may do now
        </div>
        <div className={styles.creditbuilderbox}>
          <div className={styles.title}>
            Refer & Earn
          </div>
          <h2>Invite friends and get ₹500 for every loan disbursal.</h2>
          <div className={styles.circularbtn}

            onClick={() => {
              if (process.env.NEXT_PUBLIC_LIVE_URL === window.location.origin) {
                window.location.href = 'https://www.stashfin.com/refer-and-earn/play-and-win'
              } else {
                window.location.href = 'https://dev.stashfin.com/refer-n-earn/home'
              }
            }}
          >Refer Now</div>
        </div> */}
        {/* <div className={styles.otheroffer}>
          <div className={styles.yellow}>
            <div className={styles.title}>
              cash against mutual funds
            </div>
            <p>Use your mutual funds to get instant cash</p>
            <div className={styles.circularbtn} onClick={() => commonCTa('Check Now', "LAMF")}>Check now</div>
          </div>
          <div className={styles.green}>
            <div className={styles.title}>
              credit<br />
              report
            </div>
            <p>Check your Credit score for FREE</p>
            <div className={styles.circularbtn}
              //onClick={() => commonCTa('Check Now', "CHR")}
              onClick={() => {
                navigateToPage({
                  type: "page",
                  landingPage: "pcr_launch"
                })
              }}
            >Check now</div>
          </div>
        </div> */}
        <div className={styles.faqsparent}>
          <div className={styles.faqstitle}>
            Frequently Asked Questions (FAQs)
          </div>
          <div className={styles.accordion}>
            {accordionData.map((item, index) => {
              const itemKey = `${index}`;
              const isOpen = openAccordion === itemKey;

              return (
                <div key={itemKey} className={styles.accordion}>
                  <div
                    className={isOpen ? styles.accordionHeaderActive : styles.accordionHeader}
                    onClick={() => toggleAccordion(itemKey)}
                  >
                    <h2>
                      {item.question}
                      {isOpen ? (
                        <Image src={upperCarrot} alt="" />
                      ) : (
                        <Image src={downCarrot} alt="" />
                      )}
                    </h2>
                  </div>
                  {isOpen && (
                    <div className={styles.accordionContent}>
                      <div className={styles.faqItem}>
                        <p className={styles.faqAnswer}>{item.answer}</p>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
        <div className="bottom-footer p-0" style={{marginTop:'auto'}}>
          <button className="btn btn-primary" onClick={() => returnBackToApp()}>Back to Home</button>
        </div>
      </div>
      <CtEvents data={ctData} />

    </div>
  )
}

export default RejectedPage
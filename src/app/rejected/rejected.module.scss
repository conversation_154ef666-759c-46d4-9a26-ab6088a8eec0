.header {
    background: #FFE7E7;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 0;

    .headercont {
        padding-top: 0;
        display: flex;
        padding-bottom: 30px;

        >div {
            &.reject {
                padding-top: 0;
                padding-right: 30px;

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 25px;
                    color: var(--black);

                    &.congotxt {
                        font-family: var(--font-mona-sans);
                        font-weight: 700;
                        font-size: 20px;
                        line-height: 28px;
                        color: var(--black);
                        padding-bottom: 10px;
                    }
                }

                span {
                    font-family: var(--font-mona-sans);
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 22px;
                    color: var(--black);
                }
            }

            &.icon {
                min-width: 72px;
                height: 80px;
                display: flex;
                background: url(../images/rejectedheadericon.png) no-repeat;
                background-size: 100%;
            }
        }
    }
    header {
        min-height: auto;
        background: transparent;
        padding: 0;
        margin-bottom: 15px;
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;

    .circularbtn {
        padding: 8px 16px;
        border-radius: 66px;
        border: 1px solid #000000;
        background: #fff;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        line-height: 24px;
        font-size: 16px;
        color: #000;
        cursor: pointer;
    }

    .whatucantitle {
        font-family: var(--font-mona-sans);
        padding: 35px 0 12px;
        font-weight: 700;
        line-height: 24px;
        font-size: 16px;
        color: #000;
    }

    .creditbuilderbox {
        padding: 24px 12px 40px 12px;
        background: #F0F0F0 url(../images/dhan.png) no-repeat;
        background-position: bottom 13px right 16px;
        border-radius: 12px;

        .title {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            line-height: 16.8px;
            font-size: 14px;
            color: #555555;
            text-transform: uppercase;
        }

        h2 {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            line-height: 24px;
            font-size: 20px;
            color: var(--black);
            margin-top: 10px;
            padding-right: 60px;
        }

        p {
            margin-top: 9px;
            font-family: var(--font-mona-sans);
            font-weight: 600;
            line-height: 24px;
            font-size: 16px;
            color: #000;
        }

        .circularbtn {
            margin-top: 15px;
            max-width: 115px;
        }
    }

    .otheroffer {
        display: flex;
        margin-top: 17px;
        justify-content: space-between;

        >div {
            width: calc(50% - 6px);
            padding: 12px 10px 0 10px;
            border-radius: 12px;

            .title {
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 12px;
                line-height: 18px;
                color: #555555;
                text-transform: uppercase;
            }

            p {
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                margin-top: 12px;
            }

            .circularbtn {
                margin-top: 12px;
                max-width: 120px;
                font-size: 12px;
                max-width: 100px;
                max-height: 30px;
                line-height: 1.1;
            }

            &.yellow {
                padding-bottom: 82px;
                background: #F0F0F0 url(../images/mutualfunddhan.png) no-repeat;
                background-position: bottom 5px right 3px;
            }

            &.green {
                padding-bottom: 104px;
                background: #F0F0F0 url(../images/credittextimg.png) no-repeat;
                background-position: bottom 0 right 16px;
            }
        }
    }

    .faqsparent {
        margin-top: 30px;

        .faqstitle {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            line-height: 24px;
            font-size: 16px;
            color: #000;

            &+div.accordion {
                margin-top: 16px;

                .accordionHeader {
                    cursor: pointer;

                    h2 {
                        display: flex;
                        justify-content: space-between;
                        font-family: var(--font-mona-sans);
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 22px;
                        color: var(--black);

                        img {
                            width: 24px;
                            height: 24px;
                        }
                    }
                }

                .accordionHeaderActive {
                    cursor: pointer;

                    h2 {
                        display: flex;
                        justify-content: space-between;
                        font-family: var(--font-mona-sans);
                        font-weight: 600;
                        font-size: 14px;
                        color: var(--black);
                        line-height: 17px;

                        img {
                            width: 24px;
                            height: 24px;
                        }
                    }

                }

                >div {
                    margin-bottom: 16px;

                    p {
                        font-family: var(--font-mona-sans);
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 18px;
                        color: var(--black);
                    }
                }
            }
        }

    }

    .footerbottombtn {
        background: var(--black);
    }
}
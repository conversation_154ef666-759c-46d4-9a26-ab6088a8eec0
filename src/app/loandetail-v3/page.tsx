"use client";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import LoadingComp from "../component/loader";
import BankImage from "../components/BankImage";
import PageHeader from "../components/PageHeader";
import BottomPopup from "../components/popups/BottomPopup";
import ToastMessage from "../components/ToastMessage/ToastMessage";
import { useThemeColor } from "../hooks/useThemeColor";
import GIFT_IMG from "../images/gift_image.png";
import "../scss/button.scss";
import "../scss/form.scss";
import { ApiResponse } from "../store/interface/apiInterface";
import { formatCurrency } from "../store/middleware/currencyFormatter";
import { RootState } from "../store/store";
import { apiRequest } from "../utils/api";
import CtEvents from "../utils/Ctevents";
import { ENDPOINTS } from "../utils/endpoints";
import { logFirebaseEvent } from "../utils/firebaseAnalytics";
import { numberToWords, roundDownToNearest500 } from "../utils/helperFunctions";
import ChangeAmountPopup from "./components/change-amount/ChangeAmountPopup";
import EmiPopup from "./components/emiAmountPopup/EmiAmountPopup";
import ExclusiveBanner from "./components/exclusive-banner/ExclusiveBanner";
import Expandable from "./Expandable";
import IncreaseAmountRibbon from "./IncreaseAmountRibbon";
import styles from "./loandetail.module.scss";
import validcheck from "../images/valid-check.svg";

import {
  setCustomerBankDetails,
  setIsChecked,
  setLoanWithdrawData,
  setPageError,
  setPageSuccess,
  setShowChangeAmtPopup,
  toggleEmiModal,
  setLoanDetails,
  setAmount,
  setBreakpoints,
  setLoaded,
  setLoader,
  setShowFcpBanner,
} from "./loanDetails.slice";
import SuccessBatch from "../components/SuccessBatch";
import { pushCleverTapEvent } from "../utils/cleverTapClient";

function LoanDetailPage() {
  useThemeColor("#f5f5f5");
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const fromPostDisbursal = searchParams.get("postDisbursal");
  const {
    pageerror,
    pagesuccess,
    isChecked,
    loanWithdrawData,
    emiModal,
    customerBankDetails,
    showChangeAmtPopup,
    isCibilChecked,
    isLppChecked,
    amount,
    loader,
    loaded,
    showFcpBanner,
  } = useSelector((state: RootState) => state.loanDetailV3);

  // Utility function to safely access localStorage
  const getLocalStorageItem = useCallback(
    (key: string, fallback: string = "") => {
      if (typeof window !== "undefined") {
        return localStorage.getItem(key) || fallback;
      }
      return fallback;
    },
    []
  );

  const getLocalStorageJSON = useCallback((key: string) => {
    if (typeof window !== "undefined") {
      try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
      } catch (error) {
        console.error(`Error parsing ${key} from localStorage:`, error);
        return null;
      }
    }
    return null;
  }, []);

  const eventsData = useMemo(() => {
    return getLocalStorageJSON("eventsdata");
  }, [getLocalStorageJSON]);

  const [ctData, setCtdata] = useState<{
    event_name: string;
    event_property: Record<string, any>;
  } | null>(null);
  const [showSuccessBatch, setShowSuccessBatch] = useState(false);

  const isUserStp = useMemo(() => {
    const cteventsfromstorage = getLocalStorageJSON("eventsdata");
    return !cteventsfromstorage?.isNonSTP;
  }, []);

  // COMMON CT EVENT
  const commonCtEvent = (cta: string) => {

    const event_property = {
      CTA: cta,
      "Product category": eventsData?.product_code,
      Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
      "Page Name": "LOC_Offer Details",
    };
    pushCleverTapEvent({eventName: "Button Clicked", eventData: event_property});
  }

  // CHANGE AMOUNT POPUP
  const handleChangeAmtPopup = (callEvent: boolean = true) => {
    const event_name = "Button Clicked";
    const productcode = getLocalStorageItem("product_code");

    if(!showChangeAmtPopup){
      const event_property = {
        CTA: "Change Amount",
        "Product category": productcode,
        Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
        "Page Name": "LOC_Offer Details",
      };
      pushCleverTapEvent({ eventName: event_name, eventData: event_property });
    }

    if(showChangeAmtPopup && callEvent){
      const cancel_event_property = {
        CTA: "Cancel",
        "Product category": productcode,
        Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
        "Page Name": "Limit Slider",
        "Selected Amount": amount,
      }
      pushCleverTapEvent({eventName: event_name, eventData: cancel_event_property});
    }
    dispatch(setShowChangeAmtPopup(!showChangeAmtPopup));
  };
  

  // HANDLE NOTIFY ME
  const handleNotifyMe = () => {
    // EVENT ONE
    const event_name = "Button Clicked";
    const productcode = getLocalStorageItem("product_code", "n/a");
    const event_property = {
      CTA: "Notify Me",
      "Product category": productcode,
      Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
      "Page Name": "LOC_Offer Details",
    };
    setCtdata({ event_name, event_property });

    // EVENT TWO
    const fcp_event_name = "FCP Notify";
    const fcp_event_property = {
      "Full Amount": loanWithdrawData?.bill_amount,
      "FCP Due Date": loanWithdrawData?.bill_date,
    };
    setTimeout(() => {
      setCtdata({
        event_name: fcp_event_name,
        event_property: fcp_event_property,
      });
    }, 50);

    // Show success batch for 3 seconds
    setShowSuccessBatch(true);
    setTimeout(() => {
      setShowSuccessBatch(false);
    }, 3000);

    dispatch(setShowFcpBanner(false));
  };

  // TRANSFER LOAN AMOUNT
  const transferLoanAmount = async () => {
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    dispatch(setLoader(true));
    try {
      // EVENT ONE
      const event_name = "Button Clicked";
      const productcode = getLocalStorageItem("product_code", "n/a");
      const event_property = {
        CTA: "Transfer Loan amount",
        "Product category": productcode,
        Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
        "Page Name": "LOC_Offer Details",
      };
      setCtdata({ event_name, event_property });

      // EVENT TWO
      const transfer_event_name = "LOC_Offer Details";
      const transfer_event_property = {
        "Disbursed Amount": amount,
        "Loan amount": loanWithdrawData.approved_amount,
        "Cibil Add on": isCibilChecked,
        "Loan Protect Add on": isLppChecked,
        "Amount you will get": loanWithdrawData.final_disbursal_amount,
        "EMI Start Date": loanWithdrawData.first_emi_date,
        "No. of EMIs": loanWithdrawData.tenure,
        "EMI Amount": loanWithdrawData.emi_amount,
        Source: eventsData?.source,
        "Product category": productcode,
        Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
      };
      setTimeout(() => {
        setCtdata({ event_name: transfer_event_name, event_property: transfer_event_property });
      }, 50);

      // API CALL
      const payload = {
        amount: loanWithdrawData.approved_amount,
        credit_report_fees: isLppChecked
          ? loanWithdrawData.credit_report_fees
          : 0,
        gateway_type: "",
        early_foreclosure_fee: 0.0,
        is_fcp: true, //loanWithdrawData?.is_fcp || false
        txn_id: loanWithdrawData?.txn_id || "",
        is_fip: loanWithdrawData?.is_fip || true,
        max_amount: isCibilChecked ? loanWithdrawData?.max_amount : 0,
        gateway_fees: null,
        tenure: loanWithdrawData.tenure,
        plan_id: isLppChecked ? loanWithdrawData?.plan_id : 0,
      };
      const response = await apiRequest<ApiResponse>(
        "POST",
        ENDPOINTS.getlocconfirm,
        payload
      );
      if (
        response.mainData?.success?.toString().toLowerCase() === "true" ||
        response.mainData?.status === "success" ||
        response.mainData?.status === "true"
      ) {
        const productcode = getLocalStorageItem("product_code");

        const event_property = {
          "Disbursed Amount": loanWithdrawData?.final_disbursal_amount,
          "Loan amount": loanWithdrawData?.approved_amount,
          "Fee & charges": loanWithdrawData?.fee_and_charges,
          "Cibil Add on": isCibilChecked,
          "Loan Protect Add on": isLppChecked,
          "Amount you will get":
          loanWithdrawData?.final_disbursal_amount,
          "EMI Start Date": loanWithdrawData?.first_emi_date,
          "No. of EMIs":
          loanWithdrawData?.installment_amounts.split(",").length,
          "EMI Amount": loanWithdrawData?.emi_amount,
          "Total Payable Amount": loanWithdrawData?.approved_amount,
          "EMI Deducted Bank": customerBankDetails[0]?.bank_name,
          "EMI Deducted A/C no.": customerBankDetails[0]?.account_number,
          Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
          Source: eventsData?.source,
          "Product category": productcode,
        }

        setCtdata({ event_name: "LOC_Draw", event_property });
        setTimeout(() => {
          setCtdata({ event_name: "loc_pull", event_property });
          logFirebaseEvent({
            eventName: "LOC_Draw",
            eventParams: event_property,
          });
          logFirebaseEvent({
            eventName: "loc_pull",
            eventParams: event_property,
          });
        }, 100);

        dispatch(setLoader(false));

        dispatch(setPageSuccess(response.mainData.data.msg));
        setTimeout(async () => {
          router.push("/success");
        }, 1000);
      } else {
        dispatch(setLoader(false));
        dispatch(
          setPageError(
            response.mainData?.error_message || "Unexpected error occurred."
          )
        );
        setTimeout(() => {
          router.push("/rejected");
        }, 1000);
      }
    } catch (error) {
      dispatch(setLoader(false));
      dispatch(setPageError("Error in withdrawing amount"));
      setTimeout(() => {
        router.push("/rejected");
      }, 1000);
    }
  };

  // GET ALL LOAN DETAILS
  const getAllLoanDetails = async (
    selectedAmount?: number,
    firstTime?: boolean
  ) => {
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    try {
      const payload = {
        amount: selectedAmount || amount,
        gateway_enable: 0,
        credit_shield_enable: isCibilChecked,
        pcr_enable: isLppChecked,
        gateway_type: "",
        credit_shield_opt: isCibilChecked,
        pcr_opt: isLppChecked,
        is_cli: false,
      };
      const response = await apiRequest<ApiResponse>(
        "POST",
        ENDPOINTS.getLoanDetails,
        payload
      );
      if (response.mainData?.success) {
        dispatch(setLoanWithdrawData(response.mainData.data));
        dispatch(setAmount(response.mainData.data.amount));
        const productcode = getLocalStorageItem("product_code");
        const cteventsfromstorage = getLocalStorageJSON("eventsdata");

        if (firstTime) {
          const event_name = "Screen View";
          const event_property = {
            "Screen Name": "LOC_Offer Details",
            "Product category": productcode,
            Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
            Source: eventsData?.source,
            "Sanction Amount": response.mainData.data.approved_amount,
            "Disbural Amount": response.mainData.data.amount,
            "Banner type": "FCP",
          };
          setCtdata({ event_name, event_property });
        } else {
          const event_name = "LOC_Offer Details";
          const event_property = {
            "Disbursed Amount": response.mainData.data.final_disbursal_amount,
            "Loan amount": response.mainData.data.approved_amount,
            "Fee & charges": response.mainData.data.fee_and_charges,
            "Cibil Add on": payload.pcr_enable,
            "Loan Protect Add on": payload.credit_shield_opt,
            "Amount you will get":
              response.mainData.data.final_disbursal_amount,
            "EMI Start Date": response.mainData.data.first_emi_date,
            "No. of EMIs":
              response.mainData.data.installment_amounts.split(",").length,
            "EMI Amount": response.mainData.data.emi_amount,
            "Product category": productcode,
            Segment: cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP",
            Source: cteventsfromstorage?.source,
          };
          setCtdata({ event_name, event_property });
        }
        dispatch(setLoader(false));
        dispatch(setLoaded(false));
      } else {
        dispatch(
          setPageError(
            response.mainData?.error_message || "Unexpected error occurred."
          )
        );
        dispatch(setLoader(false));
        dispatch(setLoaded(false));
      }
    } catch (error) {
      dispatch(setPageError("Something went wrong"));
      if (fromPostDisbursal) {
        router.push("/post-disbursal/select-withdraw-amount");
        dispatch(setLoader(false));
        dispatch(setLoaded(false));
        return;
      }
      dispatch(setLoader(false));
      dispatch(setLoaded(false));
    } finally {
      setTimeout(() => {
        dispatch(setLoader(false));
      }, 2000);
    }
  };

  // GET ALL LOAN DETAILS ON AMOUNT CHANGE
  useEffect(() => {
    if (amount) {
      getAllLoanDetails(0, true);
    }
  }, [isCibilChecked, isLppChecked]);

  // GET USER BANK DETAILS
  const fetchUserBank = async () => {
    try {
      const response = await apiRequest<ApiResponse>(
        "GET",
        ENDPOINTS.customerBankList
      );
      if (
        response.mainData?.success?.toString().toLowerCase() === "true" ||
        response.mainData?.status === "success" ||
        response.mainData?.status === "true"
      ) {
        dispatch(setCustomerBankDetails(response.mainData.data));
      } else {
        dispatch(
          setPageError(
            response.mainData?.error_message || "Unexpected error occurred."
          )
        );
      }
    } catch (error) {
      dispatch(setPageError("Error submitting address confirmation"));
    }
  };

  // GET LOAN AMOUNT ON INITAIL PAGE LOAD
  const getPlLoanData = async () => {
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    try {
      const response = await apiRequest<ApiResponse>(
        "GET",
        ENDPOINTS.getlocwithdrawform
      );
      if (response.mainData?.success) {
        dispatch(setLoanDetails(response.mainData.data));

        const bpArr: any = [];
        let minAmount = response.mainData.data.minimum_request_amount;
        let step = response.mainData.data.request_amount_increment_step;
        let remainingAmount = roundDownToNearest500(
          response.mainData.data.remaining_loc
        );
        for (let i = minAmount; i <= remainingAmount; i += step) {
          if (i + step > remainingAmount && i !== remainingAmount) {
            bpArr.push(remainingAmount);
            break;
          }
          bpArr.push(i);
        }
        dispatch(setBreakpoints(bpArr));
        dispatch(setAmount(bpArr[bpArr.length - 1]));
        await fetchUserBank();
        await getAllLoanDetails(bpArr[bpArr.length - 1], true);
      } else {
        dispatch(
          setPageError(
            response.mainData?.error_message || "Unexpected error occurred."
          )
        );
      }
    } catch (error) {
      dispatch(setLoader(false));
      dispatch(setPageError("Something went wrong. Please try again"));
    }
  };

  useEffect(() => {
    getPlLoanData();
    const cteventsfromstorage = JSON.parse(
      localStorage.getItem("eventsdata") || "null"
    );
    const event_name = "Screen View";
    const productcode = localStorage.getItem("product_code");

    const event_property = {
      "Screen Name": "LOC_Offer Details",
      "Product category": productcode,
      Segment: cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP",
      Source: cteventsfromstorage?.source,
    };
    setCtdata({ event_name, event_property });
  }, []);
  // -----------------------------------
  if (loader) {
    return <LoadingComp faded={false} />;
  }

  return (
    <div>
      {showSuccessBatch && (
        <SuccessBatch className={styles.notifySuccessBatch}>
          <p style={{fontSize: '14px', fontWeight: 500}}>Got it! We'll ping you so you don't miss out</p>
        </SuccessBatch>
      )}
      <div className={styles.header}>
        <PageHeader title="" increaseLimit={!isUserStp} amount={amount} />
        <div className={styles.top_section_container}>
          <div>
            <p className={styles.disbursal_amount_label}>Disbursal Amount</p>
            <span className={styles.disbursal_currency}>
              ₹{formatCurrency(Number(amount))}
            </span>
            <p className={styles.currency_in_words}>
              {numberToWords(Number(amount))}
            </p>
            <p onClick={() => handleChangeAmtPopup()} className={styles.change_amount}>
              Change Amount
            </p>
          </div>

          <div className={styles.icon}>
            <Image src={GIFT_IMG} alt="Gift" width={76} />
          </div>
        </div>
      </div>

      {isUserStp && <IncreaseAmountRibbon commonCtEvent={commonCtEvent} />}
      {/* TOP/HEADER SECTION ENDSSSS */}

      {/* EXPANDABLE SECTION */}
      <Expandable commonCtEvent={commonCtEvent} />

      {/* BANK-SECTION */}
      <div className={styles.bankdetail_container}>
        <div className={styles.bankdetail}>
          <p>
            Loan amount will be <br /> transferred to
          </p>
          {customerBankDetails && customerBankDetails.length ? (
            <div className={styles.bankbox}>
              <div>
                <p>{customerBankDetails[0]?.bank_name}</p>
                <span>{customerBankDetails[0]?.account_number}</span>
                <Image
                  src={validcheck}
                  alt=""
                  width={16}
                  className={styles.validcheck}
                />
              </div>

              <BankImage
                bankName={customerBankDetails[0]?.bank_name}
                ratioHeight={48}
              />
            </div>
          ) : null}
        </div>
      </div>

      {showFcpBanner && <ExclusiveBanner handleNotifyMe={handleNotifyMe} />}

      {/* CHECKBOX */}
      <div className={`consent-check ${styles.checkbox_container}`}>
        <label>
          <div className={`checkbox ${isChecked ? "checked" : ""}`}>
            <input
              type="checkbox"
              checked={isChecked}
              onChange={() => {
                commonCtEvent("Consent checkbox");
                dispatch(setIsChecked(!isChecked));
              }}
            />
            <div className={`circle ${!isChecked ? "animate" : ""}`}></div>
          </div>
          <p>
            I have read, understood and accept all the terms in the{" "}
            <Link
              href={loanWithdrawData?.financing_documents || ""}
              target="_blank"
            >
              Financing Documents
            </Link>{" "}
            (including Loan Agreement, KFS, and Sanction Letter).
          </p>
        </label>
      </div>

      {/* Footer */}
      <div
        className="bottom-footer"
        style={{ padding: "0 16px", marginTop: 16 }}
      >
        <button
          className={`btn btn-primary ${isChecked ? "" : "disabled"}`}
          onClick={transferLoanAmount}
        >
          Transfer Loan Amount Now
        </button>
        <p className="powered-by">
          Powered by Akara Capital Advisors Private Limited
        </p>
      </div>

      {/* EMI DETAILS POPUP */}
      <BottomPopup
        isOpen={emiModal}
        onClose={() => dispatch(toggleEmiModal(false))}
        title={loanWithdrawData?.emi_amount_l}
        buttons={[]}
      >
        <EmiPopup />
      </BottomPopup>

      {/* CHANGE AMOUNT SLIDER POPUP */}
      <BottomPopup isOpen={showChangeAmtPopup} onClose={handleChangeAmtPopup}>
        <ChangeAmountPopup
          getAllLoanDetails={getAllLoanDetails}
          handleChangeAmtPopup={handleChangeAmtPopup}
          eventsData={eventsData}
        />
      </BottomPopup>

      {loaded && <LoadingComp faded />}

      <CtEvents data={ctData} />
      {pageerror || pagesuccess ? (
        <ToastMessage color={pagesuccess ? "green" : "red"}>
          {pagesuccess ? pagesuccess : pageerror}
        </ToastMessage>
      ) : null}
    </div>
  );
}

export default LoanDetailPage;

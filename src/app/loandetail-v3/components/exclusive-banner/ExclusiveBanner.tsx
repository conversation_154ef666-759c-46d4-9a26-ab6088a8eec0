import React from "react";
import styles from "./exclusive.module.scss";
import Exclusive_img from "../../../images/exclusive_image.png";
import Gift_img from "../../../images/gift_image.png";
import Image from "next/image";
import { RootState } from "../../../store/store";
import { useSelector } from "react-redux";
import { format } from "date-fns";

const ExclusiveBanner = ({handleNotifyMe}: {handleNotifyMe: () => void}) => {
  const {loanWithdrawData } = useSelector((state: RootState) => state.loanDetailV3);
  return (
    <div className={styles.exclusive_banner_container}>
      <Image src={Exclusive_img} alt="exclusive for you" className={styles.exclusive_image} />
      <div className={styles.internal_container}>
        <div className={styles.exclusive_banner}>
          <p className={styles.heading}>Stashfin Zero Interest Period</p>
          <p className={styles.subHeading}>
            Pay full amount <span className={styles.amount}> ₹{Number(loanWithdrawData?.bill_amount).toLocaleString("en-IN")} </span> by <br/>
            {loanWithdrawData?.bill_date && format(new Date(loanWithdrawData.bill_date), "MMM d")} to enjoy zero interest
          </p>
          <button onClick={handleNotifyMe} className={styles.notify_button}>Notify me</button>
        </div>
        <Image src={Gift_img} alt="gift image" className={styles.gift_image} />
      </div>
    </div>
  );
};

export default ExclusiveBanner;

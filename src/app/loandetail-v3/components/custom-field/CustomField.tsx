import { ReactNode, useCallback, useState } from "react";
import styles from "../../loandetail.module.scss";
import { FaAngleDown } from "react-icons/fa6";
import { FaAngleUp } from "react-icons/fa6";
import { formatCurrency } from "../../../store/middleware/currencyFormatter";
import { IoIosInformationCircleOutline } from "react-icons/io";
import Image from "next/image"
import uppercarrot from '../../../images/uppercarrot.png'
import downcarrot from '../../../images/downcarrot.png'

interface CustomFieldProps {
  label: string;
  value: string | number;
  isCurrency?: boolean;
  borderEnd?: boolean;
  borderStart?: boolean;
  dropdown?: boolean;
  dropdownContent?: ReactNode;
  infoIcon?: boolean;
  onInfoIconClick?: () => void;
  commonCtEvent?: (cta: string) => void;
  cta?: string;
}

const CustomField = ({
  label,
  value,
  isCurrency,
  borderStart,
  borderEnd,
  dropdown,
  dropdownContent,
  infoIcon,
  onInfoIconClick,
  commonCtEvent,
  cta,
}: CustomFieldProps) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);

  const handleIsExpanded = () => {
    if(commonCtEvent){
      if(isExpanded){
        commonCtEvent("Show less " + cta);
      }else{
        commonCtEvent("Show more " + cta);
      }
    }
    setIsExpanded(!isExpanded);
  };

  const renderDropdown = useCallback(() => {
    if (dropdown) {
      return (
        <div className={styles.custom_label} onClick={handleIsExpanded}>
          {label}
          {isExpanded ? (
            <Image src={uppercarrot} alt="" width={24} style={{ marginLeft: 6 }} />
          ) : (
            <Image src={downcarrot} alt="" width={24} style={{ marginLeft: 6 }} />
          )}
        </div>
      );
    }

    return (
      <div className={styles.custom_label}>
        {label} {infoIcon && <IoIosInformationCircleOutline size={20} onClick={onInfoIconClick} />}
      </div>
    );
  }, [dropdown, isExpanded, label]);

  return (
    <>
      <div
        className={styles.custom_field_container}
        style={{
          borderBottom: borderEnd ? "1px solid #E9E9E9" : "",
          borderTop: borderStart ? "1px solid #E9E9E9" : "",
        }}
      >
        {renderDropdown()}
        <p className={styles.custom_value}>
          {isCurrency ? `₹${formatCurrency(+value)}` : value}
        </p>
      </div>
      {isExpanded && dropdownContent}
    </>
  );
};

export default CustomField;

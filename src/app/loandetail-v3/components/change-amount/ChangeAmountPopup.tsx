import React, { useEffect, useState } from "react";
import { formatCurrency } from "../../../store/middleware/currencyFormatter";
import { numberToWords } from "../../../utils/helperFunctions";
import styles from "./changeAmount.module.scss";
import { setSliderDisabled, setAmount, setLoaded } from "../../loanDetails.slice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store/store";
import { pushCleverTapEvent } from "@/app/utils/cleverTapClient";

interface ChangeAmountProps {
  getAllLoanDetails: (selectedAmount?: number, firstTime?: boolean) => void;
  handleChangeAmtPopup: (callEvent?: boolean) => void;
  eventsData: any;
}

const ChangeAmountPopup = ({getAllLoanDetails, handleChangeAmtPopup, eventsData}: ChangeAmountProps) => {
  const dispatch = useDispatch();
  const { sliderDisabled, amount, breakpoints } = useSelector(
    (state: RootState) => state.loanDetailV3
  );
  const [tempAmount, setTempAmount] = useState<number>(amount);

  const [percentage, setPercentage] = useState<number>((amount / breakpoints[breakpoints.length - 1]) * 100);

  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(event.target.value);
    const max = breakpoints.length - 1;
    const tempPercentage = (value / max) * 100;
    setPercentage(tempPercentage);
    setTempAmount(breakpoints[value]);
  };
  const handleChooseAmount = async() => {
    const event_property = {
      CTA: "Choose Amount",
      "page name": "Limit Slider",
      "Product category": localStorage.getItem("product_code"),
      Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
      "Selected Amount": tempAmount,
    }
    pushCleverTapEvent({eventName: "Button Clicked", eventData: event_property});
    handleChangeAmtPopup(false);
    dispatch(setLoaded(true));
    await getAllLoanDetails(tempAmount, true);
  };

  useEffect(() => {
    const event_name = "Screen View";
    const event_property = {
      "Screen Name": "Limit Slider",
      "Product category": eventsData?.product_code,
      Source: eventsData?.source,
      Segment: eventsData?.isNonSTP ? "Non-STP" : "STP",
    };
    pushCleverTapEvent({eventName: event_name, eventData: event_property});
  }, []);

  return (
    <div className={styles.change_amount_container}>
      <p className={styles.disbursal_amount_label}>Disbursal Amount</p>
      <p className={styles.disbursal_currency}>₹{formatCurrency(tempAmount)}</p>
      <p className={styles.disbursal_currency_words}>{numberToWords(tempAmount)}</p>

      <div className={styles.loanAmountContainer}>
        <div
          style={
            sliderDisabled
              ? {
                filter: "saturate(0)",
                opacity: 0.4,
                pointerEvents: "none",
              }
              : undefined
          }
        >
          <div className={styles.dots}>
            {Array.from({ length: 8 }).map((_: any, index: number) => {
              return (
                <div
                  key={index}
                  className={`${styles.dot} ${index <= breakpoints.indexOf(tempAmount)
                      ? styles.selected
                      : ""
                    }`}
                ></div>
              );
            })}
          </div>
          <input
            type="range"
            className={styles.slider}
            style={{
              background: `linear-gradient(to right, var(--black-color) ${percentage}%, #ddd ${percentage}%)`,
            }}
            min="0"
            max={breakpoints.length - 1}
            step="1"
            value={breakpoints.indexOf(tempAmount)}
            onChange={handleSliderChange}
          />
          <div className={styles.breakpoints}>
            <span>₹{formatCurrency(breakpoints[0])}</span>
            <span>₹{formatCurrency(breakpoints[breakpoints.length - 1])}</span>
          </div>
        </div>
      </div>

      <button className={`btn btn-primary`} onClick={handleChooseAmount}>
        Choose Amount
      </button>
    </div>
  );
};

export default ChangeAmountPopup;

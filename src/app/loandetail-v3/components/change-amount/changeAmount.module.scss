@import "../../../scss/variable.scss";

.change_amount_container{
    text-align: left;
    margin-top: 24px;

    .disbursal_amount_label{
        font-size: 14px;
        font-weight: 500;
    }

    .disbursal_currency{
        font-size: 36px !important;
        font-weight: 700;
        margin: 14px 0 10px 0;
    }

    .disbursal_currency_words{
        font-size: 12px;
        font-weight: 500;
        color: #bbbbbb;
    }

    .loanAmountContainer {
        margin: 10px 0 20px 0;
        .dots {
            display: flex;
            top: 14px;
            position: relative;
            justify-content: space-around;
        
            .dot {
                content: '';
                width: 5px;
                height: 5px;
                border-radius: 50%;
                background-color: #8d8d8d;
        
                &.selected {
                    background-color: #ffffff;
                }
            }
        }

        .slider {
            appearance: none;
            width: 100%;
            height: 12px;
            background: #dddddd;
            border-radius: 9px;
            outline: none;
            transition: background 0.3s ease;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            cursor: pointer;
            position: relative;
            z-index: 2;
            background-position: center;
            background-size: cover;
            background-image: url('../../../images/slider-thumb.svg');
        }
        
        .slider::-moz-range-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            cursor: pointer;
            position: relative;
            z-index: 2;
            background-position: center;
            background-size: cover;
            background-image: url('../../../images/slider-thumb.svg');
        }

        .breakpoints {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            margin-top: 14px;
            color: #000;
        
            span {
                position: relative;
            }
        }
    }
}
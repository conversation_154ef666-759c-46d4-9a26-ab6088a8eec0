import React, { useEffect, useState } from "react";
import styles from "./popup.module.scss";
import HassleFreeImg from "../../../images/hasslefree.png";
import Image from "next/image";
import AccidentDeathImg from "../../../images/accidentdeath.png";
import SliderCheckbox from "@/app/components/slidercheckbox";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store/store";
import { setIsLppChecked, setLoaded } from "../../loanDetails.slice";
import ShieldImg from "../../../images/shield.png"
import WarningSection from "./WarningSection";
import CtEvents from "../../../utils/Ctevents";

interface LoanProtectionProps {
  handleLoanProtectionPopup: () => void;
}

const LoanProtectionPopup = ({handleLoanProtectionPopup}: LoanProtectionProps) => {
  const dispatch = useDispatch();
  const { isLppChecked } = useSelector(
    (state: RootState) => state.loanDetailV3
  );
  const [ctData, setCtdata] = useState({});

  const onSkipClick = () => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem("eventsdata") || "");
    const event_name = "Popup Clicked";
    const event_property = {
      CTA: "Skip",
      "popup type": "Credit Shield",
      Source: cteventsfromstorage?.source,
      "Page name": "LOC_Offer Details",
    }
    setCtdata({ event_name, event_property });
    if(isLppChecked){
      handleLoanProtectionPopup();
      dispatch(setLoaded(true));
      dispatch(setIsLppChecked(false));
    }else{
      handleLoanProtectionPopup();
    }
  }

  const onProtectMeClick = () => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem("eventsdata") || "");
    const event_name = "Popup Clicked";
    const event_property = {
      CTA: "Protect Me",
      "popup type": "Credit Shield",
      Source: cteventsfromstorage?.source,
      "Page name": "LOC_Offer Details",
    }
    setCtdata({ event_name, event_property });
    if(!isLppChecked){
      handleLoanProtectionPopup();
      dispatch(setLoaded(true));
      dispatch(setIsLppChecked(true));
    }else{
      handleLoanProtectionPopup();
    }
  }

  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem("eventsdata") || "");
    const event_name = "Popup Shown";
    const event_property = {
      "Page name": "LOC_Offer Details",
      "popup type": "Credit Shield",
      Segment: cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP",
      Source: cteventsfromstorage?.source,
    }
    setCtdata({ event_name, event_property });
  }, []);

  return (
    <div className={styles.loan_protection_popup_container}>
      <div className={styles.inner_container}>
        <Image
          src={HassleFreeImg}
          alt="haslefree"
          id="credit-score-img"
          width={48}
          height={48}
        />
        <div className={styles.text_box}>
          <h3>Hassle-free & Paperless</h3>
          <p>Get insured instantly with premium deducted from loan</p>
        </div>
      </div>
      <div className={styles.inner_container}>
        <Image
          src={AccidentDeathImg}
          alt="accident-death-img"
          id="credit-score-img"
          width={48}
          height={48}
        />
        <div className={styles.text_box}>
          <h3>Accidental death benefit</h3>
          <p>Stashfin loan amount repaid in case of accidental death</p>
        </div>
      </div>

      <div
        style={{
          display: "flex",
          justifyContent: "center",
          columnGap: 10,
          alignItems: "center",
          margin: "20px 0 30px 0",
        }}
      >
        <Image
          src={ShieldImg}
          alt="credit-meter"
          id="credit-meter-img"
          width={22}
        />
        <p style={{ fontSize: 14, fontWeight: 500 }}>Get my Credit Report</p>
        <SliderCheckbox
          initialChecked={isLppChecked}
          onChange={() => dispatch(setIsLppChecked(!isLppChecked))}
        />
      </div>

      {!isLppChecked ? (
        <WarningSection
          title=""
          subTitle="Skipping out may leave your family responsible for the full loan in unforeseen events."
          onSkipClick={onSkipClick}
          onClick={onProtectMeClick}
          btnText="Protect Me"
        />
      ) : (
        <button className="btn btn-primary" onClick={onProtectMeClick}>
          Protect Me
        </button>
      )}

      <CtEvents data={ctData} />
    </div>
  );
};

export default LoanProtectionPopup;

import Image from "next/image";
import React from "react";
import CautionImg from "../../../images/caution.png";
import styles from "./popup.module.scss";

interface WarningSectionProps {
    title: string;
    subTitle: string;
    onSkipClick: () => void;
    onClick: () => void;
    btnText: string;
}

const WarningSection = ({title, subTitle, onSkipClick, onClick, btnText}: WarningSectionProps) => {
  return (
    <div className={styles.warning_section_container}>
      <div className={styles.warning_inner_container}>
        <Image
          src={CautionImg}
          alt="credit score"
          id="credit-score-img"
          width={21}
          height={19}
          style={{ marginTop: 4 }}
        />
        <div className={styles.text_box}>
          <strong>{title}</strong>
          <p>{subTitle}</p>
        </div>
      </div>
      <div className={styles.btn_container}>
        <button className={styles.skip} onClick={onSkipClick}>
          Skip
        </button>
        <button onClick={onClick}>{btnText}</button>
      </div>
    </div>
  );
};

export default WarningSection;

import React, { useEffect, useState } from "react";
import CreditScoreImg from "../../../images/creditscore.png";
import AccountInfoIcon from "../../../images/accountinfoicon.png";
import EnquiryInfo from "../../../images/enquiryinfo.png";
import CreditMeterImg from "../../../images/creditmeter.png";
import Image from "next/image";
import styles from "./popup.module.scss";
import SliderCheckbox from "../../../components/slidercheckbox";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store/store";
import { setIsCibilChecked, setLoaded } from "../../loanDetails.slice";
import WarningSection from "./WarningSection";
import CtEvents from "../../../utils/Ctevents";
interface CibilPopupProps{
  handleCibilPopup: () => void;
}

const CibilPopup = ({handleCibilPopup}: CibilPopupProps) => {
  const dispatch = useDispatch();
  const { isCibilChecked } = useSelector(
    (state: RootState) => state.loanDetailV3
  );
  const [isChecked, setIsChecked] = useState(isCibilChecked);
  const [ctData, setCtdata] = useState({});

  const onSkipClick = () => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem("eventsdata") || "");
    const event_name = "Popup Clicked";
    const event_property = {
      CTA: "Skip",
      "popup type": "Credit Report",
      Source: cteventsfromstorage?.source,
      "Page name": "LOC_Offer Details",
    }
    setTimeout(() => {
      setCtdata({ event_name, event_property });
    }, 50);
    if(isCibilChecked){
      handleCibilPopup();
      dispatch(setLoaded(true));
      dispatch(setIsCibilChecked(false));
    }else{
      handleCibilPopup();
    }
  }

  const onGetReportClick = () => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem("eventsdata") || "");
    const event_name = "Popup Clicked";
    const event_property = {
      CTA: "Get Report",
      "popup type": "Credit Report",
      Source: cteventsfromstorage?.source,
      "Page name": "LOC_Offer Details",
    }
    setCtdata({ event_name, event_property });
    if(!isCibilChecked){
      handleCibilPopup();
      dispatch(setLoaded(true));
      dispatch(setIsCibilChecked(true));
    }else{
      handleCibilPopup();
    }
  }

  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem("eventsdata") || "");
    const event_name = "Popup Shown";
    const event_property = {
      "Page name": "LOC_Offer Details",
      "popup type": "Cibil Report",
      Segment: cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP",
      Source: cteventsfromstorage?.source,
    }
    setCtdata({ event_name, event_property });
  }, []);

  return (
    <div className={styles.cibil_popup_container}>
      <div className={styles.inner_container}>
        <Image
          src={CreditScoreImg}
          alt="credit score"
          id="credit-score-img"
          width={48}
          height={48}
        />
        <div className={styles.text_box}>
          <h3>Credit Score and Summary</h3>
          <p>
            An overview of your credit health, creditworthiness and credit
            utilization.
          </p>
        </div>
      </div>
      <div className={styles.inner_container}>
        <Image
          src={AccountInfoIcon}
          alt="account info"
          id="account-info-img"
          width={48}
          height={48}
        />
        <div className={styles.text_box}>
          <h3>Credit Score and Summary</h3>
          <p>Displays previous lenders and loan-related details with them.</p>
        </div>
      </div>
      <div className={styles.inner_container}>
        <Image
          src={EnquiryInfo}
          alt="credit score"
          id="enquiry-img"
          width={48}
          height={48}
        />
        <div className={styles.text_box}>
          <h3>Credit Score and Summary</h3>
          <p>Provide details of all the enquiries made by the lenders.</p>
        </div>
      </div>

      <div
        style={{
          display: "flex",
          justifyContent: "center",
          columnGap: 10,
          alignItems: "center",
          margin: "20px 0 30px 0",
        }}
      >
        <Image
          src={CreditMeterImg}
          alt="credit-meter"
          id="credit-meter-img"
          width={22}
        />
        <p style={{ fontSize: 14, fontWeight: 500 }}>Get my Credit Report</p>
        <SliderCheckbox
          initialChecked={isChecked}
          onChange={() => setIsChecked(!isChecked)}
        />
      </div>

      {!isChecked ? (
        <WarningSection
          title="Grow financially"
          subTitle="See how to improve your score and get better loan rates"
          onSkipClick={onSkipClick}
          onClick={onGetReportClick}
          btnText="Get Report"
        />
      ) : (
        <button className="btn btn-primary" onClick={onGetReportClick}>
          Get Report
        </button>
      )}

    <CtEvents data={ctData} />
    </div>
  );
};

export default CibilPopup;

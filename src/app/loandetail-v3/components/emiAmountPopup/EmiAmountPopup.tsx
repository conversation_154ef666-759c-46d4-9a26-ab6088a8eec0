"use client"
import styles from './emi.module.scss'
import React from "react"
import { useSelector } from "react-redux"
import { RootState } from "../../../store/store"
import { formatCurrency } from '../../../store/middleware/currencyFormatter'

function EmiPopup() {
    const { loanWithdrawData } = useSelector((state: RootState) => state.loanDetailV3);

    return (
        <div>
            <div className={styles.loanamntbox}>
                <div className={styles.loanamtrow}>
                    <div className={styles.amounttxt}>
                        {loanWithdrawData?.total_payable_amount_l}
                    </div>
                    <div className={styles.amount}>
                        ₹{formatCurrency(Number(loanWithdrawData?.net_amount_payable))}
                    </div>
                </div>
                <div className={styles.loanamtrow}>
                    <div className={styles.amounttxt}>
                        {loanWithdrawData?.loan_amount_l}
                    </div>
                    <div className={styles.amount}>
                        ₹{formatCurrency(Number(loanWithdrawData?.approved_amount))}
                    </div>
                </div>

                <div className={styles.loanamtrow}>
                    <div className={styles.amounttxt}>
                        {loanWithdrawData?.effective_roi_l}
                    </div>
                    <div className={styles.amount}>
                        {loanWithdrawData?.effective_annual_roi}%
                    </div>
                </div>
                <div className={styles.loanamtrow}>
                    <div className={styles.amounttxt}>
                        {loanWithdrawData?.apr_title}
                    </div>
                    <div className={styles.amount}>
                        {loanWithdrawData?.apr_value}
                    </div>
                </div>
                <div className={styles.loanamtrow} style={{borderBottom: "none"}}>
                    <div className={styles.amounttxt}>
                        {loanWithdrawData?.total_interest_pay_l}
                    </div>
                    <div className={styles.amount}>
                        ₹{formatCurrency(Number(loanWithdrawData?.total_interest_pay))}
                    </div>
                </div>
            </div>
            <div className={styles.btoomtxt}>
                Please refer to <a href={loanWithdrawData?.financing_documents} target='_blank'>Financing documents</a> for details of applicable interest and charges.
            </div>
        </div>
    )
}

export default EmiPopup
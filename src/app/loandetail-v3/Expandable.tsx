import { useCallback, useState } from "react";
import { FaAngleDown, FaAngleUp } from "react-icons/fa6";
import { IoIosInformationCircleOutline } from "react-icons/io";
import { useDispatch, useSelector } from "react-redux";
import BottomPopup from "../components/popups/BottomPopup";
import { formatCurrency } from "../store/middleware/currencyFormatter";
import { RootState } from "../store/store";
import CustomField from "./components/custom-field/CustomField";
import CibilPopup from "./components/pop-up/CibilPopup";
import LoanProtectionPopup from "./components/pop-up/LoanProtectionPopup";
import styles from "./loandetail.module.scss";
import { toggleEmiModal } from "./loanDetails.slice";
import Image from "next/image"
import info from '../images/info.png'

const Expandable = ({commonCtEvent}: {commonCtEvent: (cta: string) => void}) => {
  const { loanWithdrawData, isCibilChecked, isLppChecked, emiModal } = useSelector(
    (state: RootState) => state.loanDetailV3
  );
  const dispatch = useDispatch();
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [showCibilPopup, setShowCibilPopup] = useState<boolean>(false);
  const [showLppPopup, setShowLppPopup] = useState<boolean>(false);

  // FEE and CHARGES DROPDOWN ----------
  const feeAndChargesDropdown = useCallback(() => {
    return (
      <div className={styles.fee_charges_container}>
        <div className={styles.fee_charges_field}>
          <p>{loanWithdrawData?.trans_fee_plus_gst_l}</p>
          <p>₹{formatCurrency(loanWithdrawData?.trans_fee_plus_gst)}</p>
        </div>
        <div className={styles.fee_charges_field} style={{ margin: "8px 0" }}>
          <p>{loanWithdrawData?.processing_fee_plus_gst_l}</p>
          <p>₹{formatCurrency(loanWithdrawData?.processing_fee_plus_gst)}</p>
        </div>
        <div
          className={styles.fee_charges_field}
          style={{ marginBottom: "8px" }}
        >
          <p>{loanWithdrawData?.upfront_interest_l}</p>
          <p>₹{formatCurrency(loanWithdrawData?.upfront_interest)}</p>
        </div>
        <div style={{ display: "flex", columnGap: 8 }}>
          <IoIosInformationCircleOutline size={20} style={{ width: 36 }} />
          <p style={{ fontSize: 10 }}>
            {loanWithdrawData?.upfront_interest_description}
          </p>
        </div>
      </div>
    );
  }, [loanWithdrawData]);
  // -----------XXXXX-------------

  // ADD ON SEGMENT
  const handleCibilPopup = () => {
    if(!showCibilPopup){
      commonCtEvent("Credit Report Info");
    }
    setShowCibilPopup(!showCibilPopup);
  };

  const handleLoanProtectionPopup = () => {
    if(!showLppPopup){
      commonCtEvent("Credit Shield Info");
    }
    setShowLppPopup(!showLppPopup);
  };

  const addOnDropdown = useCallback(() => {
    return (
      <div className={styles.addOn_container}>
        <div className={styles.addOn_field}>
          <div style={{ display: "flex", alignItems: "center", columnGap: 8 }}>
            <p>CIBIL Report</p>{" "}
            <Image src={info} alt=""
              width={16}
              onClick={handleCibilPopup}
            />
          </div>
          <div style={{ display: "flex", alignItems: "center", columnGap: 8 }}>
            {loanWithdrawData?.credit_report_actual_fees > 0 && (
              <p style={{color: "#909090", textDecoration: "line-through"}}>₹{formatCurrency(loanWithdrawData?.credit_report_actual_fees)}</p>
            )}
            <p>₹{formatCurrency(loanWithdrawData?.credit_report_fees)}</p>
          </div>
        </div>
        <div className={styles.addOn_field} style={{ margin: "8px 0" }}>
          <div style={{ display: "flex", alignItems: "center", columnGap: 8 }}>
            <p>Loan Protection Plan</p>{" "}
            <Image src={info} alt=""
              width={16}
              onClick={handleLoanProtectionPopup}
            />
          </div>
          <p>₹{formatCurrency(loanWithdrawData?.max_amount)}</p>
        </div>
      </div>
    );
  }, [loanWithdrawData]);

  const calculateAddOnValue = () => {
    if (isCibilChecked && isLppChecked)
      return (
        loanWithdrawData?.credit_report_fees + loanWithdrawData?.max_amount
      );
    if (isCibilChecked) return loanWithdrawData?.credit_report_fees;
    if (isLppChecked) return loanWithdrawData?.max_amount;
    return 0;
  };

  // --------XXXX----------

  // HANDLE EXPANDABLE SECTION
  const handleExpandableSection = () => {
    if(isExpanded){
      commonCtEvent("Show less Main Widget");
    }else{
      commonCtEvent("Show more Main Wdget");
    }
    setIsExpanded(!isExpanded);
  }

  // EMI MODAL TOGGLE
  const handleEmiModal = () => {
    if(!emiModal){
      commonCtEvent("EMI amount info");
    }
    dispatch(toggleEmiModal(true));
  }

  return (
    <div className={styles.loan_detail_body} style={{ marginTop: 16 }}>
      <div className={styles.temsbox}>
        {loanWithdrawData?.emi_card_description && (
          <span>
            {!isExpanded &&
              <>
                {loanWithdrawData?.emi_card_description.replace(/(\d+)\.00(?=%)/g, "$1")}
              </>
            }
            {isExpanded &&
              <>
                Loan Terms
              </>
            }
          </span>
        )}
      </div>

      <div className={styles.collapsible_container}>
        <CustomField
          label={loanWithdrawData?.loan_amount_l}
          value={loanWithdrawData?.approved_amount}
          isCurrency
          borderEnd
        />

        {isExpanded && (
          <>
            <CustomField
              label={loanWithdrawData?.fee_and_charges_l}
              value={loanWithdrawData?.fee_and_charges}
              isCurrency
              dropdown
              dropdownContent={feeAndChargesDropdown()}
              commonCtEvent={commonCtEvent}
              cta="Fee"
            />
            <CustomField
              label={loanWithdrawData?.add_on_l}
              value={calculateAddOnValue()}
              isCurrency
              dropdown
              dropdownContent={addOnDropdown()}
              commonCtEvent={commonCtEvent}
              cta="Add Ons"
            />
            <CustomField
              label="Amount you will get"
              value={loanWithdrawData?.final_disbursal_amount}
              isCurrency
              borderEnd
            />
            <CustomField
              label={loanWithdrawData?.first_emi_date_l}
              value={loanWithdrawData?.first_emi_date}
              borderEnd
            />
            <CustomField
              label={loanWithdrawData?.number_of_emi_l}
              value={loanWithdrawData?.tenure}
              borderEnd
            />
          </>
        )}

        <CustomField
          label={loanWithdrawData?.emi_amount_l}
          value={loanWithdrawData?.emi_amount}
          isCurrency
          infoIcon
          onInfoIconClick={handleEmiModal}
        />

        {/* COLLAPSE-ICON */}
        <p
          onClick={handleExpandableSection}
          className={styles.collapse_icon}
        >
          {isExpanded ? <FaAngleUp /> : <FaAngleDown />}
        </p>

        {/* CIBIL POPUP */}
        <BottomPopup
          isOpen={showCibilPopup}
          onClose={handleCibilPopup}
          title="What is credit report ?"
        >
          <CibilPopup handleCibilPopup={handleCibilPopup} />
        </BottomPopup>

        {/* LOAN PROTECTION POPUP */}
        <BottomPopup
          isOpen={showLppPopup}
          onClose={handleLoanProtectionPopup}
          title="Why is this Protection important ?"
        >
          <LoanProtectionPopup handleLoanProtectionPopup={handleLoanProtectionPopup} />
        </BottomPopup>
      </div>
    </div>
  );
};

export default Expandable;

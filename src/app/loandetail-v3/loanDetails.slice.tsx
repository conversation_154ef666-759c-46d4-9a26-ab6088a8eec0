import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface LoanDetailsTypes {
  bill_date: string;
  can_reload_card: boolean;
  loc_disable: boolean;
  loc_limit: number;
  max_tenure: number;
  min_tenure: number;
  minimum_request_amount: number;
  pending_used_loc: number;
  rate_of_interest: number;
  remaining_loc: number;
  request_amount_increment_step: number;
  request_max_percentage: number;
  sanction_letter: string;
  used_loc: number;
}
export interface LoanState {
  pageerror: string | null;
  pagesuccess: string | null;
  loanWithdrawData: any;
  isChecked: boolean;
  emiModal: boolean;
  customerBankDetails: any;
  // ------
  showChangeAmtPopup: boolean;
  sliderDisabled: boolean;
  amount: number;
  breakpoints: number[];
  // ------
  isCibilChecked: boolean;
  isLppChecked: boolean;
  // ------
  loanDetails: LoanDetailsTypes;
  loader: boolean;
  loaded: boolean;
  showFcpBanner: boolean;
}

const initialState: LoanState = {
  pageerror: null,
  pagesuccess: null,
  loanWithdrawData: null,
  isChecked: false,
  emiModal: false,
  customerBankDetails: null,
  // -------
  showChangeAmtPopup: false,
  sliderDisabled: false,
  amount: 0,
  breakpoints: [],
  // -------
  isCibilChecked: true,
  isLppChecked: true,
  loanDetails: {} as LoanDetailsTypes,
  loader: true,
  loaded: false,
  showFcpBanner: true,
};

const loanDetailsSlice = createSlice({
  name: "loandetailsV3",
  initialState,
  reducers: {
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setLoanWithdrawData: (state, action: PayloadAction<string>) => {
      state.loanWithdrawData = action.payload;
    },
    setIsChecked: (state, action: PayloadAction<boolean>) => {
      state.isChecked = action.payload;
    },
    toggleEmiModal: (state, action: PayloadAction<boolean>) => {
      state.emiModal = action.payload;
    },
    setCustomerBankDetails: (state, action: PayloadAction<boolean>) => {
      state.customerBankDetails = action.payload;
    },
    setShowChangeAmtPopup: (state, action: PayloadAction<boolean>) => {
      state.showChangeAmtPopup = action.payload;
    },
    setSliderDisabled: (state, action: PayloadAction<boolean>) => {
      state.sliderDisabled = action.payload;
    },
    setAmount: (state, action: PayloadAction<number>) => {
      state.amount = action.payload;
    },
    setBreakpoints: (state, action: PayloadAction<number[]>) => {
      state.breakpoints = action.payload;
    },
    setIsCibilChecked: (state, action: PayloadAction<boolean>) => {
      state.isCibilChecked = action.payload;
    },
    setIsLppChecked: (state, action: PayloadAction<boolean>) => {
      state.isLppChecked = action.payload;
    },
    setLoanDetails: (state, action: PayloadAction<LoanDetailsTypes>) => {
      state.loanDetails = action.payload;
    },
    setLoader: (state, action: PayloadAction<boolean>) => {
      state.loader = action.payload;
    },
    setLoaded: (state, action: PayloadAction<boolean>) => {
      state.loaded = action.payload;
    },
    setShowFcpBanner: (state, action: PayloadAction<boolean>) => {
      state.showFcpBanner = action.payload;
    },
  },
});

export const {
  setPageError,
  setPageSuccess,
  setLoanWithdrawData,
  setIsChecked,
  toggleEmiModal,
  setCustomerBankDetails,
  setShowChangeAmtPopup,
  setAmount,
  setSliderDisabled,
  setIsCibilChecked,
  setIsLppChecked,
  setLoanDetails,
  setBreakpoints,
  setLoader,
  setLoaded,
  setShowFcpBanner,
} = loanDetailsSlice.actions;

export default loanDetailsSlice.reducer;

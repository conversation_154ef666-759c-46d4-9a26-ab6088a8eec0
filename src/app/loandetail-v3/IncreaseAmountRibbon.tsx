import Image from "next/image";
import { FaAngleRight } from "react-icons/fa6";
import Rocket_Img from "../images/rocket.webp";
import { ApiResponse } from "../store/interface/apiInterface";
import { generateChecksum } from "../store/middleware/currencyFormatter";
import { apiRequest } from "../utils/api";
import { ENDPOINTS } from "../utils/endpoints";
import styles from "./loandetail.module.scss";

const IncreaseAmountRibbon = ({commonCtEvent}: {commonCtEvent: (cta: string) => void}) => {
  const startAAJourney = async() => {
    // const body: Record<string, any> = {
    //   mode: "enhanceLOC",
    // }
    // body.checksum = generateChecksum(body);
    // const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.increaseLimit, body);
    commonCtEvent("Increase amount");
  }
  return (
    <div className={styles.increase_amount_ribbon} onClick={startAAJourney}>
      <p>
        <Image src={Rocket_Img} alt="rocket" width={22} style={{marginRight: 10}} /> Increase amount up to ₹5 lakh
      </p>
      
      <FaAngleRight size={16} style={{marginRight: 20}} />
    </div>
  );
};

export default IncreaseAmountRibbon;

.notifySuccessBatch{
    position: absolute;
    left: 16px;
    top: 10px;
    width: 100%;
    max-width: calc(100% - 32px);
    z-index: 2;
}

.header {
    background: #F5F5F5;
    padding-left: 16px;
    padding-right: 16px;

    h2 {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 24px;
        line-height: 36px;
        color: #000;
        margin-top: 20px;
    }

    .top_section_container {
        display: flex;
        justify-content: space-between;
        padding-bottom: 10px;
        .disbursal_amount_label {
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 16px;
            line-height: 25px;
            color: var(--black);
            padding-right: 30px;
            margin-bottom: 10px;
        }

        .disbursal_currency {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            font-size: 36px;
            line-height: 35px;
            color: #000;
        }

        .currency_in_words{
            font-size: 12px;
            font-weight: 500;
            color: #BBBBBB;
            text-transform: capitalize;
            margin-top: 4px;
        }

        .change_amount {
            font-size: 14px;
            font-weight: 500;
            color: #0064E0;
            margin-top: 8px;
        }
    }

    header {
        min-height: auto;
        background: transparent;
        padding: 0;
        margin-bottom: 0;
    }
}

.increase_amount_ribbon{
    background-color: #1f1f1f;
    color: #fff;
    font-size: 14;
    font-weight: 600;
    padding: 4px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    p {
        font-size: 14px;
        font-weight: 600;
        font-family: var(--font-mona-sans);
    }
}

.loan_detail_body{
    padding: 0 16px;

    .temsbox {
        border-radius: 8px;
        padding: 12px;
        padding-bottom: 16px;
        font-family: var(--font-mona-sans);
        font-size: 14px;
        background: linear-gradient(180deg, rgba(255, 236, 231, 1) 60%, rgba(255, 255, 255, 1) 100%);

        span {
            font-weight: 600;
        }
    }

    .collapsible_container{
        padding: 8px 16px;
        border: 1px solid #E9E9E9;
        border-radius: 8px;
        background: #fff;
        margin-top: -10px;
        position: relative;

        .custom_field_container{
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            .custom_label{
                font-size: 14px;
            }
            .custom_value{
                font-size: 14px;
                font-weight: 600;
            }
        }

        .fee_charges_container{
            background-color: #F5F5F5;
            padding: 12px;
            border-radius: 8px;
            font-size: 12px;
            .fee_charges_field{
                display: flex;
                justify-content: space-between;
                p {
                    font-weight: 600;
                }
            }
        }

        .addOn_container{
            background-color: #F5F5F5;
            padding: 12px;
            border-radius: 8px;
            font-size: 12px;
            .addOn_field{
                display: flex;
                justify-content: space-between;
                p {
                    font-weight: 600;
                }
            }
        }

        .collapse_icon{
            border: 1px solid #E9E9E9;
            display: flex;
            position: absolute;
            padding: 9px;
            background-color: #FFF;
            border-radius: 50%;
            left: 50%;
            transform: translateX(-50%);
            bottom: -18px;
        }
    }
}

.bankdetail_container{
    padding: 0 16px;
    margin: 40px 0;

    .bankdetail {
        padding: 16px;
        border: 1px solid #E9E9E9;
        border-radius: 8px;
        background: #fff;
    
        >p {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #000;
        }
    
        .bankbox {
            display: flex;
            justify-content: space-between;
            margin-top: 13px;
    
            >div {
    
                p,
                span {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 12px;
                    line-height: 20px;
                    color: #000;
                }
    
                span {
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 24px;
                }
            }
    
            img {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                &.validcheck {
                    width: 16px;
                    height: 16px;
                    margin-left: 8px;
                }
            }
        }
    }
}

.checkbox_container{
    margin-top: 13px;
    padding: 0 16px;
}
.icon {
    padding-top: 20px;
}

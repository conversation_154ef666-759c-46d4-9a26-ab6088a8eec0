"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './success.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import { useEffect, useState } from "react"
import upperCarrot from '../images/uppercarrot.png';
import downCarrot from '../images/downcarrot.png';
import React from "react"
import { useSelector } from "react-redux"
import { RootState } from "../store/store"

import { useRouter } from "next/navigation";
import CtEvents from "../utils/Ctevents"
import { navigateToPage } from "../utils/bridge"

function SuccessdPage() {
  const router = useRouter();

  useThemeColor('#D5FAE6')
  const { loanWithdrawData, customerBankDetails } = useSelector((state: RootState) => state.loandetails);
  const accordionData = [
    {
      "question": "What is the Free Credit Period offer?",
      "answer": "If you are eligible for the Free Credit Period offer, then every time you load your app, you can repay the same amount at 0% interest rate and no processing fees before/on your Bill Date. The Free Credit Period is applicable only till your Bill Date. If you are not able to pay your Bill Amount before or on your Bill Date, then the amount that you have loaded under the Free Credit Period will automatically get converted into your chosen EMIs and tenure that you have selected at the time of loading your app. You will be able to see your Bill Amount and Bill Date along with the EMI Date and EMI Amount in your application."
    },
    {
      "question": "Will my Bill Amount get auto debited from my account?",
      "answer": "Bill Amount will not be auto-debited from your account. You will have to pay your Bill Amount on the Stashfin App / Website before/ on your Bill Date."
    },
    {
      "question": "What will happen if I don't pay the Bill Amount before my Bill Date?",
      "answer": "If you are not able to pay your Bill Amount on or before your Bill Date, then the amount that you have transferred to your bank account will automatically get converted into EMIs and the tenure that you have selected before transferring the amount. While transferring the amount to yourself you will be able to see your Bill Amount and Bill Date along with the EMI Date and EMI Amount in your application. If the loan gets converted into EMIs then you will additionally be charged with the interest rate, processing fees, upfront Interest (if applicable), credit shield fees (if you have selected) and GST."
    },
    {
      "question": "How do I repay the loan?",
      "answer": "The loan can be repaid in the form of Equated Monthly Instalment (EMI) via NACH (National Automated Clearing House) mandates. You can even pay online on Stashfin app."
    },
    {
      "question": "How do I pay my EMI?",
      "answer": "If your NACH is registered and set up with Stashfin, you do not need to pay the EMI yourself. The EMI amount will get auto-debited from your registered bank account on the due date anytime between 10 am to 6 pm. Please maintain sufficient balance in the account. In case your EMI is due on a weekend or any other bank holiday, you need not worry. NACH services are operational on all 7 days of the week (including any bank / gazetted holiday). Alternatively, you can also make your EMI payment by logging into your account on the website and clicking on 'Make Payment' on the left-hand panel. You can also use the app to effortlessly make your EMI payment. If you wish to pay the EMI before the due date, please do the same 48 hours prior to the EMI due date. Please note: You will be charged a nominal fee of Rs. 20 plus GST when you make the payment on the website or the app."
    },
    {
      "question": "Will my EMI get auto-debited or will I need to pay myself?",
      "answer": "If your NACH is registered and set up with Stashfin, you will be notified by us through an SMS. In that case, you need not pay yourself. If your NACH has not yet been registered, please make the payment yourself by visiting www.stashfin.com or through the app at least 2 working days before the EMI due date."
    },
    {
      "question": "How can I foreclose my loan?",
      "answer": "You can foreclose your loan anytime by paying the principal and foreclosure charges through the 'Pay Now' section in the Stashfin App."
    }
  ];

  const [openAccordion, setOpenAccordion] = useState<string | null>(null);
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

  const toggleAccordion = (key: string) => {
    setOpenAccordion((prev) => (prev === key ? null : key));
  };
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code") || "n/a"

    const event_property = { "Screen Name": 'Congrats Screen', "Action": "", "Mandate Amount": "", "CTA Shown": "Back to Home", "Sanction Amount": loanWithdrawData?.final_disbursal_amount, "Product category": productcode, "Segment": cteventsfromstorage?.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source }
    setCtdata({ event_name, event_property })
  }, [])
  const returnBackToApp = () => {
    // debugger;
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Button Clicked'
    const productcode = localStorage.getItem("product_code") || "n/a"

    const event_property = { "CTA": 'Back to Home', "Page Shown": "Congrats Screen", "Banner": "LAMF", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non-STP" : "STP" }
    setCtdata({ event_name, event_property })
    window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
  };
  const commonCTa = (ctaname: string, bannername: string) => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Button Clicked'
    const event_property = { "Page shown": 'LOC_Rejected', "CTA": ctaname, "Banner": bannername }
    setCtdata({ event_name, event_property })
  }
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'Screen View'
    const productcode = localStorage.getItem("product_code") || "n/a"

    const event_property = { "Screen Name": 'Congrats Screen', "Action": "", "CTA Shown": "Back to Home", "Sanction Amount": loanWithdrawData?.final_disbursal_amount, "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non-STP" : "STP", "Source": cteventsfromstorage?.source, "CTA": "", "Page shown":"Congrats Screen"}
    setCtdata({ event_name, event_property })
  }, [])
  return (
    <div>
      <div className={styles.header}>
        <PageHeader title="" gating={false} />
        <div className={styles.headercont}>
          <div className={styles.select}>
            <p className={styles.congotxt}>Congratulations!</p>
            <p>Loan amount will be credited to your account shortly.</p>
          </div>
          <div className={styles.icon}></div>
        </div>
      </div>
      <div className={styles.successdetail}>
        <div>
          <div>Loan Amount</div>
          <div>₹{loanWithdrawData?.final_disbursal_amount}</div>
        </div>
        <div>
          <div>Bank IFSC</div>
          <div>{customerBankDetails?.[0]?.ifsc_code}</div>
        </div>
        <div>
          <div>
            Account Number
          </div>
          <div>{customerBankDetails?.[0]?.account_number}</div>
        </div>
      </div>
      <div className={styles.body}>
        {/* <div className={styles.whatucantitle}>
          What you may do now
        </div>
        <div className={styles.creditbuilderbox}>
          <div className={styles.title}>
            Refer & Earn
          </div>
          <h2>Invite friends and get ₹500 for every loan disbursal.</h2>
          <div className={styles.circularbtn}
            onClick={() => {
              if (process.env.NEXT_PUBLIC_LIVE_URL === window.location.origin) {
                window.location.href = 'https://www.stashfin.com/refer-and-earn/play-and-win'
              } else {
                window.location.href = 'https://dev.stashfin.com/refer-n-earn/home'
              }
            }}
          >Refer Now</div>
        </div>
        <div className={styles.otheroffer}>
          <div className={styles.yellow}>
            <div className={styles.title}>
              cash against mutual funds
            </div>
            <p>Use your mutual funds to get instant cash</p>
            <div className={styles.circularbtn} onClick={() => commonCTa('Check Now', "LAMF")}>Check now</div>
          </div>
          <div className={styles.green}>
            <div className={styles.title}>
              credit<br />
              report
            </div>
            <p>Check your Credit score for FREE</p>
            <div className={styles.circularbtn} onClick={() => commonCTa('Check Now', "CHR")}>Check now</div>
          </div>
        </div> */}
        <div className={styles.faqsparent}>
          <div className={styles.faqstitle}>
            Frequently Asked Questions (FAQs)
          </div>
          <div className={styles.accordion}>
            {accordionData.map((item, index) => {
              const itemKey = `${index}`;
              const isOpen = openAccordion === itemKey;

              return (
                <div key={itemKey} className={styles.accordion}>
                  <div
                    className={isOpen ? styles.accordionHeaderActive : styles.accordionHeader}
                    onClick={() => toggleAccordion(itemKey)}
                  >
                    <h2>
                      {item.question}
                      {isOpen ? (
                        <Image src={upperCarrot} alt="" />
                      ) : (
                        <Image src={downCarrot} alt="" />
                      )}
                    </h2>
                  </div>
                  {isOpen && (
                    <div className={styles.accordionContent}>
                      <div className={styles.faqItem}>
                        <p className={styles.faqAnswer}>{item.answer}</p>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
        <div className="bottom-footer p-0" style={{ padding: '10px 0px' }}>
          <div className={`footerbottombtn ${styles.footerbottombtn}`} onClick={() => returnBackToApp()}>Back to Home</div>
        </div>
      </div>
      <CtEvents data={ctData} />

    </div>
  )
}


export default SuccessdPage

"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams, useRouter } from "next/navigation";
import { useFormik } from "formik";
import * as Yup from "yup";
import styles from "./verify-salary-bank.module.scss";
import dropdown from "../../../public/images/dropdown.png";
import userFrame from "../../../public/images/userFrame.png";
import shield from "../images/shield-icon.svg";
import "../scss/form.scss";
import { apiRequest } from "../utils/api";
import { ENDPOINTS } from "../utils/endpoints";
import { setPageError } from "../register/register.slice";
import Image from "next/image";
import BankImage from "../components/BankImage";
import ToastMessage from "../components/ToastMessage/ToastMessage";
import LoadingComp from "../component/loader";
import { setAccountNumber, setIfscCode } from "../store/features/bankSlice";
import CtEvents from "../utils/Ctevents"; // Added import

const PageWrapper = dynamic(() => import("../components/PageWrapper"), {
  ssr: false,
});
const PageHeader = dynamic(() => import("../components/PageHeader"), {
  ssr: false,
});
const BottomPopup = dynamic(() => import("../components/popups/BottomPopup"), {
  ssr: false,
});

interface Bank {
  id: string;
  name: string;
  is_popular: boolean;
}

interface ApiResponse {
  mainData?: {
    success?: boolean | string;
    error_message?: string;
    data?: Bank[];
  } | null;
}

export default function VerifySalaryBank() {
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const router = useRouter();
  const initialBankName = searchParams.get("bankName") || "";
  const initialBankId = searchParams.get("bankId") || "";

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [loaded, setLoaded] = useState<boolean>(true);
  const [fullName, setFullName] = useState<string>("");

  const { accountNumber, ifscCode, isChecked } = useSelector(
    (state: any) => state.bank
  );
  const { pageerror, pagesuccess } = useSelector(
    (state: any) => state.register
  );

  const [allBanks, setAllBanks] = useState<Bank[]>([]);

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBankId, setSelectedBankId] = useState<string | null>(
    initialBankId
  );
  const [selectedBankName, setSelectedBankName] =
    useState<string>(initialBankName);

  // Added state for CT events
  const [ctData, setCtdata] = useState<{
    event_name: string;
    event_property: Record<string, any>;
  } | null>(null);

  const filteredBanks = allBanks.filter((bank) =>
    bank.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Fetch personal details to set KYC name
  useEffect(() => {
    const getPersonalDetails = async () => {
      try {
        const response = await apiRequest<any>("GET", ENDPOINTS.personalDetail);
        if (
          response.mainData &&
          response.mainData.data &&
          (response.mainData.data as any).first_name
        ) {
          const data: any = response.mainData.data;
          setFullName(`${data.first_name} ${data.last_name}`);
        } else {
          if (response.mainData && response.mainData.error_message) {
            dispatch(setPageError(response.mainData.error_message));
          } else if (
            response.mainData &&
            response.mainData.success &&
            response.mainData.success.toString().toLowerCase() === "false"
          ) {
            // handle false success if needed
          } else if ((response as any).error) {
            dispatch(setPageError((response as any).error));
          }
        }
      } catch (error) {
        console.error("Error fetching personal details", error);
      }
    };
    getPersonalDetails();
  }, [dispatch]);

  useEffect(() => {
    const getBankList = async () => {
      try {
        const resp: ApiResponse = await apiRequest(
          "GET",
          ENDPOINTS.banklist + "?is_aa_enabled=true"
        );
        const isSuccess =
          resp.mainData?.success?.toString().toLowerCase() === "true";
        const hasData = Array.isArray(resp.mainData?.data);

        if (isSuccess && hasData) {
          const banks = resp.mainData!.data!;
          setAllBanks(banks);
        } else if (resp.mainData?.error_message) {
          dispatch(setPageError(resp.mainData.error_message));
        } else {
          dispatch(setPageError("Failed to load bank list. Please try again."));
        }
      } catch (err) {
        console.error("Error fetching bank list", err);
        dispatch(
          setPageError("Something went wrong while fetching bank data.")
        );
      }
    };

    getBankList();

    // --- CT Event for 'Screen View' ---
    const cteventsfromstorage = JSON.parse(
      localStorage.getItem("eventsdata") || "null"
    );
    const productcode = localStorage.getItem("product_code");
    const event_name = "Screen View";
    const event_property = {
      "Screen Name": "Penny Drop",
      "Product category": productcode, // Assuming product_code is relevant here
      Source: cteventsfromstorage?.source, // Assuming source is relevant here
    };
    setCtdata({ event_name, event_property });
    // --- End of CT Event ---
  }, [dispatch]);

  const openPopup = () => setIsPopupOpen(true);
  const closePopup = () => setIsPopupOpen(false);

  const handleBankClick = (bankId: string, bankName: string) => {
    setSelectedBankId(bankId);
    setSelectedBankName(bankName);
    closePopup();
  };

  // Formik setup
  const formik = useFormik({
    initialValues: {
      accountNumber: accountNumber,
      ifscCode: ifscCode,
    },
    validationSchema: Yup.object({
      accountNumber: Yup.string()
        .required("Bank account number is required")
        .test(
          "valid-length",
          "Bank account number must contain between 1 and 25 digits",
          (value) => (value ? value.replace(/ /g, "").length <= 25 : false)
        ),
      ifscCode: Yup.string()
        .required("IFSC Code is required")
        .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, "Invalid IFSC Code format"),
    }),
    onSubmit: async (values) => {
      // --- CT Event for 'Verify Bank Account' Button Click ---
      const cteventsfromstorage = JSON.parse(
        localStorage.getItem("eventsdata") || "null"
      );
      const productcode = localStorage.getItem("product_code");
      const event_name = "Button Clicked";
      const event_property = {
        pagename: "Penny Drop",
        CTA: "Verify Bank Account",
        "Product category": productcode,
        Source: cteventsfromstorage?.source,
        "Bank Name": selectedBankName, // Add selected bank name
      };
      setCtdata({ event_name, event_property });
      // --- End of CT Event ---

      setLoaded(false);
      try {
        const payload = {
          account_number: accountNumber.replace(/ /g, ""),
          ifsc_code: ifscCode,
        };
        const response = await apiRequest<any>(
          "POST",
          ENDPOINTS.trigger_penny_drop,
          payload
        );
        if (
          response.mainData &&
          response.mainData.success &&
          response.mainData.success.toString().toLowerCase() === "true"
        ) {
          router.push(`/banksuccess`);
        } else {
          setLoaded(true);
          if (response.mainData && response.mainData.error_message) {
            dispatch(setPageError(response.mainData.error_message));
          } else if (
            response.mainData &&
            response.mainData.success?.toString().toLowerCase() === "false"
          ) {
            dispatch(setPageError(response.mainData.error_message || ""));
          }
        }
      } catch (error) {
        setLoaded(true);
      } finally {
        setTimeout(() => {
          dispatch(setPageError(""));
        }, 4000);
      }
    },
  });

  return (
    <PageWrapper>
      {loaded ? (
        <div className={styles.externalWrapper}>
          <PageHeader
            title="Verify salary bank account to receive your loan"
            para="Your loan goes into your verified salary account, and EMIs are auto-debited from this account."
            call={true}
            back={true}
          />

          <div className={`page-content ${styles.pageContent}`}>
            <div className={styles.mainSection}>
              <div className={styles.bankName}>
                <div className={styles.bankDetails}>
                  <div className={styles.bankImage}>
                    <BankImage
                      key={selectedBankName}
                      bankName={selectedBankName || "Default Bank"}
                      ratioHeight={48}
                    />
                  </div>
                  <p>{selectedBankName || "Select a bank"}</p>
                </div>
                <button className={styles.dropdownButton} onClick={openPopup}>
                  <Image src={dropdown} alt="Select bank" loading="lazy" />
                </button>
              </div>

              <div className={styles.divider} />

              <div className={styles.userName}>
                <div className={styles.name}>
                  <p>Full name as per KYC</p>
                  <p className={styles.kycName}>{fullName}</p>
                </div>
                <div className={styles.userIcon}>
                  <Image src={userFrame} alt="User frame" loading="lazy" />
                </div>
              </div>

              <div className={styles.bankForm}>
                <form onSubmit={formik.handleSubmit}>
                  <div className="input-wrapper">
                    <input
                      type="password"
                      name="accountNumber"
                      placeholder=" "
                      inputMode="numeric"
                      style={{ fontSize: "24px" }}
                      className={`form-control`}
                      value={formik.values.accountNumber}
                      onPaste={(e) => e.preventDefault()}
                      onCopy={(e) => e.preventDefault()}
                      disabled={!isChecked}
                      onChange={(e) => {
                        const value = e.target.value.replace(/[^0-9]/g, "");
                        formik.setFieldValue(
                          "accountNumber",
                          value
                            .match(/.{1,4}/g)
                            ?.join(" ")
                            .slice(0, 31) || ""
                        );
                        dispatch(
                          setAccountNumber(
                            value
                              .match(/.{1,4}/g)
                              ?.join(" ")
                              .slice(0, 31) || ""
                          )
                        );
                        if (value?.length === 25) {
                          setTimeout(() => e.target.blur(), 0);
                        }
                      }}
                    />
                    <label>Enter Bank Account Number</label>
                    {typeof formik.errors.accountNumber === "string" &&
                      formik.touched.accountNumber && (
                        <div className="error">
                          {formik.errors.accountNumber}
                        </div>
                      )}
                    <p className={styles.infoText}>
                      Bank account holder name should match KYC records
                    </p>
                  </div>
                  <div className="input-wrapper">
                    <input
                      type="text"
                      name="ifscCode"
                      autoComplete="off"
                      placeholder=" "
                      maxLength={11}
                      className={`form-control`}
                      value={formik.values.ifscCode}
                      onChange={(e) => {
                        const value = e.target.value;
                        const formattedValue = value
                          ? value.replace(/[^A-Za-z0-9]/g, "").slice(0, 11)
                          : "";
                        formik.setFieldValue(
                          "ifscCode",
                          formattedValue.toUpperCase()
                        );
                        dispatch(setIfscCode(formattedValue.toUpperCase()));
                        if (formattedValue?.length === 11) {
                          setTimeout(() => e.target.blur(), 0);
                        }
                      }}
                      onBlur={formik.handleBlur}
                      disabled={!isChecked}
                    />
                    <label>IFSC Code</label>
                    {typeof formik.errors.ifscCode === "string" &&
                      formik.errors.ifscCode &&
                      formik.touched.ifscCode && (
                        <div className="error">{formik.errors.ifscCode}</div>
                      )}
                  </div>
                  <div
                    className={`bottom-footer mt-auto ${styles.bottomFooter}`}
                  >
                    <p className="secure-tag">
                      <Image src={shield} alt="Shield icon" loading="lazy" />{" "}
                      Your data is 100% safe & secure
                    </p>
                    <div className={styles.buttonWrapper}>
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={!formik.isValid || !formik.dirty}
                      >
                        Verify bank Account
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <BottomPopup
            isOpen={isPopupOpen}
            onClose={closePopup}
            title="Select or type your bank name from the list"
            className="custom-bank-popup"
          >
            <div className={styles.otherBanks}>
              <div className="input-wrapper">
                <input
                  type="text"
                  name="otherBank"
                  autoComplete="off"
                  placeholder=" "
                  className={`form-control ${styles.search}`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <label>Search by bank name</label>
              </div>
              <ul className={styles.otherBanksList}>
                {filteredBanks.map((bank) => (
                  <li
                    key={bank.id}
                    className={
                      selectedBankId === bank.id ? styles.selected : ""
                    }
                    onClick={() => handleBankClick(bank.id, bank.name)}
                  >
                    <BankImage bankName={bank.name} ratioHeight={32} />
                    <span>{bank.name}</span>
                  </li>
                ))}
              </ul>
            </div>
          </BottomPopup>

          {pageerror || pagesuccess ? (
            <ToastMessage color={pagesuccess ? "green" : "red"}>
              {pagesuccess ? pagesuccess : pageerror}
            </ToastMessage>
          ) : null}
        </div>
      ) : (
        <LoadingComp />
      )}
      {/* Added CtEvents component to render */}
      <CtEvents data={ctData} />
    </PageWrapper>
  );
}

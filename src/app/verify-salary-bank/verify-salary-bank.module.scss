/* File: verify-salary-bank.module.scss */

.externalWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.externalWrapper .bottomFooter {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

.pageContent {
  min-height: calc(100vh - 400px);
}

.mainSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 350px;
  border: 1px solid #e9e9e9;
  border-radius: 8px;
  padding: 16px;
}

.bankName {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.bankDetails {
  display: flex;
  align-items: center;
  gap: 10px;

  p {
    font-size: 14px;
    font-weight: 600;
    line-height: 100%;
    letter-spacing: 0px;
  }
}

.divider {
  border-top: 1px solid #e0e0e0;
  margin: 16px 0;
  width: 100%;
}

.bankImage {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 1px solid #e9e9e9;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.bankForm {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.dropdownButton {
  background: none;
  border: none;
}

.userName {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  margin-bottom: 36px;
}

.name {
  display: flex;
  flex-direction: column;
  gap: 6px;

  p {
    font-size: 12px;
    font-weight: 400;
    line-height: 100%;
    letter-spacing: 0px;
    color: #000000cc;
  }
}

.kycName {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #******** !important;
}

.userIcon {
  width: 40px;
  height: 40px;
  background-color: #eeeded;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.buttonWrapper {
  padding: 0 16px;
}

.infoText {
  font-size: 12px;
  font-weight: 400;
  line-height: 100%;
  letter-spacing: 0px;
  color: #7b7b7b;
  margin-top: 6px;
}

.otherBanks {
  margin-top: 24px;

  .search {
    height: 52px;
    padding-left: 35px;

    + label {
      top: 20px;
      left: 12px;
    }

    &:focus,
    &:not(:placeholder-shown) {
      + label {
        top: 0;
      }
    }
  }

  .otherBanksList {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 200px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 7px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    &::-webkit-scrollbar-thumb {
      background: #888;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    li {
      display: flex;
      align-items: center;
      padding: 8px 0;
      cursor: pointer;
      position: relative;

      img {
        margin-right: 10px;
      }

      &:not(:last-of-type)::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 42px;
        right: 0;
        height: 1px;
        background-color: #e0e0e0;
      }

      &.selected {
        color: #000;
      }
    }
  }
}

"use client"
import { <PERSON>_San<PERSON> } from "next/font/google";
import "./globals.scss";
import ReduxProvider from "./store/ReduxProvider";
import CommonDataListener from "./utils/CommonDataListener";
import { Suspense, useEffect, useState } from 'react'
import Head from "next/head";
import KeyboardAwareLayout from "./keyboardLayout";
import LoadingComp from "./component/loader";
import { useRouter } from 'next/router'


const monaSans = Mona_Sans({
  variable: "--font-mona-sans",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [height, setHeight] = useState("100vh");
  const [loading, setloading] = useState(false);
  const isIOS = () => {
    if (typeof window !== "undefined") {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) || (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    }
    return false;
  };
  useEffect(() => {
    if (isIOS()) return; // Exclude iOS devices

    const updateHeight = () => {
      setHeight(`${window.visualViewport?.height}px`);
    };

    window.visualViewport?.addEventListener("resize", updateHeight);
    updateHeight(); // Set initial height

    return () => {
      window.visualViewport?.removeEventListener("resize", updateHeight);
    };
  }, []);

  useEffect(() => {
    if (isIOS()) return; // Exclude iOS devices

    const handleFocus = (event: FocusEvent) => {
      const target = event.target as HTMLElement;
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        setTimeout(() => {
          target.scrollIntoView({ behavior: "smooth", block: "center" });
        }, 300); // Delay ensures scrolling after keyboard opens
      }
    };

    document.addEventListener("focusin", handleFocus);
    return () => {
      document.removeEventListener("focusin", handleFocus);
    };

  }, []);
  return (
    <html lang="en">
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, height=device-height" />
      </Head>
      <body className={`${monaSans.variable} ${monaSans.variable}`} style={{ height, overflow: "auto", transition: "height 0.3s ease" }}>
        <Suspense fallback={<LoadingComp />}>

          <main className="main-container">
            <ReduxProvider>
              <KeyboardAwareLayout>
                {children}
              </KeyboardAwareLayout>
              <CommonDataListener /> {/* Add this component */}

            </ReduxProvider>
          </main>
        </Suspense>
      </body>
    </html>
  );
}

'use client'
import React from 'react';
import '../scss/button.scss';
import successImg from '../images/success-icon.svg'
import styles from './kyc.module.scss'
import Image from 'next/image';
function BankSuccess() {
    return (
        <div className='external-wrapper'>
            <div className='page-content'>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        <Image src={successImg} alt="Congratulations" />
                        <h3>Success!</h3>
                        <p>Your selfie captured and verified successfully!</p>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default BankSuccess;

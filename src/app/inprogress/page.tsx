"use client";

import { useState, useEffect } from 'react';
import ProgressBar from './ProgressBar';
import styles from './inprogress.module.scss'
import Image from 'next/image';
import stepchk from '../images/stepchk.png'
import poweredby from '../images/poweredby.png'
import { apiRequest, fetchCommonApi } from '../utils/api';
import { ENDPOINTS } from '../utils/endpoints';
import { setCommonData } from '../store/slices/commonSlice';
import store from '../store/store';
import CtEvents from '../utils/Ctevents';

export default function Home() {
    const [currentStep, setCurrentStep] = useState(0);
    const [headerText, setHeaderText] = useState('Keep going!');
    const [subText, setSubText] = useState('Only 2 more minutes to get funds...');
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
    interface SaveBasicDetailsResponse {
        success: boolean | string;
        status: boolean | string;
        error_message?: string;
        data: Record<string, any>,
        steps: []
    }

    const getInprogress = async () => {
        try {
            const ctevents = JSON.parse(localStorage.getItem("eventsdata") || "null");
            const productcode = localStorage.getItem("product_code")

            const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.inProgress);
            if (response.mainData && response.mainData.status && response.mainData.status.toString().toLowerCase() === "true") {
                const allsteps = response.mainData.data.steps;
                if (Array.isArray(allsteps)) {
                    const initiatedStep = allsteps.find((step: any) => step.step_status === 'IN_PROGRESS');
                    let stepOrder = 1;
                    if (initiatedStep) {
                        stepOrder = initiatedStep.step_order;
                        setCtdata({
                            event_name: "LOC Journey State Viewed",
                            event_property: {
                                "state": allsteps[stepOrder - 1].step_name,
                                "Product category": productcode,
                                Source: ctevents?.source,
                            },
                        });
                    }
                    setCurrentStep(stepOrder);
                    if (stepOrder === 4) {
                        setHeaderText('Great!');
                        setSubText('All set to get funds...');
                    } else {
                        setHeaderText('Keep going!');
                        setSubText('Only 2 more minutes to get funds...');
                    }

                }
            } else {
                if (response.mainData && response.mainData.error_message) {
                }
                if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {

                } else if (response.error) {

                } else {

                }
            }
        } catch (error) {

        }
    }


    useEffect(() => {
        getInprogress();
        setTimeout(async () => {
            const commonData = await fetchCommonApi();
            store.dispatch(setCommonData(commonData));
        }, 8000);
    }, []);

    return (
        <div className='external-wrapper'>
            <div className='page-content'>
                <div className={styles.progressparent}>

                    <div className={styles.progressbox}>
                        <div className={styles.stepchkcircle}>
                            <Image src={stepchk} alt='' />
                        </div>
                        <h2>{headerText}</h2>
                        <p>{subText}</p>
                        <ProgressBar currentStep={currentStep} />
                    </div>
                </div>
                <div className={styles.poweredby}>
                    <Image src={poweredby} alt='' />
                </div>
            </div>
            <CtEvents data={ctData} />
        </div>
    );
}
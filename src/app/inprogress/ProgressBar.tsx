"use client";

import { useEffect, useState } from 'react';
import { Check } from 'lucide-react';
import styles from './inprogress.module.scss';

interface ProgressBarProps {
  currentStep: number;
}

interface Step {
  id: number;
  title: string;
}

const steps: Step[] = [
  { id: 1, title: '<PERSON>an Offer' },
  { id: 2, title: '<PERSON><PERSON> and KYC' },
  { id: 3, title: 'Banking Details' },
  { id: 4, title: 'Auto Pay' }
];

export default function ProgressBar({ currentStep }: ProgressBarProps) {
  const [animatedStep, setAnimatedStep] = useState(0);
  const [animatedLines, setAnimatedLines] = useState<number[]>([]);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // On mount, start animation immediately
    if (!mounted) {
      setMounted(true);
      setAnimatedStep(1);
      return;
    }
    if (currentStep > animatedStep) {
      setAnimatedLines((prev) => [...prev, animatedStep + 1]);
      const timer = setTimeout(() => {
        setAnimatedStep(animatedStep + 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (currentStep < animatedStep) {
      setAnimatedStep(currentStep);
      setAnimatedLines((prev) => prev.filter((id) => id <= currentStep));
    }
  }, [currentStep, animatedStep, mounted]);

  const getStepStatus = (stepId: number) => {
    if (stepId < animatedStep) return 'completed';
    if (stepId === animatedStep) return 'current';
    return 'upcoming';
  };

  const getLineStatus = (stepId: number) => {
    if (animatedLines.includes(stepId)) return 'completed';
    return 'upcoming';
  };

  return (
    <div className={styles.progressContainer}>
      <div className={styles.progressBar}>
        {steps.map((step, index) => (
          <div key={step.id} className={styles.stepContainer}>
            <div className={styles.stepContent}>
              <div 
                className={`${styles.stepCircle} ${styles[getStepStatus(step.id)]}`}
              >
                <Check size={16} className={styles.checkIcon} />
              </div>
              <span className={`${styles.stepTitle} ${styles[getStepStatus(step.id)]}`}>
                {step.title}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className={styles.connectingLineWrapper}>
                <div className={styles.connectingLineBg} />
                <div
                  className={
                    `${styles.connectingLineFg} ${getLineStatus(step.id + 1) === 'completed' ? styles.animated : ''}`
                  }
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
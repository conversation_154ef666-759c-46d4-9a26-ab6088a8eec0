.progressparent {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    max-width: 420px;
   
    .progressbox {
        width: 100%;
        background: #F5F5F5;
        border-radius: 34px;
        padding: 23px;
        padding-top: 51px;
        position: relative;

        h2 {
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            padding-bottom: 10px;
            color: #1F1F1F;
        }
        p {
            font-weight: 400;
            font-size: 14px;
            color: #1F1F1F;
            text-align: center;
            padding-bottom: 24px;
        }
        .stepchkcircle {
            width: 60px;
            height: 60px;
            position: absolute;
            top: -30px;
            left: 0;
            right: 0;
            margin: 0 auto;
            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}

.poweredby {
    position: absolute;
    border: 30px;
    width: 173px;
    left: 0;
    right: 0;
    margin: 0 auto;
    bottom: 30px;
    img {
        max-width: 100%;
        height: auto;
    }
}

.progressContainer {
    padding: 40px 20px;
    max-width: 400px;
    margin: 0 auto;
    background: #fff;
    border-radius: 12px;
}

.progressBar {
    display: flex;
    flex-direction: column;
    gap: 0;
}

.stepContainer {
    position: relative;
    display: flex;
    flex-direction: column;
}

.stepContent {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    z-index: 2;
}

.stepCircle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    animation: stepAppear 1s ease forwards;
    opacity: 0;
    transform: scale(0.8);

    &.completed {
        background-color: #0FBC26;
        border: 2px solid #0FBC26;
        color: white;
        opacity: 1;
        transform: scale(1);
    }

    &.current {
        background-color: transparent;
        border: 2px solid #0FBC26;
        color: #0FBC26;
        opacity: 1;
        transform: scale(1);

        &::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            border: 2px solid #0FBC26;
            opacity: 0;
            animation: pulse 2s infinite;
        }
    }

    &.upcoming {
        background-color: #C8C8C8;
        border: 2px solid #C8C8C8;
        color: #fff;
        opacity: 1;
        transform: scale(1);
    }
}

.checkIcon {
    opacity: 0;
    transform: scale(0);
    transition: all 0.2s ease;

    .stepCircle.completed &,
    .stepCircle.current & {
        opacity: 1;
        transform: scale(1);
    }

    .stepCircle.upcoming & {
        opacity: 1;
        transform: scale(1);
    }
}

.stepTitle {
    font-size: 164px;
    font-weight: 600;
    transition: color 0.3s ease;

    &.completed {
        color: #000;
    }

    &.current {
        color: #000;
        font-weight: 600;
    }

    &.upcoming {
        color: #000;
    }
}

.connectingLineWrapper {
    width: 2px;
    height: 40px;
    margin-left: 13px;
    position: relative;
}

.connectingLineBg {
    width: 2px;
    height: 100%;
    background-color: #C8C8C8;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
}

.connectingLineFg {
    width: 2px;
    height: 100%;
    background-color: #0FBC26;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    transform-origin: top;
    transform: scaleY(0);
    transition: background-color 0.3s;
}

.connectingLineFg.animated {
    animation: lineGrow 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes stepAppear {
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes lineGrow {
    from {
        transform: scaleY(0);
    }

    to {
        transform: scaleY(1);
    }
}

@keyframes pulse {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }

    50% {
        opacity: 0.3;
        transform: scale(1.1);
    }

    100% {
        opacity: 0;
        transform: scale(1.3);
    }
}

// @media (max-width: 640px) {
    .progressContainer {
        padding: 25px 38px 34px 37px;
    }

    .stepContent {
        gap: 12px;
    }

    .stepCircle {
        width: 28px;
        height: 28px;
    }

    .checkIcon {
        width: 14px;
        height: 14px;
    }

    .stepTitle {
        font-size: 14px;
    }

    .connectingLine {
        height: 32px;
        margin-left: 13px;
    }
// }
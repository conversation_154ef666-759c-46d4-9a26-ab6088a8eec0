import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface AddressState {
  startCapture: boolean;
  capturedImage: string | null;
  missingDetails: string | null;
  pageerror: string | null;
  pagesuccess: string | null;
  pincodeValid: boolean;
  fileerror: string | null;
  fileData: string | null;
  selectedDocType: string | null;
  isUploadModalOpen: boolean;
  uploadedImage: string;
  addressList: any;
}

// Define the initial state
const initialState: AddressState = {
  startCapture: false,
  capturedImage: null,
  missingDetails: null,
  pageerror: null,
  pagesuccess: null,
  pincodeValid: false,
  fileerror: null,
  fileData: null,
  selectedDocType: null,
  isUploadModalOpen: false,
  uploadedImage: '',
  addressList: null
};

// Create the slice
const addressSlice = createSlice({
  name: 'address',
  initialState,
  reducers: {
    setStartCapture: (state, action: PayloadAction<boolean>) => {
      state.startCapture = action.payload;
    },
    setCapturedImage: (state, action: PayloadAction<string | null>) => {
      state.capturedImage = action.payload;
    },
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setPincodeValid: (state, action: PayloadAction<boolean>) => {
      state.pincodeValid = action.payload;
    },
    setMissingDetails: (state, action: PayloadAction<string | null>) => {
      state.missingDetails = action.payload;
    },
    setSelectedDocType: (state, action: PayloadAction<string | null>) => {
      state.selectedDocType = action.payload;
    },
    openUploadModal: (state) => {
      state.isUploadModalOpen = true;
    },
    closeUploadModal: (state) => {
      state.fileData = '';
      state.isUploadModalOpen = false;
    },
    setFileError: (state, action: PayloadAction<string | null>) => {
      state.fileerror = action.payload;
    },
    setFileData: (state, action: PayloadAction<string | null>) => {
      state.fileData = action.payload;
    },
    setUploadedFile: (state, action: PayloadAction<string>) => {
      state.uploadedImage = action.payload;
    },
    setAddressList: (state, action: PayloadAction<string>) => {
      state.addressList = action.payload;
    },
  },
});

// Export actions
export const {
  setStartCapture,
  setCapturedImage,
  setPageError,
  setPageSuccess,
  setPincodeValid,
  setMissingDetails,
  setSelectedDocType,
  openUploadModal,
  closeUploadModal,
  setFileError,
  setFileData,
  setUploadedFile,
  setAddressList
} = addressSlice.actions;

// Export the reducer
export default addressSlice.reducer;

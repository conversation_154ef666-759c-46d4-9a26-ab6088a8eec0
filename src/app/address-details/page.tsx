'use client';

import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import SelectDropdown from '../components/SelectDropdown';
import { useDispatch, useSelector } from 'react-redux';
import styles from './address.module.scss';
import Image from 'next/image';
import UploadIcon from '../images/uploadIcon.svg';
import UploadedIcon from '../images/valid-check.svg';
import { openUploadModal, setSelectedDocType, setUploadedFile } from '../store/features/addressSlice';
import store, { RootState } from '../store/store';
import ShieldIcon from '../images/shield-icon.svg';
import BottomPopup from '../components/popups/BottomPopup';
import { closeUploadModal, setAddressList, setPageError, setPageSuccess, setPincodeValid } from './address.slice';
import { useEffect, useRef, useState } from 'react';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { AddressForm } from './address.interface';
import React from 'react';
import { ENDPOINTS } from '../utils/endpoints';
import { Proof } from "../utils/fetchData";
import PageWrapper from '../components/PageWrapper';
import { setCommonData } from '../store/slices/commonSlice';
import LoadingComp from '../component/loader';
import CtEvents from '../utils/Ctevents';
import { useRouter } from "next/navigation";
import CommunicationAddressDetails from '../communication-address-details-component';


function AddressDetails() {
    const dispatch = useDispatch();
    const { uploadedImage, selectedDocType, isUploadModalOpen, pageerror, pagesuccess, pincodeValid, addressList } = useSelector((state: RootState) => state.address);
    const [data, setData] = useState<any[]>([]);
    const [loaded, setLoaded] = useState<boolean>(false);
    const [documentErr, setDocumentErr] = useState(false);
    // const [fileName, setFileName] = useState("")
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    const router = useRouter();


    const uploadInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if(uploadedImage !== "") {
            setDocumentErr(false);
        }
    }, [uploadedImage])

    //const MAX_FILES = 5;
    const MAX_FILE_SIZE = 5 * 1024 * 1024;
    const ALLOWED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/png'];

    const validateAndUploadFiles = (files: FileList) => {
        //let newFiles = [...uploadedImage]; // Keep existing files
        // setFileName(files[0].name);  

        for (const file of files) {
            /*if (newFiles.length >= MAX_FILES) {
                alert(`You can upload a maximum of ${MAX_FILES} files.`);
                break;
            }*/
            if (!ALLOWED_FILE_TYPES.includes(file.type)) {
                alert('Supported formats: PDF, JPEG, PNG.');
                continue;
            }
            if (file.size > MAX_FILE_SIZE) {
                alert('File size must be less than 5 MB.');
                continue;
            }

            const reader = new FileReader();
            reader.onloadend = () => {
                //newFiles = [...newFiles, reader.result as string]; // Append new file
                dispatch(setUploadedFile(reader.result as string)); // Update Redux state
            };
            reader.readAsDataURL(file);
        }
    };

    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            validateAndUploadFiles(event.target.files);
        }
    };

    const handleBrowseFilesClick = () => {
        uploadInputRef.current?.click();
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
    };

    const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        if (event.dataTransfer.files) {
            validateAndUploadFiles(event.dataTransfer.files);
        }
    };

    const validateAddressInput = (value: any) => {
        return value.replace(/[^a-zA-Z0-9\s,.-]/g, ''); // Allows letters, numbers, space, comma, dot, and hyphen
    };

    const validationSchema = Yup.object({
        isAddressSame: Yup.string().required("Please select if the address is same"),

        residenceType: Yup.string().when("isAddressSame", {
            is: (val: string) => val !== "yes",
            then: (schema) => schema.required("Residence Type is required"),
            otherwise: (schema) => schema.notRequired(),
        }),

        flatHouseNo: Yup.string()
            .when("isAddressSame", {
                is: (val: string) => val !== "yes",
                // then: (schema) => schema.required("Flat/House No. is required").max(50, "Maximum 50 characters allowed").min(3, "Minimum 3 characters must be there"),
                then: (schema) => schema.required("Flat/House No. is required"),
                otherwise: (schema) => schema.notRequired(),
            }),

        street: Yup.string()
            .when("isAddressSame", {
                is: (val: string) => val !== "yes",
                then: (schema) => schema.required("Street is required"),
                otherwise: (schema) => schema.notRequired(),
            }),

        landmark: Yup.string()
            .when("isAddressSame", {
                is: (val: string) => val !== "yes",
                then: (schema) => schema.required("Landmark is required"),
                otherwise: (schema) => schema.notRequired(),
            }),

        pincode: Yup.string()
            .nullable()
            .when("isAddressSame", {
                is: (val: string) => val !== "yes",
                then: (schema) =>
                    schema
                        .required("PIN Code is required")
                        .test(
                            "is-not-blank",
                            "PIN Code cannot be blank",
                            (value) => value === "N/A" || !!value
                        )
                        .test(
                            "is-six-digits",
                            "PIN Code must be exactly 6 digits long",
                            (value) => !value || /^\d{3} \d{3}$/.test(value) // "xxx xxx" format
                        )
                        .test(
                            "only-digits",
                            "PIN Code must only contain numeric digits",
                            (value) => !value || /^\d{3}( \d{3})*$/.test(value) // Numeric check
                        )
                        .test(
                            "no-leading-zero",
                            "PIN Code must not start with zero",
                            (value) => !value || /^[1-9]/.test(value) // First digit shouldn't be zero
                        ),
                otherwise: (schema) => schema.notRequired(),
            }),

        city: Yup.string().when("isAddressSame", {
            is: (val: string) => val !== "yes",
            then: (schema) => schema.required("City is required"),
            otherwise: (schema) => schema.notRequired(),
        }),

        state: Yup.string().when("isAddressSame", {
            is: (val: string) => val !== "yes",
            then: (schema) => schema.required("State is required"),
            otherwise: (schema) => schema.notRequired(),
        }),

        docuType: Yup.string().when("isAddressSame", {
            is: (val: string) => val !== "yes",
            then: (schema) => schema.required("Required selected document type"),
            otherwise: (schema) => schema.notRequired(),
        })
    });


    const formik = useFormik<AddressForm>({
        initialValues: {
            isAddressSame: 'yes',
            residenceType: '',
            flatHouseNo: '',
            street: '',
            landmark: '',
            pincode: '',
            city: '',
            state: '',
            docuType: '',
        },
        validationSchema,
        onSubmit: async (values) => {
            submitForm(values)
        },
    });

    // const docuTypeList = ['Lease Agreement', 'Utility Bill'];

    const saveUploadedFile = () => {
        //upload file api
        if (uploadedImage) {
            dispatch(closeUploadModal())
        }
    }

    const verifyPincode = async (pincode: string) => {
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))

        try {
            if (pincode && pincode.length === 6) {
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.pincode, { pincode });
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success')) {
                    formik.setFieldValue('city', response.mainData.data.city)
                    formik.setFieldValue('state', response.mainData.data.state)
                    dispatch(setPincodeValid(true))
                } else {
                    dispatch(setPincodeValid(false))
                    formik.setFieldValue('city', '')
                    formik.setFieldValue('state', '')
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }
                    else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            } else {
                dispatch(setPincodeValid(false))
                formik.setFieldValue('city', '')
                formik.setFieldValue('state', '')
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        }
    };

    const submitForm = async (values: AddressForm) => {
        setDocumentErr(false);
        if(uploadedImage === "" && values.docuType !== "") {
            setDocumentErr(true);
            return;
        }
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))
        setLoaded(false)
        try {
            if (formik.values.isAddressSame === 'no' && !pincodeValid) {
                setTimeout(() => {
                    dispatch(setPageError('Pin code is not valid.'))
                }, 200);
            } else {
                let payload
                if (formik.values.isAddressSame === 'yes') {
                    payload = {
                        aadhar_address_id: addressList?.id || 0
                    }
                } else {
                    payload = {
                        flat_no: values.flatHouseNo,
                        address_line_1: values.street,
                        address_line_2: '',
                        landmark: values.landmark,
                        pincode: parseInt(values.pincode.split(' ').join('')),
                        city: values.city,
                        state: values.state,
                        residence_type: values.residenceType === 'Rented House' ? 'Rented' : 'Self_Owned',
                        address_proof: uploadedImage,
                        //aadhar_address_id: addressList?.address_id || 0
                    }
                }
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.addcurrentaddress, payload);
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success')) {
                    // dispatch(setPageSuccess(response.mainData.message))
                    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                    const event_name = 'Button Clicked'
                    const productcode = localStorage.getItem("product_code")

                    const event_property = { "Page Name": "Address Details with Proof", "CTA": "Continue", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", }
                    setCtdata({ event_name, event_property })
                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }
                    if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError(response.mainData?.message as string))
                    }
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        } finally {
            setTimeout(() => {
                setLoaded(true)
            }, 2000);
        }
    };

    const getAddressList = async () => {
        dispatch(setPageError(''))
        try {
            const response = await apiRequest<ApiResponse>("GET", `${ENDPOINTS.aadharAddressData}`);
            if (response.mainData && response.mainData.success?.toString().toLowerCase() === 'true') {
                dispatch(setAddressList(response.mainData.data))
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                    dispatch(setPageError(response.mainData.error_message || 'Error in uploading file'))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        }
    };
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    useEffect(() => {
        getAddressList()
    }, [])
    useEffect(() => {
        const fetchData = async () => {
            const result = await Proof();
            setData(result.document);
        };
        fetchData();
    }, []);
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "Address Details with Proof", "Product category":productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    useEffect(() => {
        if (formik.values.isAddressSame === "yes") {
            formik.setErrors({});
        }
    }, [formik.values.isAddressSame]);

    return (
        <PageWrapper>
            {loaded ?
                <div className={`${styles.addressPage}`}>
                    <PageHeader
                        title="Address Details"
                        para="To comply with regulatory guidelines, we must verify your current address"
                    />
                    <div className={`page-content ${styles.pageContent}`}>
                        <div className={styles.addressDetails}>
                            <strong style={{ fontWeight: '600' }}>Current Address as per KYC:</strong>
                            <br />
                            {addressList ? <p>{addressList?.address}<br /></p> : null}
                        </div>
                        {/* <form onSubmit={formik.handleSubmit}>
                            Radio Buttons for Address Confirmation need to comment
                            <div className="input-wrapper radio-wrapper">
                                <label className="radio-label">Is this your current address?</label>
                                <div className="radios">
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="yes"
                                            name="isAddressSame"
                                            value="yes"
                                            checked={formik.values.isAddressSame === 'yes'}
                                            onChange={() => formik.setFieldValue('isAddressSame', 'yes')}
                                        />
                                        <label htmlFor="yes" className="noabs">
                                            Yes
                                        </label>
                                    </div>
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="no"
                                            name="isAddressSame"
                                            value="no"
                                            checked={formik.values.isAddressSame === 'no'}
                                            onChange={() => formik.setFieldValue('isAddressSame', 'no')}
                                        />
                                        <label htmlFor="no" className="noabs">
                                            No
                                        </label>
                                    </div>
                                </div>
                                {formik.errors.isAddressSame && formik.touched.isAddressSame && (
                                    <div className="error">{formik.errors.isAddressSame}</div>
                                )}
                            </div>
                            {formik.values.isAddressSame === 'no' && (
                                <>
                                    <div className={styles.notebox}>
                                        <div className={styles.title}>
                                            Important Note
                                        </div>
                                        <div className={styles.subtitle}>Choosing a different address could require manual verification and delay your loan disbursal by 2-3 days.</div>
                                    </div>

                                    <h3>Current Address</h3>


                                    <div className="input-wrapper">
                                        <SelectDropdown
                                            name="residenceType"
                                            id="residenceType"
                                            options={['Self Owned House', 'Rented House']}
                                            labelText="Residence Type"
                                            value={formik.values.residenceType}
                                            onChange={(option) => formik.setFieldValue('residenceType', option)}
                                        />
                                        {formik.errors.residenceType && formik.touched.residenceType && (
                                            <div className="error">{formik.errors.residenceType}</div>
                                        )}
                                    </div>

                                    <div className="input-wrapper">
                                        <input
                                            type="text"
                                            name="flatHouseNo"
                                            placeholder=" "
                                            value={formik.values.flatHouseNo}
                                            onChange={(e) => {
                                                const formattedValue = validateAddressInput(e.target.value);
                                                formik.setFieldValue("flatHouseNo", formattedValue);
                                            }}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>Flat/House No.</label>
                                        {formik.errors.flatHouseNo && formik.touched.flatHouseNo && (
                                            <div className="error">{formik.errors.flatHouseNo}</div>
                                        )}
                                    </div>

                                    <div className="input-wrapper">
                                        <input
                                            type="text"
                                            name="street"
                                            placeholder=" "
                                            value={formik.values.street}
                                            onChange={(e) => {
                                                const formattedValue = validateAddressInput(e.target.value);
                                                formik.setFieldValue("street", formattedValue);
                                            }}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>Street</label>
                                        {formik.errors.street && formik.touched.street && (
                                            <div className="error">{formik.errors.street}</div>
                                        )}
                                    </div>

                                    <div className="input-wrapper">
                                        <input
                                            type="text"
                                            name="landmark"
                                            placeholder=" "
                                            value={formik.values.landmark}
                                            onChange={(e) => {
                                                const formattedValue = validateAddressInput(e.target.value);
                                                formik.setFieldValue("landmark", formattedValue);
                                            }}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>Locality/Landmark</label>
                                        {formik.errors.landmark && formik.touched.landmark && (
                                            <div className="error">{formik.errors.landmark}</div>
                                        )}
                                    </div>

                                    <div className="input-wrapper">
                                        <input
                                            type="text"
                                            name="pincode"
                                            maxLength={7}
                                            minLength={6}
                                            placeholder=" "
                                            inputMode="numeric"

                                            value={formik.values.pincode}
                                            onChange={async (e) => {
                                                formik.handleChange(e)
                                                //verifyPincode(e.target.value)
                                                const value = e.target.value ? e.target.value.replace(/[^0-9]/g, "") : '';
                                                const formattedValue = value ? value
                                                    .match(/.{1,3}/g)
                                                    ?.join(" ")
                                                    .slice(0, 7) : '';
                                                formik.setFieldValue("pincode", formattedValue);
                                                await verifyPincode(e.target.value.split(' ').join(''))
                                                if (formattedValue?.length === 7) {
                                                    setTimeout(() => e.target.blur(), 100)
                                                }
                                            }}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>PIN Code</label>
                                        {formik.errors.pincode && formik.touched.pincode && (
                                            <div className="error">{formik.errors.pincode}</div>
                                        )}
                                    </div>

                                    <div className="input-wrapper" style={{ pointerEvents: 'none' }}>
                                        <input
                                            type="text"
                                            name="city"
                                            readOnly
                                            placeholder=" "
                                            value={formik.values.city}
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>City</label>
                                        {formik.errors.city && formik.touched.city && (
                                            <div className="error">{formik.errors.city}</div>
                                        )}
                                    </div>

                                    <div className="input-wrapper" style={{ pointerEvents: 'none' }}>
                                        <input
                                            type="text"
                                            name="state"
                                            readOnly
                                            placeholder=" "
                                            value={formik.values.state}
                                            onChange={formik.handleChange}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>State</label>
                                        {formik.errors.state && formik.touched.state && (
                                            <div className="error">{formik.errors.state}</div>
                                        )}
                                    </div>

                                    <div className="input-wrapper">
                                        <SelectDropdown
                                            name="docuType"
                                            id="docuType"
                                            options={data.map(item => item)}
                                            labelText="Document Type"
                                            value={selectedDocType || ''}
                                            onChange={(option: string | { name: string }) => {
                                                formik.setFieldValue('docuType', option)
                                                if (typeof option === 'string') {
                                                    dispatch(setSelectedDocType(option));
                                                    dispatch(setUploadedFile(''))
                                                } else if ('name' in option) {
                                                    dispatch(setSelectedDocType(option.name));
                                                    dispatch(setUploadedFile(''))
                                                }
                                            }}
                                        />
                                    </div>

                                    <div className="input-wrapper">
                                        <label className="noabs">Upload selected document proof</label>
                                        <p style={{paddingLeft: 6, fontSize: 14, paddingTop: 4}}>{fileName}</p>
                                        <div
                                            className={uploadedImage ? 'upload-button uploaded' : 'upload-button'}
                                            onClick={() => dispatch(openUploadModal())}
                                        >
                                            <div className="text">{uploadedImage ? `File Uploaded successfully` : 'Upload'}</div>
                                            <div className="icon">
                                                {uploadedImage ? (
                                                    <div className="image">
                                                        <Image src={UploadedIcon} alt="Uploaded" />
                                                    </div>
                                                ) : (
                                                    <Image src={UploadIcon} alt="Upload" />
                                                )}
                                            </div>
                                        </div>
                                        {uploadedImage ? <div className='reupload' onClick={() => dispatch(openUploadModal())}>Re-upload</div> : null}
                                        {documentErr && <span style={{color: "red", fontSize: 14}}>Document is required</span>}
                                    </div>
                                </>
                            )}

                            <div className="bottom-footer p-0 mt-auto">
                                <p className="secure-tag">
                                    <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                </p>
                                <button type="submit" className={`btn btn-primary ${formik.values.isAddressSame === 'no' && (!formik.isValid || !formik.dirty || !pincodeValid) ? 'disabled' : ''}`}>
                                    Continue
                                </button>
                            </div>
                        </form> */}
                        <CommunicationAddressDetails pageSource={"address-screen-v3"}/>
                    </div>
                    <BottomPopup
                        isOpen={isUploadModalOpen}
                        onClose={() => {
                            dispatch(closeUploadModal());
                        }}
                        title="Upload"
                        buttons={[
                            {
                                label: 'Upload & Save',
                                onClick: () => {
                                    saveUploadedFile()
                                },
                                className: `mb-0 ${uploadedImage ? '' : 'disabled'}`
                            }
                        ]}
                    >
                        <div>
                            <p style={{ textAlign: 'left' }}>Max Size: 5 MB</p>
                            <p style={{ textAlign: 'left', marginBottom: '15px' }}>Supported Formats: .PDF, PNG, JPEG.</p>
                            <div className="upload-btn">
                                <button
                                    className="btn btn-primary-outline"
                                    onClick={handleBrowseFilesClick}
                                    type="button"
                                >
                                    Browse files
                                </button>
                                {/* Hidden file input to handle file selection */}
                                <input
                                    type="file"
                                    ref={uploadInputRef}
                                    style={{ display: 'none' }}
                                    //multiple={true}
                                    accept="application/pdf,image/jpeg,image/png"
                                    onChange={handleFileInputChange}
                                    onDragOver={handleDragOver}
                                    onDrop={handleDrop}
                                />
                            </div>
                        </div>
                    </BottomPopup>
                </div>
                :

                <LoadingComp />
            }
            <CtEvents data={ctData} />
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
}

export default AddressDetails;
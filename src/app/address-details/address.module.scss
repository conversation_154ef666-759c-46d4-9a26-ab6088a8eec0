@import '../scss/variable.scss';

.addressDetails {
    padding: 20px 14px;
    border-radius: 12px;
    margin-bottom: 25px;
    font-size: 14px;
    background-color: #f5f5f5;
}

.pageContent{
    min-height: calc(100vh - 200px);
}

.addressPage {
    h3 {
        font-size: 20px;
        font-weight: 600;
        margin: 24px 0px;
    }

    form {
        display: flex;
        flex-direction: column;
        min-height: calc(100vh - 340px);
    }
}

.notebox {
    width: 100%;
    background: #FFF8E0;
    border-radius: 12px;
    padding: 11px 16.5px;
    margin-top: 8px;

    .title {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        color: var(--black);
    }

    .subtitle {
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        margin-top: 5px;
        color: var(--black);
    }
}
import { useEffect } from 'react';

/**
 * Custom hook to dynamically update the status bar color.
 * @param color - The color to set for the status bar.
 */
export const useThemeColor = (color: string) => {
  useEffect(() => {
    const themeMetaTag = document.querySelector('meta[name="theme-color"]');
    if (themeMetaTag) {
      themeMetaTag.setAttribute('content', color);
    } else {
      const newMetaTag = document.createElement('meta');
      newMetaTag.setAttribute('name', 'theme-color');
      newMetaTag.setAttribute('content', color);
      document.head.appendChild(newMetaTag);
    }
  }, [color]);
};

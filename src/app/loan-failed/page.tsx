'use client'
import React from 'react';
import '../scss/button.scss';
import failureIcon from '../images/failure-icon.svg'
import styles from './senctioned.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
function LoanSenctioned() {
    return (
        <div className={`external-wrapper ${styles.externalWrapper}`}>
            <PageHeader title='Sorry' para="We can't process your application" centeredHeader={true} call={false} nobg={true} bgcolor="#FFE7E7" icon={<Image src={failureIcon} alt='Header' />} />
            <div className={`page-content ${styles.pageContent}`}>
                <div className="bottom-footer p-0 mt-auto">
                    <button type="button" className="btn btn-primary-outline">Go to Homepage</button>
                    <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
                </div>
            </div>
        </div>
    );
}

export default LoanSenctioned;

"use client"
import PageHeader from "../components/PageHeader"
import styles from './loadingoffer.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import Image from "next/image"
import loadingoffer from '../images/loadingoffer.png'
import { useEffect, useState } from "react"
import React from "react"
// import Lottie from "lottie-react";
import loadingAnimation from "../../../public/Getting_Offer.json"
import loadedAnimation from "../../../public/Got_Offer.json"
import { GetOffer } from '../utils/fetchData';
import { apiRequest, fetchCommonApi } from "../utils/api";
import '../scss/button.scss';
import styless from '../congratulations/congratulations.module.scss'
import loadedboxbottom from '../images/loadedboxbottom.svg'
import { setCommonData } from "../store/slices/commonSlice";
import store, { AppDispatch } from "../store/store";
import dynamic from "next/dynamic";
import { ApiResponse } from "@/app/store/interface/apiInterface";
import { ENDPOINTS } from "@/app/utils/endpoints";
import CtEvents from "../utils/Ctevents";
import { setPageError } from '../register/register.slice';
import { useDispatch } from "react-redux";
import LoadingComp from "../component/loader"
import { pushCleverTapEvent } from "../utils/cleverTapClient"
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });



interface Data {
  timer: any;
  OfferPrice: string;
}
function LoadingOfferPage() {
  useThemeColor('#fff')

  const [currentStep, setCurrentStep] = useState<number>(0);
  const [data, setData] = useState<Data | null>(null);
  const [isAnimationComplete, setIsAnimationComplete] = useState(false);
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
  const [productcode, setproductcode] = useState(false);
  const [isSentinalUser, setIsSentinalUser] = useState<boolean>(false);
  const [isLoading, setIsloading] = useState<boolean>(false)
  const dispatch = useDispatch<AppDispatch>();

  const steps = [
    "Analyzing your profile",
    "Fetching your credit score",
    "Personalizing your offer",
    "Crunching the numbers",
    "Finalizing your loan offer"
  ];

  useEffect(() => {
    const fetchData = async () => {
      setIsAnimationComplete(false); // Reset animation completion state
      const result = await GetOffer(); // Fetch data from Firebase

      if (!result) return; // If no result, don't proceed

      setData(result); // Update state with fetched data

      // Ensure timer is properly set before proceeding
      if (result.timer) {
        const stepDuration = result.timer / steps.length;
        let stepCount = 0;

        const interval = setInterval(() => {
          setCurrentStep((prev) => {
            if (prev < steps.length - 1) {
              stepCount++;
              return prev + 1;
            } else {
              clearInterval(interval);
              setIsAnimationComplete(true);
              return prev;
            }
          });
        }, stepDuration);

        return () => clearInterval(interval);
      }
    };

    fetchData();
    const geproductcode = localStorage.getItem('product_code')
    if (geproductcode == "Sentinel") {
      setproductcode(true)
    }
  }, []);

  interface SaveBasicDetailsResponse {
    success: boolean | string;
    error_message?: string;
    data: Record<string, any>; // Adjust according to the actual data structure you expect
  }

  useEffect(() => {
    const getPersonalDetails = async () => {
      try {
        const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.personalDetail);

        if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
            setIsSentinalUser(response.mainData?.data?.is_sentinel_user)
        } else {

            if (response.mainData && response.mainData.error_message) {
                dispatch(setPageError(response.mainData.error_message))
            }
            else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                dispatch(setPageError(response.mainData.error_message || ''))
            } else if (response.error) {
                dispatch(setPageError(response.error))
            } else {
                dispatch(setPageError('Unexpected error occurred.'))
            }
        }
      } catch (error) {
        dispatch(setPageError('Error in fetching basic details'))
      }
    }
    getPersonalDetails();
  }, []);

  useEffect(() => {
    if (!isAnimationComplete) {
      const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
      const event_name = 'Screen View'
      const event_property = { "Screen Name": "Credit Score Pull", "Source": cteventsfromstorage?.source, "Product category": localStorage.getItem("product_code") }
      setCtdata({ event_name, event_property })
    }
    else {
      const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
      const event_name = 'Screen View'
      const event_property = { "Screen Name": "Soft Loan offer shown", "Source": cteventsfromstorage?.source, "Product category": localStorage.getItem("product_code") }
      setCtdata({ event_name, event_property })
    }
  }, [isAnimationComplete])
  const GotoNext = async () => {
    const commonData = await fetchCommonApi();
    store.dispatch(setCommonData(commonData));
  }
  const createNextStep = async () => {
    setIsloading(true)
    const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.nextStep);
    if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
      const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
      const event_name = 'Button Clicked'
      const productcode = localStorage.getItem("product_code")

      const event_property = { "CTA ": "Proceed", "Page Name": "Soft Loan offer shown", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
      setCtdata({ event_name, event_property })

      const ckycResp = await apiRequest<ApiResponse>("GET", ENDPOINTS.ckycSearch);
      if (ckycResp.mainData?.status === "success" && ckycResp.mainData?.step === "otp") {
        localStorage.setItem("ckycTrnsactionId", ckycResp.mainData?.transaction_id || "");
      }
      const commonData = await fetchCommonApi();
      store.dispatch(setCommonData(commonData));
    }
  }
  return (
    <div>
      {!isAnimationComplete ? (
        <div className={styles.body}>
          <div className={styles.animationbox}>
            <Lottie
              animationData={loadingAnimation}
              loop={!data} // Keep looping until data is available
              style={{ height: 200, width: 200 }}
            />
            <div
              className={styles.loader}
              style={{
                animation: data
                  ? `l2 ${data?.timer / 1000}s steps(200) forwards`
                  : "none" // Prevents progress bar from animating if data is missing
              }}
            ></div>
            <p>{steps[currentStep]}</p>
          </div>
          <p className='powered-by'>
            <span>Powered by Akara Capital Advisors Private Limited</span>
          </p>
        </div>
      ) : (
        <div className='external-wrapper'>
          {/*<PageHeader title='' back={false} nobg={true} />*/}
          <div className='page-content' style={{ minHeight: '100vh' }}>
            <div className={`${styless.congratulationsContent} ${styles.congratulationsContent}`}>
              <div className={`${styless.congratulationsCard} ${styles.congratulationsCard}`}>
                <div className={styles.lottie}>
                  <Lottie
                    animationData={loadedAnimation}
                    loop={false}
                    style={{ height: 200, width: 200 }} // Adjust size as needed
                  />
                </div>
                <h3>Great!</h3>
                <p>You have a loan offer of up to</p>
                {!(productcode || isSentinalUser) ?
                  <h2>₹ {data?.OfferPrice ? Number(data.OfferPrice).toLocaleString("en-IN") : ""}</h2>
                  :
                  <h2>₹ {Number(500000).toLocaleString("en-IN")}</h2>
                }
                <p className={styles.note}>You can enhance your loan offer by providing additional details as you progress through this journey.</p>
              </div>
            </div>
            <p className={`${styless.info} ${styles.info}`}>Lending partners offer credit limits up to ₹5 Lakhs with tenures from 1 to 36 months. Platform fees start at 0.45% + GST, and interest rates from 11.99% p.a., based on user profile. Eligible users get up to 30 days of interest-free credit. Interest applies only to drawn amounts. Please refer to the KFS available at loan approval for details.</p>
            <div className="bottom-footer p-0  mt-auto">
              <button type="submit" className="btn btn-primary" onClick={createNextStep}>Proceed</button>
              <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
            </div>
          </div>
        </div>
      )}
      <CtEvents data={ctData} />
      {isLoading && <LoadingComp />}
      
    </div>
  )
}

export default LoadingOfferPage;

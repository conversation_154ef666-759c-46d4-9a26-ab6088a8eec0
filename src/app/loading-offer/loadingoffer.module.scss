@import '../scss/variable.scss';

.header {
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 10px;

    .headercont {
        padding-top: 0;
        display: flex;
        padding-bottom: 40px;
        justify-content: space-between;
    }
}

.lottie {
    margin: 0 auto;
    display: flex;
    align-items: center;
    flex-flow: column;
}

.note {
    background-color: $light-red;
    font-size: 14px;
    text-align: left;
    padding: 17px 26px;
    padding-right: 60px;
    border-radius: 12px;
    color: $color-black;
    line-height: 20px;
    font-weight: 500;
    background-repeat: no-repeat;
    background-position: right 27px top 17px;
    background-image: url('../images/sparkle.svg');
    margin-bottom: 0 !important;
}

.body {
    padding-left: 16px;
    padding-right: 16px;
    height: 100vh;
    display: flex;
    flex-flow: column;

    .animationbox {
        margin-top: auto;
        margin-bottom: auto;
        display: flex;
        flex-flow: column;
        align-items: center;
        width: 100%;

        img {
            width: 157px;
            height: 141px;
            margin-bottom: 25px;
        }

        >p,
        >span {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            text-align: center;
            color: var(--black);
            margin-top: 20px;
        }

        >span {
            font-weight: 500;
            color: #7B7B7B;
        }

        .steps {
            margin-top: 47px;

            div {
                padding-bottom: 35px;
                display: flex;
                position: relative;

                &:before {
                    content: "";
                    display: flex;
                    height: 30px;
                    width: 1px;
                    background: #B1B1B1;
                    bottom: 2;
                    left: 10.5px;
                    position: absolute;
                    transition: background 0.5s ease;
                }

                &:last-child::before {
                    display: none;
                }

                span {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #B1B1B1;
                    border-radius: 50%;
                    margin-right: 15px;
                    display: flex;
                    transition: background 0.5s ease, border-color 0.5s ease;

                }

                p {
                    font-family: var(--font-mona-sans);
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 16px;
                    color: #7B7B7B;
                    transition: color 0.5s ease;
                }

                &.completed {
                    &:before {
                        background: #21c12b; // Green vertical line
                    }

                    span {
                        background: #21c12b; // Fill circle with green
                        border-color: #21c12b; // Change border color to green
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        &:after {
                            content: "";
                            width: 8.4px;
                            height: 6px;
                            display: flex;
                            background: url(../images/checkmark.png) no-repeat;
                            background-size: 100%;
                        }
                    }

                    p {
                        color: #1f1f1f; // Change text color to dark
                    }
                }

            }

        }
    }



    /* HTML: <div class="loader"></div> */
    .loader {
        margin-top: 30px;
        width: 200px;
        height: 13px;
        border-radius: 8px;
        background: linear-gradient(#FF002B 0 0) 0/0% no-repeat #f5f5f5;
    }

    @keyframes l2 {
        0% {
            background-size: 0%;
        }

        100% {
            background-size: 110%;
        }
    }

    .powerdby {
        margin-top: 18px;
        text-align: center;
        display: flex;
        align-items: center;
        flex-flow: column;
        padding-bottom: 5px;
        margin-top: auto;

        p {
            font-family: var(--font-mona-sans);
            font-weight: 400;
            font-size: 10px;
            line-height: 17px;
            color: #5A5A5A;
        }

        span {
            font-family: var(--font-mona-sans);
            font-weight: 400;
            font-size: 10px;
            line-height: 17px;
            color: #5A5A5A;
        }
    }

    .termsbox {
        margin-top: 13px;
        display: flex;
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;

        a {
            color: var(--primary-color);
        }

        input {
            margin-right: 8px;
            position: relative;
            top: 4px;
        }
    }

    .pointer {
        cursor: pointer;
    }
}

.congratulationsContent {
    padding-top: 30px;
}

.congratulationsCard {
    >div:first-child {
        margin-bottom: 0 !important;
    }
    h3{
        margin-bottom: 22px !important;
    }
    p{
        margin-bottom: 10px !important;
        font-size: 18px;
    }
    h2{
        margin-bottom: 20px !important;
        font-size: 44px !important;
    }
    .note{
        margin-bottom: 0 !important;
        font-size: 14px;
        line-height: 20px;
    }
}
.info{
    margin-bottom: 27px !important;
    line-height: 16px !important;
}
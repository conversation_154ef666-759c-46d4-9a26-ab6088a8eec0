import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface LoanState {
  pageerror: string | null;
  pagesuccess: string | null;
  otp: string | null;
  showAadharBtn: boolean;
  otperror: boolean;
  timer: number;
}

const initialState: LoanState = {
  pageerror: null,
  pagesuccess: null,
  otp: null,
  timer: 90,
  otperror: false,
  showAadharBtn: false,
};

const ckycOtpInputSlice = createSlice({
  name: 'ckycOtpInput',
  initialState,
  reducers: {
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setTimer: (state, action: PayloadAction<number>) => {
      state.timer = action.payload;
    },
    setOtp: (state, action: PayloadAction<string | null>) => {
      state.otp = action.payload;
    },
    setOtpError: (state, action: PayloadAction<boolean>) => {
      state.otperror = action.payload;
    },
    setShowAadharBtn: (state, action: PayloadAction<boolean>) => {
      state.showAadharBtn = action.payload;
    },
  },
});

export const {
  setPageError,
  setPageSuccess,
  setTimer,
  setOtp,
  setOtpError,
  setShowAadharBtn,
} = ckycOtpInputSlice.actions;

export default ckycOtpInputSlice.reducer;

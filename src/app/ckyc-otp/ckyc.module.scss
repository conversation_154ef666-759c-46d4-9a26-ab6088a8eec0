@import '../scss/variable.scss';

.ckyc_conainer {
    padding: 16px;
    padding-top: 0;
    min-height: calc(100vh - 120px);
    margin-top: -20px;

    .description {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 20px;

        span {
            font-weight: 700;
        }
    }

    .otp_sent_text {
        font-size: 14px;
        font-weight: 500;
        color: #7b7b7b;
        margin-top: 10px;
    }

    .timer_container {
        font-size: 12px;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        margin-top: 20px;

        .time {
            text-align: center;
            margin-bottom: 20px;

            span {
                color: #1C43DF;
            }
        }

        .try_with_aadhar_container {
            text-align: center;

            .try_with_aadhar {
                font-size: 14px;
                color: #1C43DF;
            }
        }
    }

    .why_otp_required {
        font-size: 14px;
        font-weight: 500;
    }

    .safe_secure_data {
        display: flex;
        column-gap: 8px;
        margin: 20px 0 20px 0;
        justify-content: center;
        align-items: center;

        p {
            font-size: 12px;
            font-weight: 500;
            color: #7b7b7b;
        }
    }

    .footer {
        position: fixed;
        right: 16px;
        left: 16px;
        bottom: 0;

        .verify_btn {
            width: 100% !important;
        }
    }
}
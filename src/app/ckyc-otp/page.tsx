"use client";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import LoadingComp from "../component/loader";
import PageHeader from "../components/PageHeader";
import PageWrapper from "../components/PageWrapper";
import ToastMessage from "../components/ToastMessage/ToastMessage";
import SHIELD_ICON from "../images/shield-icon.svg";
import { ApiResponse } from "../store/interface/apiInterface";
import { setCommonData } from "../store/slices/commonSlice";
import store, { RootState } from "../store/store";
import { apiRequest, fetchCommonApi } from "../utils/api";
import { ENDPOINTS } from "../utils/endpoints";
import styles from "../verify-email/verify.module.scss";
import ckycStyles from "./ckyc.module.scss";
import {
  setOtp,
  setOtpError,
  setPageError,
  setPageSuccess,
  setShowAadharBtn,
  setTimer,
} from "./ckycInputSlice";

const CkycOtpInput = () => {
  const dispatch = useDispatch();
  const { pageerror, pagesuccess, otp, showAadharBtn, otperror, timer } =
    useSelector((state: RootState) => state.ckycOtp);
  const [loader, setLoader] = useState<boolean>(false);
  const [disableResend, setDisableResend] = useState<boolean>(false);
  const [keyboardOpen, setKeyboardOpen] = useState<boolean>(false); // <-- NEW
  const inputRefs = useRef<(HTMLInputElement | null)[]>(Array(6).fill(null));
  // OTP COMPONENT RELATED
  // Ensure OTP always has 6 characters (empty if not set)
  const otpArray = Array.from({ length: 6 }, (_, i) => otp?.[i] || "");

  // Handle OTP input changes
  const handleChange = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value.replace(/[^0-9]/g, ""); // Allow only numbers
    if (!value) return;

    const newOtp = [...otpArray];
    newOtp[index] = value.charAt(0); // Store only one digit
    dispatch(setOtp(newOtp.join("")));

    // Move focus to the next field
    if (index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle Backspace: Clear input and move focus back
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    dispatch(setOtpError(false));
    if (e.key === "Backspace") {
      const newOtp = [...otpArray];

      if (otpArray[index]) {
        // Clear current field
        newOtp[index] = "";
        dispatch(setOtp(newOtp.join("")));
      } else if (index > 0) {
        // Move focus to previous field and clear it
        newOtp[index - 1] = "";
        dispatch(setOtp(newOtp.join("")));
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  // --------XXXXXX---------XXXXXXX-------------END

  // RESENDING OTP FUNCTION
  const reSendOtp = async (): Promise<void> => {
    setLoader(true);
    dispatch(setPageError(""));
    dispatch(setPageSuccess(""));
    try {
      const response = await apiRequest<ApiResponse>(
        "POST",
        ENDPOINTS.ckycOtpResend,
        { ckyc_id: localStorage.getItem("ckycTrnsactionId") }
      );
      if (response.mainData && response.mainData.status === "success") {
        dispatch(setPageSuccess("OTP sent successfully"));
      }
      if (response.mainData && response.mainData.status === "error") {
        dispatch(setPageError(response.mainData?.message));
      }
      if (response.mainData && response.mainData.status === "disburse") {
        dispatch(setPageError(response.mainData.message));
        setDisableResend(true);
      }
      if (response.error) {
        dispatch(setPageError(response.error));
      }
    } catch (error) {
      dispatch(setPageError("Error in sending OTP"));
    } finally {
      setLoader(false);
    }
  };

  // Handle Resend OTP
  const handleResend = () => {
    dispatch(setOtp(null)); // Clear Redux OTP state
    dispatch(setPageError(null));
    dispatch(setTimer(90)); // Reset timer
    dispatch(setShowAadharBtn(true));
    // Call your OTP generation API here
    reSendOtp();
  };

  // Timer countdown logic
  useEffect(() => {
    if (timer <= 0) {
      // dispatch(setOtpSent(false)); // Enable Resend button
      return;
    }

    const countdown = setInterval(() => {
      dispatch(setTimer(Math.max(timer - 1, 0)));
    }, 1000);

    return () => clearInterval(countdown);
  }, [timer, dispatch]);

  // Format seconds into MM:SS
  const formatTime = () => {
    const minutes = Math.floor(timer / 60);
    const seconds = timer % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  };

  // VERIFICATION OF OTP
  const handleVerifyOTP = async () => {
    setLoader(true);
    dispatch(setPageError(""));
    try {
      const response = await apiRequest<ApiResponse>(
        "POST",
        ENDPOINTS.ckycFetch,
        { otp, ckyc_id: localStorage.getItem("ckycTrnsactionId") }
      );
      if (response.mainData?.status === "retry") {
        dispatch(setOtpError(true));
        dispatch(setShowAadharBtn(true));
        dispatch(
          setPageError(response.mainData?.message || "Please try again.")
        );
        setLoader(false);
        return;
      }
      const commonData = await fetchCommonApi();
      store.dispatch(setCommonData(commonData));
      setLoader(false);
    } catch (error) {
      const commonData = await fetchCommonApi();
      store.dispatch(setCommonData(commonData));
      setLoader(false);
    }
  };

  const handleCKYCwithAdhar = async () => {
    setLoader(true)
    try {
      const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.redirectToDigi);
      if(response.mainData?.success){
        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
      }else{
        console.log("error in /redirect/digilocker");
      }
    } catch (error) {
      console.log("error in /redirect/digilocker api", error);
    }
  };

  return (
    <PageWrapper>
      <PageHeader call={false} title="Instant KYC verification" />
      <div className={ckycStyles.ckyc_conainer}>
        <p className={ckycStyles.description}>
          We will securely fetch your records from the Central KYC registry.
          <span> Just Verify via OTP</span>
        </p>

        <div className={`${styles.otpInputs}`}>
          {otpArray.map((digit, index) => {
            // Find the last filled digit index (or first empty)
            const lastFilledIndex = otpArray.findIndex((d) => d === "");
            const lastActiveIndex = lastFilledIndex === -1 ? 5 : lastFilledIndex - 1;
            return (
              <input
                key={index}
                type="text"
                value={digit}
                maxLength={1}
                inputMode="numeric"
                placeholder=" "
                className={`${styles.otpInput} form-control`}
                style={{
                  border: otperror ? "1px solid red" : "",
                }}
                ref={(el) => {
                  if (el) inputRefs.current[index] = el;
                }}
                onChange={(e) => handleChange(index, e)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onFocus={() => {
                  setKeyboardOpen(true);
                  // If user tries to focus a previous digit, move focus to last filled/next empty
                  if (index < lastActiveIndex) {
                    setTimeout(() => {
                      const targetIndex = lastFilledIndex === -1 ? 5 : lastFilledIndex;
                      inputRefs.current[targetIndex]?.focus();
                    }, 0);
                  }
                }}
                onBlur={() => {
                  setTimeout(() => {
                    const stillFocused = inputRefs.current.some(
                      (input) => input && document.activeElement === input
                    );
                    if (!stillFocused) setKeyboardOpen(false);
                  }, 10);
                }}
              />
            );
          })}
        </div>

        <p className={ckycStyles.otp_sent_text}>
          CKYC has sent a 6 digit OTP to your mobile no
        </p>

        {/* Timer & Resend OTP */}
        <div className={ckycStyles.timer_container}>
          {!disableResend && (
             timer > 0 ? (
              <span className={ckycStyles.time}>
                Resend OTP in <span> {formatTime()}</span>
              </span>
            ) : (
              <span onClick={handleResend} className={`link ${ckycStyles.time}`}>
                Resend
              </span>
            )
          )}
         

          {showAadharBtn && (
            <div className={ckycStyles.try_with_aadhar_container}>
              <p>Having trouble with OTP? </p>
              <p onClick={handleCKYCwithAdhar} className={ckycStyles.try_with_aadhar}>Complete KYC with Aadhaar</p>
            </div>
          )}
        </div>

        {/* ------------- CENTER ---------- */}
        <div style={{height: "calc(100vh - 700px)"}}/>

        {/* ------------------- FOOTER---------------- */}

        <div className={ckycStyles.footer}>
          <div style={{display: keyboardOpen ? "none" : "block"}}>
            <p className={ckycStyles.why_otp_required}>
              Why is this OTP required?
            </p>

            <p style={{ fontSize: 12, fontWeight: 500, color: "#7b7b7b" }}>
              Accessing your KYC from CERSAI helps us instantly verify your
              details. Enter the OTP to proceed. <br /> <br /> By proceeding, I
              consent to Akara initiating and completing the KYC checks as per
              applicable regulatory guidelines.
            </p>
            <div className={ckycStyles.safe_secure_data}>
              <Image src={SHIELD_ICON} alt="Shield Icon" width={24} height={24} />
              <p style={{ color: "#1F1F1F" }}>Your data is 100% safe & secure</p>
            </div>
          </div>
          <button
            type="button"
            onClick={handleVerifyOTP}
            className={`btn btn-primary ${ckycStyles.verify_btn} ${
              !otp || otp.length < 6 ? "disabled" : ""
            }`}
          >
            Verify OTP
          </button>
        </div>
      </div>

      {pageerror || pagesuccess ? (
        <ToastMessage color={pagesuccess ? "green" : "red"}>
          {pagesuccess ? pagesuccess : pageerror}
        </ToastMessage>
      ) : null}
      {loader && <LoadingComp faded />}
    </PageWrapper>
  );
};

export default CkycOtpInput;

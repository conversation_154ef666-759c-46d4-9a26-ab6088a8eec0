"use client";

import React, { useState, useEffect } from "react";

const KeyboardAwareLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [keyboardHeight, setKeyboardHeight] = useState(false);

  useEffect(() => {
    const isMobile = /Android/i.test(navigator.userAgent);
    if (!isMobile) return;

    const handleFocusIn = (event: FocusEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        setKeyboardHeight(true); // Adjust based on testing
      }
    };

    const handleFocusOut = () => {
      setKeyboardHeight(false);
    };

    window.addEventListener("focusin", handleFocusIn);
    window.addEventListener("focusout", handleFocusOut);

    return () => {
      window.removeEventListener("focusin", handleFocusIn);
      window.removeEventListener("focusout", handleFocusOut);
    };
  }, []);

  return (
    <div>
      {children}
    </div>
  );
};

export default KeyboardAwareLayout;

.loanspage {
    padding: 0 16px;
    color: #000;

    .header {
        padding-left: 0;
        padding-right: 0;
        background: transparent !important;
        padding-top: 40px;

        >h1 {
            font-size: 24px !important;
            font-weight: 700;
            display: flex;
            justify-content: space-between;
            align-items: center;

            a {
                font-weight: 600;
                font-size: 12px;
                color: #0064E0;
            }
        }
    }

    .sectionheader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30px;

        h2 {
            font-size: 18px;
            font-weight: 600;
        }

        .detailslink {
            color: #0064E0;
            font-size: 12px;
            font-weight: 500;
        }
    }

    .creditline {
        .cardcontent {
            background: url(../images/giftbox.png) no-repeat;
            background-size: 91px 74px;
            background-position: top right;
        }
    }

    .card {
        border-radius: 10px;
        padding: 16px;
        margin-top: 15px;
        background-color: #f6f6f6;

        &.creditcard {
            background-color: #2A2851;
            color: white;
        }

        &.greencard {
            background-color: #e6ffe6;
        }

        &.greycard {
            background-color: #f1f1f1;
        }

        .cardcontent {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;

        }
    }

    .label {
        font-weight: 500;
        font-size: 12px;
        color: #fff;
    }

    .amount {
        font-size: 31px;
        font-weight: 600;
    }

    .subtext {
        font-size: 12px;
        font-weight: 400;
        color: #ccc;
    }

    .warning {
        display: flex;
        align-items: center;
        font-size: 10px;
        color: #FF552B;

        img {
            margin-right: 8px;
        }

        strong {
            &:last-child {
                color: #fff;
            }
        }
    }

    .increasebox,
    .paybox {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-radius: 8px;
        background-color: white;
        color: black;

        span {
            font-size: 14px;
            font-weight: 500;
            color: #000;
        }
    }

    .increasebox {
        background: #FFE6DC;

        span {
            max-width: 132px;
        }

        button {
            width: 137px;
            height: 26px;
            border-radius: 73px;
            background: #1F1F1F;
            padding: 0;
            font-size: 12px;
            font-weight: 500;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                margin-right: 8px;
            }
        }
    }

    .paybox {
        background: #E4FCE4;

        span {
            max-width: 194px;
        }

        button {
            width: 73px;
            height: 26px;
            background: #fff;
            border-radius: 73px;
            border: 1px solid #000000;
            font-size: 12px;
            font-weight: 500;
            color: #1F1F1F;
            padding: 0;
        }
    }

    button {
        &.withdrawcashbtn {
            width: 100%;
            height: 47px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 9px;
            background: #fff !important;
            font-size: 14px;
            color: #1F1F1F !important;
            font-weight: 600;
            margin-top: 14px;
        }
    }

    .btn,
    .btnoutline {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 5px;
        cursor: pointer;

        &.btn {
            background-color: black;
            color: white;
        }

        &.btnoutline {
            background-color: transparent;
            border: 1px solid black;
            color: black;
        }
    }

    .btn.fullwidth {
        width: 100%;
    }

    .loanheader,
    .loandetails,
    .loanfooter {
        display: flex;
        justify-content: space-between;
        font-size: 0.875rem;
    }

    .tag {
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;

        &.blue {
            color: #0056d2;
        }

        &.green {
            color: #228b22;
        }

        &.grey {
            color: #888;
        }
    }

    .boldtext {
        font-weight: 600;
    }

    .personalloan {
        margin-top: 24px;

        h2 {
            font-size: 24px !important;
            font-weight: 700;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card.creditcard {
            background: #fff;
            border: 1px solid #E6E6E6;
            border-radius: 8px;
            position: relative;
            z-index: 1;

            .cardcontent {
                background: url(../images/personalloanbg.png) no-repeat;
                background-size: 168px 104px;
                background-position: center right;

                .label {
                    font-weight: 400;
                    font-size: 14px;
                    color: #000;
                }

                h3 {
                    font-weight: 600;
                    font-size: 24px;
                    color: #000;
                }

                .subtext {
                    color: #00000080;
                    font-size: 8px;
                    font-weight: 500;
                }

                .loandetails {
                    display: flex;
                    max-width: 209px;
                    font-weight: 400;
                    color: #7B7B7B;
                    font-size: 12px;

                    p {
                        display: flex;
                        flex-flow: column;

                        span {
                            font-weight: 500;
                            color: #1F1F1F;
                        }
                    }
                }
            }

            &+.loanfooter {
                background: #F5F5F5;
                padding-top: 24px;
                margin-top: -12px;
                padding-bottom: 12px;
                padding-left: 16px;
                padding-right: 16px;
                position: relative;
                border-radius: 9px;

                p {
                    font-weight: 500;
                    font-size: 12px;
                }

                a {
                    color: #0064E0;
                    font-weight: 500;
                    font-size: 12px;
                }
            }
        }



    }

    .exploreproducts {
        margin-top: 24px;

        h2 {
            font-size: 24px !important;
            font-weight: 700;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card.creditcard {
            border: 1px solid #E6E6E6;
            border-radius: 8px;
            position: relative;
            z-index: 1;
            background:#fff url(../images/coins15.png) no-repeat;
            background-size: 108px 70px;
            background-position: bottom 16px right 16px;

            .tag.blue {
                font-weight: 600;
                color: #1C43DF;
                font-size: 12px;

                &+p {
                    font-size: 16px;
                    line-height: 24px;
                    color: #1F1F1F;
                    max-width: 244px;
                    font-weight: 700;
                }
            }

            button {
                width: 98px;
                height: 28px;
                background: #1F1F1F;
                border-radius: 66px;
                font-weight: 600;
                font-size: 12px;
                padding: 0;
                color: #fff;
            }
            &.greenbg {
                background: #D3FFCB url(../images/creditreportimg.svg) no-repeat;
                background-size: 100px 95px;
                background-position: right 16px center;
                border-color: #A8DF93;
            }
        }
    }

    .creditbuilderbox {
        margin-top: 20px;
        padding: 24px 12px 40px 12px;
        background: #F0F0F0 url(../images/dhan.png) no-repeat;
        background-position: bottom 13px right 16px;
        border-radius: 12px;

        .title {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            line-height: 16.8px;
            font-size: 14px;
            color: #555555;
            text-transform: uppercase;
        }

        h2 {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            line-height: 24px;
            font-size: 20px;
            color: var(--black);
            margin-top: 10px;
            padding-right: 60px;
            max-width: 280px;
        }

        p {
            margin-top: 9px;
            font-family: var(--font-mona-sans);
            font-weight: 600;
            line-height: 24px;
            font-size: 16px;
            color: #000;
        }

        .circularbtn {
            margin-top: 15px;
            max-width: 115px;
            padding: 8px 16px;
            border-radius: 66px;
            border: 1px solid #000000;
            background: #fff;
            font-family: var(--font-mona-sans);
            font-weight: 600;
            line-height: 24px;
            font-size: 16px;
            color: #000;
            cursor: pointer;
        }
    }
}
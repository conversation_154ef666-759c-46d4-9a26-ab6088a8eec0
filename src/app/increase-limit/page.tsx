"use client"
import React from "react";
import styles from "./increaseamount.module.scss";
import PageHeader from "../components/PageHeader";
import PageWrapper from "../components/PageWrapper";
import Image from "next/image"
import redmeter from '../images/redmeter.svg'
import whitemeter from '../images/whitemeter.svg'
import moveright from '../images/moveright.svg'

export default function LoansPage() {
    return (
        <PageWrapper>
            <div className={styles.loanspage}>
                <div className={styles.header}>
                    <h1>
                        Loans
                        <a href="#">Help</a>
                    </h1>
                    {/* <PageHeader title="Loans" call={false} /> */}
                </div>



                <section className={styles.creditline}>
                    <div className={styles.sectionheader}>
                        <h2>Credit Line</h2>
                        <a href="#" className={styles.detailslink}>Details</a>
                    </div>
                    <div className={`${styles.card} ${styles.creditcard}`}>
                        <div className={styles.cardcontent}>
                            <p className={styles.label}>Limit Available</p>
                            <h3 className={styles.amount}>₹20,000</h3>
                            <p className={styles.subtext}>out of ₹2,00,000</p>
                            <p className={styles.warning}><Image src={redmeter} alt="" /> Last &nbsp;<strong>20%</strong>&nbsp; limit left, &nbsp;<strong>Increase limit upto 5 Lakh</strong></p>
                            <div className={styles.increasebox}>
                                <span>Increase available limit up to 5 lakh</span>
                                <button className={styles.btn}><Image src={whitemeter} alt="" /> Increase now</button>
                            </div>
                            <div className={styles.paybox}>
                                <span>Pay full by &lt; date &gt; & enjoy 0% interest free credit period</span>
                                <button className={styles.btnoutline}>Pay full</button>
                            </div>
                            <button className={`${styles.btn} ${styles.fullwidth} ${styles.withdrawcashbtn}`}>Withdraw Cash</button>
                        </div>
                    </div>
                </section>

                <section className={styles.personalloan}>
                    <h2>Personal Loan</h2>
                    <div className={`${styles.card} ${styles.creditcard}`}>
                        <div className={styles.cardcontent}>
                            <div className={styles.loanheader}>
                                <div>
                                    <p className={styles.label}>Outstanding Amount</p>
                                    <h3 className={styles.amount}>₹50,000</h3>
                                    <p className={styles.subtext}>Partner name: HT MEDIA</p>
                                </div>
                                <div className={styles.arrow}><Image src={moveright} alt="" /></div>
                            </div>
                            <div className={styles.loandetails}>
                                <p>Loan amount <span>₹2,00,000</span></p>
                                <p>Remaining duration <span>48 months</span></p>
                            </div>

                        </div>
                    </div>
                    <div className={styles.loanfooter}>
                        <p>₹2,000 due on 23 Feb</p>
                        <a href="#" className={styles.payearly}>Pay early</a>
                    </div>
                </section>

                <section className={styles.exploreproducts}>
                    <h2>Explore more product</h2>

                    <div className={`${styles.card} ${styles.creditcard}`}>
                        <div className={styles.cardcontent}>
                            <p className={`${styles.tag} ${styles.blue}`}>CASH AGAINST MUTUAL FUNDS</p>
                            <p>Use your mutual funds to get instant cash</p>
                            <button className={styles.btnoutline}>Check now</button>
                        </div>
                    </div>

                    
                </section>

                
            </div>
        </PageWrapper>
    );
}

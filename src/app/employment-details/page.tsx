'use client';

import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import ShieldIcon from '../images/shield-icon.svg';
import { useDispatch, useSelector } from 'react-redux';
import store, { AppDispatch, RootState } from '../store/store';
import SelectDropdown from '../components/SelectDropdown';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import styles from './employment.module.scss'
import { setPageError, setPageSuccess } from '../register/register.slice';
import { apiRequest, fetchCommonApi } from '../utils/api';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { ENDPOINTS } from '../utils/endpoints';
import PageWrapper from '../components/PageWrapper';

import { setCommonData } from "../store/slices/commonSlice";
import LoadingComp from '../component/loader';
import CtEvents from '../utils/Ctevents';
import { useRouter } from "next/navigation";


function EmploymentDetails() {
    const router = useRouter();

    const dispatch = useDispatch<AppDispatch>();
    const { isChecked, isForceModalOpen, isReferalModalOpen, defenceCheck, referalCode, pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [loaded, setLoaded] = useState<boolean>(false)
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);


    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
    }
    const validationSchema = Yup.lazy((values) => {
        return Yup.object({
            employmentType: Yup.string().required("Occupation type is required"),
            companyName: values.employmentType === "Salaried"
                ? Yup.string().required("Company Name is required")
                : Yup.string().nullable(),
            salaryDate: values.employmentType === "Salaried"
                ? Yup.string().required("Salary Date is required")
                : Yup.string().nullable(),
            entityName: values.employmentType === "Self Employed"
                ? Yup.string().required("Entity Name is required")
                : Yup.string().nullable(),
            gstRegistered: values.employmentType === "Self Employed"
                ? Yup.string().required("GST registration is required")
                : Yup.string().nullable(),
            annualIncome: Yup.string().required("Annual Income is required"),
        });
    });

    const formik = useFormik({
        initialValues: {
            employmentType: '',
            companyName: '',
            salaryDate: '',
            annualIncome: '',
            entityName: '',
            gstRegistered: ''
        },
        validationSchema,
        onSubmit: async (values) => {
            // debugger;
            setLoaded(false)
            const salaried_data = {
                "salary_date": values.salaryDate,
                "company_name": values.companyName,
                "annual_income": values.annualIncome
            }
            const self_employed_data = {
                "is_gst_registered": values.gstRegistered == 'No' ? "0" : "1",
                "entity_name": values.entityName,
                "annual_income": values.annualIncome
            }
            try {
                const payload = {
                    // "customer_id": 58912863,
                    "profession_type": values.employmentType === 'Salaried' ? 'SALARIED' : 'SELF_EMPLOYED',
                    [values.employmentType === 'Salaried' ? "salaried_data" : "self_employed_data"]:
                        values.employmentType === 'Salaried' ? salaried_data : self_employed_data
                };

                // Pass SaveBasicDetailsResponse as the type parameter
                const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.update_professional_details, payload);
                if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                    // dispatch(setPageSuccess('Data updated'))
                    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                    const event_name = 'LOC_Emp Details Submitted'
                    const productcode = localStorage.getItem("product_code")

                    if (values.employmentType === "Salaried") {
                        const event_property = { "Employment Type": payload.profession_type, "Salary Date": salaried_data.salary_date, "Entity Name": salaried_data.company_name, "Annual Income": salaried_data.annual_income, "User flow": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Product category": productcode, "Source": cteventsfromstorage?.source }
                        setCtdata({ event_name, event_property })
                    }
                    else {
                        const event_property = { "Employment Type": payload.profession_type, "GST Registered": self_employed_data.is_gst_registered, "Entity Name": self_employed_data.entity_name, "Annual Income": salaried_data.annual_income, "User flow": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Product category": productcode, "Source": cteventsfromstorage?.source }
                        setCtdata({ event_name, event_property })
                    }



                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));

                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }
                    else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {


                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {

                        dispatch(setPageError(response.error))
                    } else {

                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            } catch (error) {

                //dispatch(setPageError('Error submitting basic details'))
                console.error("Error submitting basic details:", error);
                //alert("Failed to save basic details. Please try again.");
            } finally {
                setTimeout(() => {
                    setLoaded(true)
                }, 2000);
            }

        },
    });
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);

    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "Employement Type", "User flow": cteventsfromstorage.isNonSTP, "Product category": productcode, "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })

    }, [])

    const dayOptions = Array.from({ length: 31 }, (_, i) => {
        const num = i + 1;
        const suffix = num === 1 || num === 21 || num === 31 ? "st" : num === 2 || num === 22 ? "nd" : num === 3 || num === 23 ? "rd" : "th";
        return { name: `${num}${suffix}` }; // Change `label` to `name`
    });
    return (
        <PageWrapper>
            {loaded ?
                <div className={`registration-page ${styles.employmentpage}`}>
                    <PageHeader title="Select your employment type" />
                    <div className="page-content">
                        <form onSubmit={formik.handleSubmit}>

                            {/* Occupation Type */}
                            <div className="input-wrapper radio-wrapper">
                                <div className={`radios ${styles.radios}`}>
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="salaried"
                                            name="employmentType"
                                            value="Salaried"
                                            checked={formik.values.employmentType === 'Salaried'}
                                            onChange={() => formik.setFieldValue('employmentType', 'Salaried')}
                                        />
                                        <label htmlFor="salaried" className="noabs">Salaried</label>
                                    </div>
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="selfEmployed"
                                            name="employmentType"
                                            value="Self Employed"
                                            checked={formik.values.employmentType === 'Self Employed'}
                                            onChange={() => formik.setFieldValue('employmentType', 'Self Employed')}
                                        />
                                        <label htmlFor="selfEmployed" className="noabs">Self Employed</label>
                                    </div>
                                </div>
                                {formik.errors.employmentType && formik.touched.employmentType && (
                                    <div className="error">{formik.errors.employmentType}</div>
                                )}
                            </div>

                            {/* Conditional Fields */}
                            {formik.values.employmentType === 'Salaried' && (
                                <>
                                    <div className="input-wrapper">
                                        <SelectDropdown
                                            name="salaryDate"
                                            options={dayOptions}
                                            labelText="Salary Date"
                                            value={formik.values.salaryDate}
                                            onChange={(option: string | { name: string }) => {
                                                if (typeof option === 'string') {
                                                    formik.setFieldValue('salaryDate', option)
                                                } else if ('name' in option) {
                                                    formik.setFieldValue('salaryDate', option.name)
                                                }
                                            }}
                                        />
                                        {formik.errors.salaryDate && formik.touched.salaryDate && (
                                            <div className="error">{formik.errors.salaryDate}</div>
                                        )}
                                    </div>
                                    <div className="input-wrapper">
                                        <input
                                            type="text"
                                            name="companyName"
                                            placeholder=" "
                                            value={formik.values.companyName}
                                            // onChange={formik.handleChange}
                                            onChange={(e) => {
                                                const value = e.target.value;
                                                const formattedValue = value.replace(/[^A-Za-z\s]/g, "");
                                                formik.setFieldValue("companyName", formattedValue);
                                            }}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>Company Name</label>
                                        {formik.errors.companyName && formik.touched.companyName && (
                                            <div className="error">{formik.errors.companyName}</div>
                                        )}
                                    </div>
                                </>
                            )}
                            {formik.values.employmentType === 'Self Employed' && (
                                <>
                                    <div className="input-wrapper radio-wrapper">
                                        <label className="radio-label">Are you GST registered?</label>
                                        <div className="radios">
                                            <div className="radio">
                                                <input
                                                    type="radio"
                                                    id="yes"
                                                    name="gstRegistered"
                                                    value="Yes"
                                                    checked={formik.values.gstRegistered === 'Yes'}
                                                    onChange={() => formik.setFieldValue('gstRegistered', 'Yes')}
                                                />
                                                <label htmlFor="yes" className="noabs">Yes</label>
                                            </div>
                                            <div className="radio">
                                                <input
                                                    type="radio"
                                                    id="no"
                                                    name="gstRegistered"
                                                    value="No"
                                                    checked={formik.values.gstRegistered === 'No'}
                                                    onChange={() => formik.setFieldValue('gstRegistered', 'No')}
                                                />
                                                <label htmlFor="no" className="noabs">No</label>
                                            </div>
                                        </div>
                                        {formik.errors.gstRegistered && formik.touched.gstRegistered && (
                                            <div className="error">{formik.errors.gstRegistered}</div>
                                        )}
                                    </div>
                                    <div className="input-wrapper">
                                        <input
                                            type="text"
                                            name="entityName"
                                            placeholder=" "
                                            value={formik.values.entityName}
                                            onChange={(e) => {
                                                const value = e.target.value ? e.target.value.replace(/[^A-Za-z0-9\s]/g, '') : '';
                                                formik.setFieldValue('entityName', value);
                                            }}
                                            onBlur={formik.handleBlur}
                                            className="form-control"
                                        />
                                        <label>Entity Name</label>
                                        {formik.errors.entityName && formik.touched.entityName && (
                                            <div className="error">{formik.errors.entityName}</div>
                                        )}
                                    </div>
                                </>
                            )}

                            {/* Annual Income (Radio Buttons) */}
                            {(formik.values.employmentType === 'Self Employed' || formik.values.employmentType === 'Salaried') && <><div className="input-wrapper radio-wrapper">
                                <label className="radio-label">Annual Income</label>
                                <div className="radios">
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="income1"
                                            name="annualIncome"
                                            value="3 - 5 Lakh"
                                            checked={formik.values.annualIncome === '3 - 5 Lakh'}
                                            onChange={() => formik.setFieldValue('annualIncome', '3 - 5 Lakh')}
                                        />
                                        <label htmlFor="income1" className='noabs'>3 - 5 Lakh</label>
                                    </div>
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="income2"
                                            name="annualIncome"
                                            value="5 - 10 Lakh"
                                            checked={formik.values.annualIncome === '5 - 10 Lakh'}
                                            onChange={() => formik.setFieldValue('annualIncome', '5 - 10 Lakh')}
                                        />
                                        <label htmlFor="income2" className='noabs'>5 - 10 Lakh</label>
                                    </div>
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="income3"
                                            name="annualIncome"
                                            value="10 - 15 Lakh"
                                            checked={formik.values.annualIncome === '10 - 15 Lakh'}
                                            onChange={() => formik.setFieldValue('annualIncome', '10 - 15 Lakh')}
                                        />
                                        <label htmlFor="income3" className='noabs'>10 - 15 Lakh</label>
                                    </div>
                                    <div className="radio">
                                        <input
                                            type="radio"
                                            id="income4"
                                            name="annualIncome"
                                            value="15+ Lakh"
                                            checked={formik.values.annualIncome === '15+ Lakh'}
                                            onChange={() => formik.setFieldValue('annualIncome', '15+ Lakh')}
                                        />
                                        <label htmlFor="income4" className='noabs'>15+ Lakh</label>
                                    </div>
                                </div>
                                {formik.errors.annualIncome && formik.touched.annualIncome && (
                                    <div className="error">{formik.errors.annualIncome}</div>
                                )}
                            </div>

                                {/* Footer */}

                                <div className="bottom-footer p-0 mt-auto">
                                    <p className="secure-tag">
                                        <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                    </p>
                                    <button type="submit" className={`btn btn-primary ${!formik.isValid || !formik.dirty ? 'disabled' : ''}`}>Continue</button>
                                </div>
                            </>
                            }
                        </form>
                    </div>

                </div>
                :

                <LoadingComp />
            }
            <CtEvents data={ctData} />
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
}

export default EmploymentDetails;
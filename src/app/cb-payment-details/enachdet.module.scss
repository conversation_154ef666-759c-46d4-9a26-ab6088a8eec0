@import '../scss/variable.scss';

.pageContent {
    min-height: calc(100vh - 140px);
}

.enachDetails {
    list-style: none;
    margin: 0;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 400;
    border-radius: 8px;
    border: 1px solid #eeeeee;
    padding: 0px 16px;

    li {
        padding: 15px 0px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:not(:last-of-type) {
            border-bottom: 1px solid #eeeeee;
        }

        &:last-of-type {
            font-weight: 700;
        }

        span:nth-of-type(2) {
            font-weight: 700;
        }
    }
}

.checked {
    &:before {
        background-image: url('../images/bluecheck.svg');
        background-size: 24px;
        background-position: -3px;
        border: none;
    }
}

p.changepage {
    font-family: var(--font-mona-sans);
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    padding: 16px 0;
    cursor: pointer;
}

.modalItems {
    .modalItem {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        position: relative;
        text-align: left;

        &:after {
            content: "";
            background-color: #e9e9e9;
            width: calc(100% - 60px);
            height: 1px;
            position: absolute;
            bottom: -1px;
            right: 0;
        }

        >img {
            width: 48px;
            height: 48px;
            margin-right: 12px;
        }

        >div {
            padding-bottom: 12px;

            h3 {
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
            }

            p {
                font-family: var(--font-mona-sans);
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
            }
        }

        &:last-child {
            &::after {
                display: none;
            }
        }
    }
}

.creditReportText {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: var(--font-mona-sans);
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    color: var(--black);
}

.toggleWrapper {
    display: flex;
    margin: 24px 0;
    padding-left: 60px;
    align-items: center;
    font-family: var(--font-mona-sans);
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    color: var(--black);

    img {
        margin-right: 10px;
        width: 23px;
        height: 14px;
    }

    label {
        margin-left: 15px
    }
}
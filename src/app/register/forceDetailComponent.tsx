'use client';

import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store/store';
import Image from 'next/image';
import UploadIcon from '../images/uploadIcon.svg';
import uploadedIcon from '../images/valid-check.svg'
import SelectDropdown from '../components/SelectDropdown';
import { closeUploadModal, setPageError, setPageSuccess, setUploadedFile } from './register.slice';
import { useRef } from 'react';
import { apiRequest } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import { ForceData } from './register.interface';
import React from 'react';
import { ENDPOINTS } from '../utils/endpoints';

function ForceDetailsForm() {
    const dispatch = useDispatch<AppDispatch>();
    const { uploadedImage, defenceCheck } = useSelector((state: RootState) => state.register);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const MAX_FILE_SIZE = 3 * 1024 * 1024;
    const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/jpg'];

    const validateAndUploadImage = (file: File) => {
        if (!file) return;

        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
            alert('Only JPG, JPEG, and PNG files are allowed.');
            return;
        }

        if (file.size > MAX_FILE_SIZE) {
            alert('File size must be less than 2MB.');
            return;
        }

        const reader = new FileReader();
        reader.onloadend = () => {
            dispatch(setUploadedFile(reader.result as string));
            formik.setFieldValue('cardImage', file);
        };
        reader.readAsDataURL(file);
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        validateAndUploadImage(file as File);
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
    };

    const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        const file = event.dataTransfer.files?.[0];
        validateAndUploadImage(file as File);
    };

    const validationSchema = Yup.object({
        forceName: Yup.string().required('Force Name is required'),
        designation: Yup.string().required('Designation is required'),
        rank: Yup.string().required('Rank is required'),
        cardNumber: Yup.string()
            .required('Canteen Card / Employee ID Number is required')
            .matches(/^[A-Za-z0-9]+$/, 'Invalid ID format'),
        cardImage: Yup.mixed().required('Card image is required'),
    });

    const handleSubmit = async (values: ForceData) => {
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))
        try {
            const payload = {
                // customer_id: 58912863,
                loan_id: 0,
                not_defence_customer: defenceCheck ? false : true,
                force_data: {
                    force_name: values.forceName,
                    designation: values.designation,
                    rank: values.rank,
                    canteen_id: values.cardNumber,
                    documents: {
                        doc_type: 'Canteen Card',
                        doc_content: uploadedImage,
                        doc_format: values.cardImage.type.includes('/') ? values.cardImage.type.split('/')[1] : values.cardImage.type
                    }
                }
            }
            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.addsentineldetails, payload);
            if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success' || response.mainData.status?.toString().toLowerCase() === 'true')) {
                dispatch(setPageSuccess(response.mainData.data.message || 'Force details update successful'))
                dispatch(closeUploadModal())
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                    dispatch(setPageError(response.mainData.error_message || 'Error in uploading file'))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        }
    };

    const formik = useFormik({
        initialValues: {
            forceName: '',
            designation: '',
            rank: '',
            cardNumber: '',
            cardImage: '',
        },
        validationSchema,
        onSubmit: (values) => {
            handleSubmit(values)
        },
    });

    return (
        <form onSubmit={formik.handleSubmit} style={{ textAlign: 'left' }}>
            {/* Force Name */}
            <div className="input-wrapper">
                <SelectDropdown
                    name="forceName"
                    id="forceName"
                    options={['Army', 'Navy', 'Air Force']}
                    labelText="Force Name"
                    value={formik.values.forceName}
                    onChange={(option) => formik.setFieldValue('forceName', option)}
                />
                {formik.touched.forceName && formik.errors.forceName && (
                    <div className="error">{formik.errors.forceName}</div>
                )}
            </div>

            {/* Designation */}
            <div className="input-wrapper">
                <SelectDropdown
                    name="designation"
                    id="designation"
                    options={['Officer', 'Soldier']}
                    labelText="Designation"
                    value={formik.values.designation}
                    onChange={(option) => formik.setFieldValue('designation', option)}
                />
                {formik.touched.designation && formik.errors.designation && (
                    <div className="error">{formik.errors.designation}</div>
                )}
            </div>

            {/* Rank */}
            <div className="input-wrapper">
                <SelectDropdown
                    name="rank"
                    id="rank"
                    options={['Captain', 'Lieutenant', 'Major']}
                    labelText="Rank"
                    value={formik.values.rank}
                    onChange={(option) => formik.setFieldValue('rank', option)}
                />
                {formik.touched.rank && formik.errors.rank && (
                    <div className="error">{formik.errors.rank}</div>
                )}
            </div>

            {/* Card Number */}
            <div className="input-wrapper">
                <input
                    type="text"
                    name="cardNumber"
                    placeholder=" "
                    value={formik.values.cardNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    className="form-control"
                />
                <label>Canteen Card / Employee ID Number*</label>
                {formik.touched.cardNumber && formik.errors.cardNumber && (
                    <div className="error">{formik.errors.cardNumber}</div>
                )}
            </div>

            {/* File Upload - Drag & Drop */}
            <div className="input-wrapper">
                <label className="noabs d-block" style={{ textAlign: 'left' }}>
                    Upload Canteen Card / Employee ID
                </label>
                <div
                    className={`upload-button ${uploadedImage ? 'uploaded' : ''}`}
                    onClick={() => fileInputRef.current?.click()}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                >
                    <div className="text">{uploadedImage ? 'File Uploaded successfully' : 'Upload'}</div>
                    <div className="icon">
                        {uploadedImage ? (
                            <Image src={uploadedIcon} alt="Uploaded" />
                        ) : (
                            <Image src={UploadIcon} alt="Upload" />
                        )}
                    </div>
                </div>
                {uploadedImage ? <div className='reupload' onClick={() => fileInputRef.current?.click()} onDragOver={handleDragOver} onDrop={handleDrop}>Re-upload</div> : null}
                <input
                    type="file"
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    accept="image/jpeg,image/png,image/jpg"
                    onChange={handleFileChange}
                />
                {formik.touched.cardImage && formik.errors.cardImage && (
                    <div className="error">{formik.errors.cardImage}</div>
                )}
            </div>

            {/* Buttons */}
            <button type="submit" className="btn btn-primary">Continue</button>
            <button type="reset" className="btn btn-primary-outline mb-0">I am not a defence person</button>
        </form>
    );
}

export default ForceDetailsForm;

'use client'

import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../store/store';
import { closeReferalModal, setReferalCode } from './register.slice';
import { useEffect, useRef } from 'react';

function ReferalCodeForm() {
    const dispatch = useDispatch<AppDispatch>();
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (inputRef.current) {
           setTimeout(() => {
            inputRef.current!.focus();
           }, 300);
        }
    }, []);

    const validationSchema = Yup.object({
        referalCode: Yup.string()
            .matches(/^[A-Za-z0-9]+$/, 'Only alphanumeric characters are allowed')
            .max(8, 'Referral code must be less than/equal to 8 characters')
            .required('Referral code is required'),
    });

    const formik = useFormik({
        initialValues: {
            referalCode: '',
        },
        validationSchema,
        onSubmit: (values) => {
            dispatch(setReferalCode(values.referalCode));
            dispatch(closeReferalModal());
        }
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="input-wrapper referal-input">
                <input
                    ref={inputRef}
                    type="text"
                    name="referalCode"
                    maxLength={8}
                    placeholder=" "
                    value={formik.values.referalCode}
                    onChange={(e) => {
                        let filteredValue = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
                        formik.setFieldValue("referalCode", filteredValue);
                        if (filteredValue.length === 8) {
                            setTimeout(() => e.target.blur(), 100);
                        }
                    }}
                    onBlur={formik.handleBlur}
                    className={`form-control`}
                />
                <label>Enter referral code</label>
                {formik.errors.referalCode && formik.touched.referalCode && (
                    <div className="error">{formik.errors.referalCode}</div>
                )}
            </div>
            <button type="submit" className={`btn btn-primary mb-0 ${!formik.isValid || !formik.dirty ? 'disabled' : ''}`}>Apply</button>
        </form>
    );
}

export default ReferalCodeForm;

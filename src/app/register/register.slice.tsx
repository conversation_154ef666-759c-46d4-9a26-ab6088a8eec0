import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface RegisterState {
    startCapture: boolean;
    capturedImage: string | null;
    missingDetails: string | null;
    pageerror: string | null;
    pagesuccess: string | null;
    fileerror: string | null;
    fileData: string | null;
    selectedDocType: string | null;
    isForceModalOpen: boolean;
    isReferalModalOpen: boolean;
    uploadedImage: string;
    referalCode: string;
    isChecked: boolean;
    defenceCheck: boolean;
    isUploadModalOpen: boolean;
    customerBankDetails: any;
    pincodeValid: boolean;
}

// Define the initial state
const initialState: RegisterState = {
    startCapture: false,
    capturedImage: null,
    missingDetails: null,
    pageerror: null,
    pagesuccess: null,
    fileerror: null,
    fileData: null,
    selectedDocType: null,
    isForceModalOpen: false,
    isReferalModalOpen: false,
    referalCode: '',
    uploadedImage: '',
    isChecked: false,
    defenceCheck: false,
    isUploadModalOpen: false,
    customerBankDetails: null,
    pincodeValid: false
};

// Create the slice
const registerSlice = createSlice({
    name: 'register',
    initialState,
    reducers: {
        setStartCapture: (state, action: PayloadAction<boolean>) => {
            state.startCapture = action.payload;
        },
        setCapturedImage: (state, action: PayloadAction<string | null>) => {
            state.capturedImage = action.payload;
        },
        setPageError: (state, action: PayloadAction<string | null>) => {
            state.pageerror = action.payload;
        },
        setPageSuccess: (state, action: PayloadAction<string | null>) => {
            state.pagesuccess = action.payload;
        },
        setMissingDetails: (state, action: PayloadAction<string | null>) => {
            state.missingDetails = action.payload;
        },
        setSelectedDocType: (state, action: PayloadAction<string | null>) => {
            state.selectedDocType = action.payload;
        },
        openForceModal: (state) => {
            state.isForceModalOpen = true;
        },
        closeForceModal: (state) => {
            state.isForceModalOpen = false;
        },
        openReferalModal: (state) => {
            state.isReferalModalOpen = true;
        },
        closeReferalModal: (state) => {
            state.isReferalModalOpen = false;
        },
        setReferalCode: (state, action) => {
            state.referalCode = action.payload;
        },
        openUploadModal: (state) => {
            state.isUploadModalOpen = true;
        },
        closeUploadModal: (state) => {
            state.fileData = '';
            state.isUploadModalOpen = false;
        },
        setFileError: (state, action: PayloadAction<string | null>) => {
            state.fileerror = action.payload;
        },
        setFileData: (state, action: PayloadAction<string | null>) => {
            state.fileData = action.payload;
        },
        setUploadedFile: (state, action: PayloadAction<string>) => {
            state.uploadedImage = action.payload;
        },
        setIsChecked(state, action: PayloadAction<boolean>) {
            state.isChecked = action.payload;
        },
        setDefenceCheck(state, action: PayloadAction<boolean>) {
            state.defenceCheck = action.payload;
        },
        setCustomerBankDetails(state, action: PayloadAction<boolean>) {
            state.customerBankDetails = action.payload;
        },
        setPincodeValid(state, action: PayloadAction<boolean>) {
            state.pincodeValid = action.payload;
        },
    },
});

// Export actions
export const {
    setStartCapture,
    setCapturedImage,
    setPageError,
    setPageSuccess,
    setMissingDetails,
    setSelectedDocType,
    openForceModal,
    closeForceModal,
    openReferalModal,
    closeReferalModal,
    setReferalCode,
    openUploadModal,
    closeUploadModal,
    setFileError,
    setFileData,
    setUploadedFile,
    setIsChecked,
    setDefenceCheck,
    setCustomerBankDetails,
    setPincodeValid
} = registerSlice.actions;

// Export the reducer
export default registerSlice.reducer;

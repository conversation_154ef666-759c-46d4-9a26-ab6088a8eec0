'use client'
import { FormikErrors, useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import styles from './register.module.scss'
import 'react-datepicker/dist/react-datepicker.css';
import PageHeader from '../components/PageHeader';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store/store';
import Image from 'next/image';
import HeaderIcon from '../images/register-icon.svg'
import ShieldIcon from '../images/shield-icon.svg'
import CrossIcon from '../images/cross-icon.svg'
import calendarIcon from '../images/calendar-icon.svg'
import ForceIcon from '../images/armyman.svg'
import { differenceInYears, format, isValid, parse, subYears } from 'date-fns';
import BottomPopup from '../components/popups/BottomPopup';
import { closeForceModal, closeReferalModal, openForceModal, openReferalModal, setDefenceCheck, setIsChecked, setPageError, setPageSuccess, setPincodeValid, setReferalCode } from './register.slice';
import ForceDetailsForm from './forceDetailComponent';
import ReferalCodeForm from './referalCodeComponent';
import { apiRequest, fetchCommonApi } from "../utils/api";
import { setAuthToken, setDeviceId } from "../store/slices/authSlice";
import { useEffect, useState } from 'react';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import React from 'react';
import { ApiResponse } from '../store/interface/apiInterface';
import { useRouter } from 'next/navigation';
import { registerForm } from './register.interface';
import { ENDPOINTS } from '../utils/endpoints';
import PageWrapper from '../components/PageWrapper';
import { setCommonData } from "../store/slices/commonSlice";
import store from "../store/store";
import Link from 'next/link';
import LoadingComp from '../component/loader';
import DatePicker from 'react-datepicker';
import CtEvents from '../utils/Ctevents'
import { debug } from 'console';

function RegistrationForm() {
    const dispatch = useDispatch<AppDispatch>();
    const router = useRouter()
    const { isChecked, isForceModalOpen, isReferalModalOpen, defenceCheck, referalCode, pageerror, pagesuccess, pincodeValid } = useSelector((state: RootState) => state.register);
    const [loaded, setLoaded] = useState<boolean>(false)
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    const validationSchema = Yup.object({
        fullName: Yup.string()
            .nullable()
            .test(
                "fullName-not-na",
                "Full Name is required",
                (value) => value === "N/A" || !!value
            )
            .test(
                "fullName-alphabetic",
                "Full Name must contain only alphabetic characters",
                (value) => {
                    if (!value || value === "N/A") return true;
                    return /^[A-Za-z\s]+$/.test(value);
                }
            ),

        panCardNumber: Yup.string()
            .nullable()
            .test(
                "is-not-blank",
                "PAN Card number cannot be blank",
                (value) => value === "N/A" || !!value
            )
            .test(
                "is-valid-length",
                "PAN Card number must be exactly 10 characters long",
                (value) => value === "N/A" || (value?.length === 10)
            )
            .test(
                "no-special-characters",
                "PAN Card number must not contain special characters",
                (value) => value === "N/A" || /^[A-Za-z0-9]*$/.test(value || "")
            )
            .test(
                "valid-pan-format",
                "Invalid PAN Card format",
                (value) =>
                    value === "N/A" ||
                    /^[A-Z]{3}P[A-Z][0-9]{4}[A-Z]{1}$/.test(value || "")
            ),
        dateOfBirth: Yup.string()
            .nullable()
            .test("is-not-blank", "Date of Birth cannot be blank", (value) => !!value)

            // Check if the format is correct (DD-MM-YYYY)
            .test("valid-format", "Date must be in DD-MM-YYYY format", (value) => {
                if (!value) return false;
                return /^\d{2}-\d{2}-\d{4}$/.test(value); // Ensures DD-MM-YYYY format
            })

            // Ensure date is valid (e.g., 31-02-2025 is invalid)
            .test("valid-date", "Invalid Date format", (value) => {
                if (!value) return false;
                const parsedDate = parse(value, "dd-MM-yyyy", new Date());
                return isValid(parsedDate);
            })

            // Check minimum age (18 years)
            .test("valid-age", "You must be at least 18 years old", (value) => {
                if (!value) return false;
                const parsedDate = parse(value, "dd-MM-yyyy", new Date());
                return isValid(parsedDate) && differenceInYears(new Date(), parsedDate) >= 18;
            })

            // Ensure the date is not in the future
            .test("no-future-date", "Date of Birth cannot be in the future", (value) => {
                if (!value) return false;
                const parsedDate = parse(value, "dd-MM-yyyy", new Date());
                return isValid(parsedDate) && parsedDate <= new Date();
            })
            .test('min-year', 'Please enter a valid date of birth', (value) => {
                if (!value) return false;
                const [,, year] = value.split('-').map(Number);
                return year > 1900;
              }),
        pinCode: Yup.string()
            .nullable()
            .test(
                "is-not-blank",
                "PIN Code cannot be blank",
                (value) => value === "N/A" || !!value
            )
            .test(
                "is-six-digits",
                "PIN Code must be exactly 6 digits long",
                (value) => !value || /^\d{3} \d{3}$/.test(value)
            )
            .test(
                "only-digits",
                "PIN Code must only contain numeric digits",
                (value) => !value || /^\d{3}( \d{3})*$/.test(value)
            )
            .test(
                "no-leading-zero",
                "PIN Code must not start with zero",
                (value) => !value || /^[1-9]/.test(value)
            )
    });

    const submitBasicDetails = async (values: registerForm) => {
        document.body.classList.add("loading"); // Show loader before API cal
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))
        setLoaded(false)
        try {
            const payload = {
                name: values.fullName,
                pan: values.panCardNumber,
                dob: values.dateOfBirth,
                pincode: values.pinCode.replace(/\s/g, ""),
                referral_code: referalCode || "",
                is_defense_personnel: defenceCheck || false,
            };

            if (!pincodeValid) {
                dispatch(setPageError('Please enter valid pincode number.'))
                return
            }

            // Pass SaveBasicDetailsResponse as the type parameter
            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.savebasicdetails, payload);
            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                // dispatch(setPageSuccess('Data updated'))
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                }
                else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {

                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                    dispatch(setPageError(response.mainData.error_message || ''))
                }
                else if (response.mainData && response.mainData.error_message) {

                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                    dispatch(setPageError(response.mainData.error_message || 'Something went wrong'))
                }

                else if (response.error) {

                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                    dispatch(setPageError(response.error))
                } else {

                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
            //console.error("Error submitting basic details:", error);
            //alert("Failed to save basic details. Please try again.");
        } finally {
            setTimeout(() => {
                setLoaded(true)
            }, 2000);
        }
    };


    const formik = useFormik<registerForm>({
        initialValues: {
            fullName: '',
            panCardNumber: '',
            dateOfBirth: '',
            pinCode: '',
        },
        validationSchema,
        validateOnBlur: true,  // Ensure it's a boolean
        onSubmit: async (values: registerForm) => {
            submitBasicDetails(values)
        },
    });
    useEffect(() => {
        // Fetch auth_token and device_id from URL/localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const auth_token = urlParams.get("auth_token") || localStorage.getItem("auth_token");
        const device_id = urlParams.get("device_id") || localStorage.getItem("device_id");

        if (auth_token) dispatch(setAuthToken(auth_token));
        if (device_id) dispatch(setDeviceId(device_id));
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const event_property = { 'Screen Name': 'LOC_Basic Details', "Source": cteventsfromstorage?.source, "Product category": localStorage.getItem("product_code") }
        setCtdata({ event_name, event_property })
    }, [dispatch]);

    const verifyPincode = async (pincode: string | undefined) => {
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))

        try {
            if (pincode && pincode.length === 6) {
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.pincode, { pincode });
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success')) {
                    dispatch(setPincodeValid(true))
                }
                else if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message || 'Something went wrong'))
                }
                else {
                    dispatch(setPincodeValid(false))
                    if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        }
    };
    const [dob, setDob] = useState("");


    const handleDateBlur = (e: React.FocusEvent<HTMLInputElement, Element>, setFieldValue: { (field: string, value: any, shouldValidate?: boolean | undefined): Promise<void> | Promise<FormikErrors<registerForm>>; (arg0: string, arg1: string): void; }, setFieldError: { (field: string, value: string | undefined): void; (arg0: string, arg1: string): void; }) => {
        let inputValue = e.target.value.replace(/\D/g, ""); // Remove non-numeric characters
        const formattedDate = `${inputValue.substring(0, 2)}-${inputValue.substring(2, 4)}-${inputValue.substring(4, 8)}`;
        if (!inputValue) {
            formik.setFieldValue("dateOfBirth", "");
            return;
        }
        formik.setFieldValue("dateOfBirth", formattedDate);
        formik.setFieldTouched(e.target.name, true, false);
    };
    useEffect(() => {


    }, [formik.touched, formik.errors])

    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);

    useEffect(() => {
        setTimeout(() => {
            const textField = document.getElementById('dateOfBirth');
            console.log('textField found:', textField);

            if (!textField) return;

            const handleDelete = () => {
                debugger;
                const currentValue = (textField as HTMLInputElement).value;
                if (currentValue.length > 0) {
                    (textField as HTMLInputElement).value = currentValue.substring(0, currentValue.length - 1);
                }
            };

            const keydownHandler = (event: KeyboardEvent) => {
                if (event.key === 'Backspace') {
                    event.preventDefault();
                    handleDelete();
                }
            };

            textField.addEventListener('keydown', keydownHandler);

            return () => {
                textField.removeEventListener('keydown', keydownHandler);
            };
        }, 500); // Delay to ensure DOM is rendered
    }, []);




    return (
        <PageWrapper>
            {loaded ?
                <div className={`registration-page registerpage`}>
                    <PageHeader title="Please provide your details to generate loan offer" call={false} icon={<Image src={HeaderIcon} alt='Header' />} />
                    <div className='page-content'>
                        <form onSubmit={formik.handleSubmit}>

                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    autoComplete='off'
                                    name="panCardNumber"
                                    autoCorrect="off"
                                    autoCapitalize="characters"
                                    spellCheck={false}
                                    //inputMode={
                                    //formik.values.panCardNumber.length >= 5 && formik.values.panCardNumber.length < 9 ? "numeric" : "text"
                                    //}
                                    minLength={10}
                                    maxLength={10}
                                    placeholder=" "
                                    value={formik.values.panCardNumber.toUpperCase()}
                                    onChange={(e) => {
                                        const uppercaseValue = e.target.value.toUpperCase().slice(0, 10);
                                        // const formattedValue = uppercaseValue.replace(/[^A-Z0-9]/g, "").slice(0, 10);
                                        formik.setFieldValue("panCardNumber", uppercaseValue);
                                        if (uppercaseValue.length === 10) {
                                            setTimeout(() => e.target.blur(), 0)
                                        }
                                    }}
                                    onBlur={formik.handleBlur}
                                    className={`form-control`}
                                />
                                <label>PAN</label>
                                {!formik.errors.panCardNumber && formik.touched.panCardNumber && (
                                    <span className="valid"></span>
                                )}
                                {formik.errors.panCardNumber && formik.touched.panCardNumber && (
                                    <div className="error">{formik.errors.panCardNumber}</div>
                                )}
                            </div>
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    autoComplete="off"
                                    name="fullName"
                                    placeholder=" "
                                    value={formik.values.fullName}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        const formattedValue = value.replace(/[^A-Za-z\s]/g, "");
                                        formik.setFieldValue("fullName", formattedValue);
                                    }}
                                    onBlur={formik.handleBlur}
                                    className={`form-control`}
                                />

                                <label>Name as per PAN</label>
                                {!formik.errors.fullName && formik.touched.fullName && (
                                    <span className="valid"></span>
                                )}
                                {formik.errors.fullName && formik.touched.fullName && (
                                    <div className="error">{formik.errors.fullName}</div>
                                )}
                            </div>
                            <div className={`input-wrapper date-picker has-icon ${formik.values.dateOfBirth ? 'active' : ''}`}>
                                <div className='icon'><Image src={calendarIcon} alt='calendar' width="20" height="20" /></div>
                                <DatePicker
                                    popperPlacement="top-end"
                                    selected={
                                        formik.values.dateOfBirth
                                            ? (() => {
                                                const parsedDate = parse(formik.values.dateOfBirth, "dd-MM-yyyy", new Date());
                                                return isValid(parsedDate) ? parsedDate : null;
                                            })()
                                            : null
                                    }
                                    onChange={(date) => {
                                        if (date) {
                                            const formattedDate = format(date, "dd-MM-yyyy"); // Ensure correct format
                                            formik.setFieldValue("dateOfBirth", formattedDate);
                                        }
                                    }}
                                    maxDate={subYears(new Date(), 18)}
                                    dateFormat="dd-MM-yyyy"
                                    showYearDropdown
                                    showMonthDropdown
                                    scrollableMonthYearDropdown
                                    dropdownMode="select"
                                    yearDropdownItemNumber={100}
                                    className={`form-control ${styles.datepicker}`}
                                    onKeyDown={(e) => e.preventDefault()}
                                    onBlur={() => formik.setFieldTouched("dateOfBirth", true)}
                                />
                                <input
                                    type="text"
                                    name="dateOfBirth"
                                    id='dateOfBirth'
                                    value={formik.values.dateOfBirth}
                                    inputMode="numeric"
                                    onChange={(e) => {
                                        let value = e.target.value.replace(/\D/g, ""); // Remove non-numeric characters

                                        if (value.length > 8) value = value.slice(0, 8); // Limit to 8 digits

                                        // Add dashes automatically
                                        let formattedValue = "";
                                        if (value.length > 0) {
                                            formattedValue = value.substring(0, 2);
                                        }
                                        if (value.length > 2) {
                                            formattedValue += "-" + value.substring(2, 4);
                                        }
                                        if (value.length > 4) {
                                            formattedValue += "-" + value.substring(4, 8);
                                        }

                                        formik.setFieldValue("dateOfBirth", formattedValue);
                                        formik.setFieldError("dateOfBirth", ""); // Clear error while typing
                                    }}

                                    onKeyDown={(e) => {
                                        if (e.key === "Backspace") {
                                            e.preventDefault(); // Stop default behavior (deleting where cursor is)

                                            const raw = formik.values.dateOfBirth.replace(/\D/g, ""); // Remove dashes
                                            const newRaw = raw.slice(0, -1); // Remove last digit

                                            let formattedValue = "";
                                            if (newRaw.length > 0) formattedValue = newRaw.substring(0, 2);
                                            if (newRaw.length > 2) formattedValue += "-" + newRaw.substring(2, 4);
                                            if (newRaw.length > 4) formattedValue += "-" + newRaw.substring(4, 8);

                                            formik.setFieldValue("dateOfBirth", formattedValue);
                                        }
                                    }}

                                    onBlur={(e) => handleDateBlur(e, formik.setFieldValue, formik.setFieldError)}

                                    maxLength={10} // Prevent typing beyond DD-MM-YYYY
                                    placeholder="DD-MM-YYYY"
                                    className="form-control"
                                />
                                <label className={`floating-label ${formik.values.dateOfBirth ? "active" : ""}`}>
                                    DOB (DD-MM-YYYY)
                                </label>
                                {/*<input
                                type="text"
                                autoComplete='off'
                                name="dateOfBirth"
                                minLength={10}
                                maxLength={10}
                                inputMode="numeric"
                                placeholder=" "
                                value={formik.values.dateOfBirth}
                                onChange={(e) => {
                                    let value = e.target.value.replace(/[^0-9]/g, ""); // Remove non-numeric characters

                                    // Format the value into DD-MM-YYYY
                                    if (value.length > 2) value = `${value.slice(0, 2)}-${value.slice(2)}`;
                                    if (value.length > 5) value = `${value.slice(0, 5)}-${value.slice(5, 9)}`;

                                    // Ensure the value doesn't exceed 10 characters
                                    value = value.slice(0, 10)
                                    //if (/^\d{2}-\d{2}-\d{4}$/.test(value) || value === "N/A") {
                                    formik.setFieldValue("dateOfBirth", value);
                                    //}
                                    setTimeout(() => {
                                        e.target.selectionStart = e.target.selectionEnd = value.length;
                                    }, 0);
                                    if (value.length === 10) {
                                        setTimeout(() => e.target.blur(), 0)
                                    }
                                }}
                                onBlur={formik.handleBlur}
                                className={`form-control`}
                                disabled={formik.values.dateOfBirth === 'N/A'}
                            />*/}

                                {/* <label>DOB (DD/MM/YYYY)</label> */}
                                {/* {!formik.errors.dateOfBirth && formik.touched.dateOfBirth && (
                                    <span className="valid"></span>
                                )} */}
                                {formik.errors.dateOfBirth && formik.touched.dateOfBirth && (
                                    <div className="error">{formik.errors.dateOfBirth}</div>
                                )}
                            </div>

                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    autoComplete='off'
                                    name="pinCode"
                                    placeholder=" "
                                    inputMode="numeric"
                                    maxLength={7}
                                    minLength={6}
                                    value={formik.values.pinCode}
                                    onChange={async (e) => {
                                        const value = e.target.value ? e.target.value.replace(/[^0-9]/g, "") : '';
                                        const formattedValue = value ? value
                                            .match(/.{1,3}/g)
                                            ?.join(" ")
                                            .slice(0, 7) : '';
                                        formik.setFieldValue("pinCode", formattedValue);
                                        await verifyPincode(formattedValue?.split(' ').join(''))
                                        if (formattedValue?.length === 7) {
                                            setTimeout(() => e.target.blur(), 0)
                                        }
                                    }}
                                    onBlur={formik.handleBlur}
                                    className={`form-control`}
                                />
                                <label>PIN Code of current address</label>
                                {!formik.errors.pinCode && formik.touched.pinCode && pincodeValid && (
                                    <span className="valid"></span>
                                )}
                                {formik.errors.pinCode && formik.touched.pinCode && (
                                    <div className="error">{formik.errors.pinCode}</div>
                                )}
                            </div>
                            {/* <div className="input-wrapper switch-wrapper">
                                <p>Are you in the Indian Defence Forces?</p>
                                <label className="toggle-switch noabs">
                                    <input type="checkbox" checked={defenceCheck} onChange={() => {
                                        dispatch(setDefenceCheck(!defenceCheck))
                                    }} />
                                    <span className="toggle-slider"></span>
                                </label>
                            </div> */}
                            {!referalCode && <p className={`link ${styles.referalLink}`} onClick={() => dispatch(openReferalModal())}>Do you have a referral code?</p>}
                            {referalCode ? <div className='referal-tag'><span><strong>{referalCode} -</strong> Referral code applied</span> <Image src={CrossIcon} alt='Cross' style={{ cursor: 'pointer' }} onClick={() => dispatch(setReferalCode(''))} /></div> : null}
                            <div className={`consent-check ${styles.consent} ${!isChecked && formik.dirty && formik.isValid && pincodeValid ? 'highlight' : ''}`}>
                                <label>
                                    <div className={`checkbox ${isChecked ? 'checked' : ''}`}>
                                        <input type="checkbox" checked={isChecked} onChange={() => {
                                            dispatch(setIsChecked(!isChecked));
                                        }} />
                                        {/* Circle for animation */}
                                        <div className={`circle ${!isChecked && formik.dirty && formik.isValid && pincodeValid ? 'animate' : ''}`}></div>
                                    </div>
                                    <p>I consent to Akara to fetch my records for using and/or sharing for KYC purposes, credit information from credit bureaus & loan application with <Link href="https://www.stashfin.com/partners" target='_blank'>lending partners</Link> for evaluation. I further agree to receive notifications on Whatsapp as per the <Link href=" https://stashfin.com/privacy-policies" target='_blank'>privacy policy</Link>.</p>
                                </label>
                            </div>
                            <div className='bottom-footer p-0'>
                                <p className='secure-tag'><Image src={ShieldIcon} alt='Shield' /> Your data is 100% safe & secure</p>
                                <button type="submit" className={`btn btn-primary ${!isChecked || !formik.isValid || !formik.dirty || !pincodeValid ? 'disabled' : ''}`}>Continue</button>
                                <div className={`input-wrapper switch-wrapper ${styles.registerswitch}`}>
                                    <p>I am in the Armed Forces</p>
                                    <label className="toggle-switch noabs">
                                        <input type="checkbox" checked={defenceCheck} onChange={() => {
                                            dispatch(setDefenceCheck(!defenceCheck))
                                            // dispatch(openForceModal())
                                        }} />
                                        <span className="toggle-slider"></span>
                                    </label>
                                </div>
                                <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
                            </div>
                        </form>
                    </div>
                    <BottomPopup
                        isOpen={isForceModalOpen}
                        titleIcon={<Image src={ForceIcon} alt='Army' />}
                        onClose={() => {
                            dispatch(closeForceModal())
                        }}
                        title="We need your force details"
                    >
                        <ForceDetailsForm />
                    </BottomPopup>
                    <BottomPopup
                        isOpen={isReferalModalOpen}
                        onClose={() => {
                            dispatch(closeReferalModal())
                        }}
                        title="Referral Code"
                    >
                        <ReferalCodeForm />
                    </BottomPopup>
                </div>
                :

                <LoadingComp />
            }
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
            <CtEvents data={ctData} />
        </PageWrapper>
    );
}

export default RegistrationForm;

'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import requestSubmitted from '../images/request-submitted.svg'
import styles from './kyc.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import CtEvents from '../utils/Ctevents';
function Page() {
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_rejection_payment_success", "Product category": 'cb', "amount": 385, "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    return (
        <div className='external-wrapper'>
            <PageHeader title='' nobg={true} call={false} />
            <div className={`page-content ${styles.pageContent}`}>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        <Image src={requestSubmitted} alt="Request submitted" />
                        <h3>Request Submitted</h3>
                        <p>We're checking your application for loan.<br />This will take 72 hours</p>
                    </div>
                </div>
            </div>
            <CtEvents data={ctData} />

        </div>
    );
}

export default Page;

'use client';

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import BottomPopup from '../components/popups/BottomPopup';
import styles from './aafail.module.scss'
import bankIcon from '../images/bank-icon.svg'
import { closeModal } from '../store/features/bankSlice';
import Image from 'next/image';
import { apiRequest } from '../utils/api'; // Common API call file
import { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { setPageError } from '../register/register.slice';
import { ENDPOINTS } from '../utils/endpoints';
import { useRouter } from "next/navigation";
import BankImage from "@/app/components/BankImage";
import { ApiResponse } from '../store/interface/apiInterface';


const AaFailPage = () => {
    const router = useRouter();
    const dispatch = useDispatch();
    const [popularBanks, setPopularBanks] = useState<any[]>([]);
    const [otherBanks, setOtherBanks] = useState<any[]>([]);
    const [selectedBank, setSelectedBank] = useState<string | null>(null);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [productcode, setproductcode] = useState(false)
    const [selectedOccupation, setSelectedOccupation] = useState<string>('');


    interface SaveBasicDetailsResponse {

        monitoring_reference_id: any;
        underwriting_reference_id: any;
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
        redirection_url: string
    }
    const {
        accountNumber,
        isModalOpen,
        bankDetails,
    } = useSelector((state: any) => state.bank);

    const handleConfirm = async () => {
        try {
        } catch (error) {
        }
    };
    useEffect(() => {
        getbankList()
        const geproductcode = localStorage.getItem('product_code')

        if (geproductcode == "Sentinel") {
            setproductcode(true)
        }

    }, [])
    const getbankList = async () => {
        try {

            const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.banklist);

            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                // setBankList(response.mainData.data);

                setPopularBanks(response.mainData.data.filter((bank: any) => bank.is_popular));
                setOtherBanks(response.mainData.data.filter((bank: any) => !bank.is_popular));
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                } else if (response.error) {
                } else {
                }
            }
        } catch (error) {
        }
    }
    const filteredBanks = otherBanks.filter((bank) =>
        bank.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    const handleBankClick = async (bankId: string, bankName: string) => {
        setSelectedBank(bankId);
        router.push(
      `/verify-salary-bank?bankId=${bankId}&bankName=${encodeURIComponent(
        bankName
      )}`
    );

    };

    useEffect(() => {
        const getPersoalDetails = async () => {
            try {
                const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.personalDetail);
                if (response.mainData && response.mainData?.data?.occupation) {
                    setSelectedOccupation(response.mainData.data.occupation)
                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }

                    else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                    } else if (response.error) {
                        dispatch(setPageError(response.error))

                    } else {
                    }
                }
            } catch (error) {
            } finally {
                setTimeout(() => {
                    // setLoaded(true)
                }, 2000);
            }
        };
        getPersoalDetails()
    }, [])
    return (
        <div className={`external-wrapper bank-details-page`}>
            <PageHeader title={`Select your ${selectedOccupation === 'SALARIED' ? 'salary' : 'primary'} bank`} para="Your loan amount will be transferred to verified bank account and EMIs be deducted from the same account." />

            {/* <div className={styles.notebox}>
                <div className={styles.title}>
                    Important Note:
                </div>
                <div className={styles.subtitle}>Please ensure you use the same bank account in your UPI app for verification.</div>
            </div> */}
            <div className="page-content">
                <ul className={styles.bankList}>
                    {popularBanks.map((bank) => (
                        <li
                            key={bank.id}
                            className={selectedBank === bank.id ? styles.selected : ''}
                            onClick={() => handleBankClick(bank.id, bank.name)}
                        >
                            <BankImage bankName={bank.name} ratioHeight={48} />
                            <p>{bank.name}</p>
                        </li>
                    ))}

                </ul>
                <div className={styles.otherBanks}>
                    <h3>Other Banks</h3>
                    <div className="input-wrapper">
                        <input
                            type="text"
                            name="otherBank"
                            autoComplete="off"
                            placeholder=" "
                            className={`form-control ${styles.search}`}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <label>Search by bank name</label>
                    </div>
                    <ul className={styles.otherBanksList}>
                        {filteredBanks.map((bank) => (
                            <li
                                key={bank.id}
                                className={selectedBank === bank.id ? styles.selected : ''}
                                onClick={() => handleBankClick(bank.id, bank.name)}
                            >
                                <BankImage bankName={bank.name} ratioHeight={32} />
                                <span>{bank.name}</span>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
            <BottomPopup
                isOpen={isModalOpen}
                onClose={() => dispatch(closeModal())}
                title="Bank Details"
                buttons={[
                    { label: 'Confirm Details', onClick: handleConfirm },
                    { label: 'Cancel', onClick: () => dispatch(closeModal()) }
                ]}
            >
                {bankDetails && (
                    <ul className="bank-details">
                        <li><span>Bank Name:</span><span>{bankDetails.BANK}</span></li>
                        <li><span>Account Number:</span><span>{accountNumber}</span></li>
                        <li><span>IFSC Code:</span><span>{bankDetails.IFSC}</span></li>
                        <li className="address"><span>Branch Address:</span><span>{bankDetails.ADDRESS}</span></li>
                    </ul>
                )}
            </BottomPopup>
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
        </div>
    );
};

export default AaFailPage;
'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import additionalImg from '../images/additional-verification.svg'
import ShieldIcon from '../images/shield-icon.svg'
import styles from './kyc.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';
import { setCommonData } from '../store/slices/commonSlice';
// import store, { RootState } from '../store/store'
import { closeForceModal, closeReferalModal, openForceModal, openReferalModal, setDefenceCheck, setIsChecked, setPageError, setPageSuccess, setPincodeValid, setReferalCode } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store/store';
import store from "../store/store";
import ToastMessage from '../components/ToastMessage/ToastMessage';
function AdditionalVerification() {
    const dispatch = useDispatch<AppDispatch>();

    const { isChecked, isForceModalOpen, isReferalModalOpen, defenceCheck, referalCode, pageerror, pagesuccess, pincodeValid } = useSelector((state: RootState) => state.register);

    const [freedomfee, setFreedomfee] = useState<number | string>();

    useEffect(() => {
        const fetchData = async () => {
            const result = await apiRequest<ApiResponse>("GET", ENDPOINTS.getFreedomFee);
            console.log("result", result);
            if(result.mainData?.success){
                setFreedomfee(result.mainData?.freedomFee?.fee)
            }
        };

        fetchData();
    }, []);

    const createNextStep = async () => {

        const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.nextStep);
        if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
            const commonData = await fetchCommonApi();
            store.dispatch(setCommonData(commonData));
        }
        else if (response.mainData && response.mainData.error_message) {
            dispatch(setPageError(response.mainData.error_message))
        }
        else {
            dispatch(setPageError(response.mainData?.error_message || "No step matched"))
        }
    }
    return (
        <div className='external-wrapper'>
            <PageHeader title='Additional verification' para={`We need to perform extra checks to evaluate your application. A fee of ₹${freedomfee} + GST will be charged for initiating this verification.`} highlightText={`₹${freedomfee} + GST`}/>
            <div className='page-content'>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        <Image src={additionalImg} alt="Additional Verification" />
                        <div className={styles.note}>

                            <h5>Terms & Conditions</h5>
                            <ul>
                                <li>Fee is non refundable.</li>
                                <li>This may include personal verification or other steps, depending on your profile.</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div className="bottom-footer p-0">
                    <button type="submit" className="btn btn-primary" onClick={() => createNextStep()}>Proceed</button>
                </div>
            </div>
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
        </div>
    );
}

export default AdditionalVerification;

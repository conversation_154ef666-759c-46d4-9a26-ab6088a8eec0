.header {


    header {
        min-height: auto;
        padding: 0;
        padding-left: 16px;
        padding-right: 16px;
        padding-top: 10px;
        margin-bottom: 0;
        background-size: 100% calc(100% - 5px);
    }
}

.body {
    padding-left: 16px;
    padding-right: 16px;
    min-height: calc(100vh - 125px);
    display: flex;
    flex-flow: column;

    >h1 {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 24px;
        line-height: 28.8px;
        color: #000;
        margin-top: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        >img {
            width: 70px;
            height: 70px;
        }
    }

    >p {
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        margin-top: 16px;
        color: var(--black);
    }

    .whybox {
        width: 100%;
        background: #F5F5F5;
        border-radius: 12px;
        padding: 16px;
        margin-top: 30px;

        .title {
            font-family: var(--font-mona-sans);
            font-weight: 700;
            font-size: 18px;
            line-height: 20px;
            color: #000;
            margin-top: 0;
        }

        .subtitle {
            font-family: var(--font-mona-sans);
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            margin-top: 5px;
            color: var(--black);
        }

        >div {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;

            >div {
                width: 90px;
                display: flex;
                flex-flow: column;
                align-items: center;

                img {
                    width: 63px;
                    height: 63px;
                }

                p {
                    margin-top: 12px;
                    font-family: var(--font-mona-sans);
                    font-weight: 600;
                    font-size: 12px;
                    line-height: 15px;
                    color: var(--black);
                    width: 100%;
                    text-align: center;
                }
            }
        }
    }

    .box {
        border: 1px solid #E6E6E6;
        border-radius: 12px;
        display: flex;
        margin-top: 20px;
        padding: 16px;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        align-items: center;

        >div {
            display: flex;
            flex-flow: column;
            align-items: flex-start;

            p {
                display: flex;
                align-items: center;
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 16px;
                line-height: 20px;
                color: var(--black);

                img {
                    margin-left: 5px;
                    width: 49px;
                    height: 12px;
                }
            }

            span {
                margin: top 6px;
                font-family: var(--font-mona-sans);
                font-weight: 400;
                font-size: 14px;
                line-height: 16px;
                color: var(--black);
            }

            .btn {
                border: 1px solid #000;
                border-radius: 66px;
                padding: 4px 10px;
                font-family: var(--font-mona-sans);
                font-weight: 500;
                font-size: 12px;
                line-height: 20px;
                color: #000;
                margin-top: 14px;
                display: flex;
                cursor: pointer;
            }
        }

        >img {
            width: 64px;
            height: 64px;
        }
    }

    .setupmandate {
        margin-top: 60px;
        text-align: center;
        color: #909090;
        font-family: var(--font-mona-sans);
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
    }

    .footerbottombtn {
        margin-top: auto;

        .datasafety {
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            color: var(--black);

            img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }
        }
        .btn {
            margin-top: 20px;
        }
    }
    .bankChange {
        text-align: right;
        margin-top: 12px;
        cursor: pointer;

        span {
            font-size: 14px;
            line-height: 1;
        }
    }
    .note{
        background: #FFF8E0;
        padding: 8px 16px;
        line-height: 1;
        border-radius: 8px;
        font-weight: 500;
        width: 313px;
        max-width: 100%;
        margin: 0 auto;
        margin-top: 5px;
        margin-bottom: 50px;
        text-align: center;
    }
}
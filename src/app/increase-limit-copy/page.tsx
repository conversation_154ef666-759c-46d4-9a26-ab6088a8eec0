"use client"
import React from "react";
import styles from "./increaseamount.module.scss";
import PageHeader from "../components/PageHeader";
import PageWrapper from "../components/PageWrapper";
import Image from "next/image"
import redmeter from '../images/redmeter.svg'
import whitemeter from '../images/whitemeter.svg'
import moveright from '../images/moveright.svg'

export default function LoansPage() {
    return (
        <PageWrapper>
            <div className={styles.loanspage}>
                <div className={styles.header}>
                    <h1>
                        Loans
                        <a href="#">Help</a>
                    </h1>
                    {/* <PageHeader title="Loans" call={false} /> */}
                </div>



                <section className={styles.creditline}>
                    <div className={styles.sectionheader}>
                        <h2>Credit Line</h2>
                        <a href="#" className={styles.detailslink}>Details</a>
                    </div>
                    <div className={`${styles.card} ${styles.creditcard}`}>
                        <div className={styles.cardcontent}>
                            <p className={styles.label}>Limit Available</p>
                            <h3 className={styles.amount}>₹20,000</h3>
                            <p className={styles.subtext}>out of ₹2,00,000</p>
                            <p className={styles.warning}><Image src={redmeter} alt="" /> Last &nbsp;<strong>20%</strong>&nbsp; limit left, &nbsp;<strong>Increase limit upto 5 Lakh</strong></p>
                            <div className={styles.increasebox}>
                                <span>Increase available limit up to 5 lakh</span>
                                <button className={styles.btn}><Image src={whitemeter} alt="" /> Increase now</button>
                            </div>
                            <div className={styles.paybox}>
                                <span>Pay full by &lt; date &gt; & enjoy 0% interest free credit period</span>
                                <button className={styles.btnoutline}>Pay full</button>
                            </div>
                            <button className={`${styles.btn} ${styles.fullwidth} ${styles.withdrawcashbtn}`}>Withdraw Cash</button>
                        </div>
                    </div>
                </section>

                <section className={styles.personalloan}>
                    <h2>Personal Loan</h2>
                    <div className={`${styles.card} ${styles.creditcard}`}>
                        <div className={styles.cardcontent}>
                            <div className={styles.loanheader}>
                                <div>
                                    <p className={styles.label}>Outstanding Amount</p>
                                    <h3 className={styles.amount}>₹50,000</h3>
                                    <p className={styles.subtext}>Partner name: HT MEDIA</p>
                                </div>
                                <div className={styles.arrow}><Image src={moveright} alt="" /></div>
                            </div>
                            <div className={styles.loandetails}>
                                <p>Loan amount <span>₹2,00,000</span></p>
                                <p>Remaining duration <span>48 months</span></p>
                            </div>

                        </div>
                    </div>
                    <div className={styles.loanfooter}>
                        <p>₹2,000 due on 23 Feb</p>
                        <a href="#" className={styles.payearly}>Pay early</a>
                    </div>
                </section>

                <section className={styles.exploreproducts}>
                    <h2>Explore more product</h2>

                    <div className={`${styles.card} ${styles.creditcard}`}>
                        <div className={styles.cardcontent}>
                            <p className={`${styles.tag} ${styles.blue}`}>CASH AGAINST MUTUAL FUNDS</p>
                            <p>Use your mutual funds to get instant cash</p>
                            <button className={styles.btnoutline}>Check now</button>
                        </div>
                    </div>

                    <div className={`${styles.card} ${styles.creditcard} ${styles.greenbg}`}>
                        <div className={styles.cardcontent}>
                            <p className={`${styles.tag} ${styles.blue}`}>CREDIT SCORE</p>
                            <p>Check your Credit Score for FREE</p>
                            <button className={styles.btnoutline}>Check now</button>
                        </div>
                    </div>
                </section>

                <div className={styles.creditbuilderbox}>
                    <div className={styles.title}>
                        Refer & Earn
                    </div>
                    <h2>Invite friends and get ₹500 for every loan disbursal.</h2>
                    <div className={styles.circularbtn}
                        //onClick={() => commonCTa('Refer Now', 'Referral')}
                        /*onClick={() => {
                          navigateToPage({
                            type: "page",
                            landingPage: "MF_form"
                          })
                        }}*/
                        onClick={() => {
                            if (process.env.NEXT_PUBLIC_LIVE_URL === window.location.origin) {
                                window.location.href = 'https://www.stashfin.com/refer-and-earn/play-and-win'
                            } else {
                                window.location.href = 'https://dev.stashfin.com/refer-n-earn/home'
                            }
                        }}
                    >Refer Now</div>
                </div>
            </div>
        </PageWrapper>
    );
}

'use client'
import React, { useEffect } from 'react';
import '../scss/button.scss';
import styles from './kycloading.module.scss'
import { setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { ENDPOINTS } from '../utils/endpoints';
import { apiRequest, fetchCommonApi, clevertapIdentitySet } from '../utils/api';
import store from "../store/store";
import { setCommonData } from "../store/slices/commonSlice";
import loadingAnimation from "../../../public/Loader_Red.json"
import dynamic from "next/dynamic";
import PageHeader from '../components/PageHeader';
import { ApiResponse } from '../store/interface/apiInterface';

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
function KycLoading() {
    const dispatch = useDispatch();
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);

    interface SaveBasicDetailsResponse {
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
        redirection_url: string
    }
    const checkCommon = async () => {
        const custResponse = await apiRequest<ApiResponse>("GET", ENDPOINTS.personalDetail);
        if(custResponse.mainData?.success){
            clevertapIdentitySet({identity: custResponse.mainData.data.id});
        }else{
            console.error("error in init identity in clevertap")
        };
        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
    }
    useEffect(() => {
        checkCommon();

    })
    return (
        <div className='external-wrapper'>
            {/* <PageHeader title={''} back={false} call={false} nobg={true} /> */}

            <div className='page-content'>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        {/* <Image src={successImg} alt="Congratulations" /> */}
                        <Lottie
                            animationData={loadingAnimation}
                            loop={true}
                            style={{ height: 100, width: 100 }} // Adjust size as needed
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}

export default KycLoading;
'use client'
import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import styles from './page.module.scss'
import PageHeader from '../components/PageHeader';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store/store';
import Image from 'next/image';
import HeaderIcon from '../images/armyman.svg'
import UploadIcon from '../images/uploadIcon.svg';
import UploadedIcon from '../images/valid-check.svg';
import { closeUploadModal, openUploadModal, setPageError, setPageSuccess, setUploadedFile } from './defence.slice';
import { apiRequest, fetchCommonApi } from "../utils/api";
import { ForceData } from './force.interface';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import React, { useEffect, useRef, useState } from 'react';
import { ApiResponse } from '../store/interface/apiInterface';
import SelectDropdown from '../components/SelectDropdown';
import BottomPopup from '../components/popups/BottomPopup';
import { ENDPOINTS } from '../utils/endpoints';
import { getStaticData } from "../utils/fetchData";
import PageWrapper from '../components/PageWrapper';
import { setCommonData } from "../store/slices/commonSlice";
import store from "../store/store";
import LoadingComp from '../component/loader';
import { useRouter } from "next/navigation";


function ForceDetails() {
    const router = useRouter()

    const dispatch = useDispatch<AppDispatch>();
    const { uploadedImage, isUploadModalOpen, pageerror, pagesuccess } = useSelector((state: RootState) => state.defence);
    const uploadInputRef = useRef<HTMLInputElement>(null);
    const [data, setData] = useState<any[]>([]);
    const [submitter, setSubmitter] = useState<string | null>(null);
    const [loaded, setLoaded] = useState<boolean>(false)
    const [fileErr, setFileErr] = useState(false);


    const MAX_FILE_SIZE = 5 * 1024 * 1024;
    const ALLOWED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/png'];


    const validateAndUploadFile = (file: File) => {
        if (!file) return;
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
            alert('Supported formats: PDF, JPEG, PNG.');
            return;
        }
        if (file.size > MAX_FILE_SIZE) {
            alert('File size must be less than 5 MB.');
            return;
        }
        const reader = new FileReader();
        reader.onloadend = () => {
            dispatch(setUploadedFile(reader.result as string));
            formik.setFieldValue('cardImage', file);
        };
        reader.readAsDataURL(file);
    };

    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            validateAndUploadFile(file);
        }
    };

    const handleBrowseFilesClick = () => {
        uploadInputRef.current?.click();
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
    };
    const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        const file = event.dataTransfer.files?.[0];
        if (file) {
            validateAndUploadFile(file);
        }
    };

    const validationSchema = Yup.object({
        forceName: Yup.string().required('Force Name is required'),
        designation: Yup.string().required('Designation is required'),
        rank: Yup.string().required('Rank is required'),
        /*cardNumber: Yup.string()
            .required('Canteen Card / Employee ID Number is required')
            .matches(/^[A-Za-z0-9]+$/, 'Invalid ID format'),*/
        // cardImage: Yup.mixed().required('Card image is required'),
        otherRank: Yup.string().when('rank', {
            is: 'Other',
            then: (schema) => schema.required('Rank name is required'),
            otherwise: (schema) => schema.notRequired(),
        }),
    });

    const handleSubmit = async (values: ForceData, submitter: string | null) => {
        if(uploadedImage === "") {
            setFileErr(true);
            return
        }
        const errors = await formik.validateForm();
        if (submitter === 'continue') {
            if (Object.keys(errors).length > 0) {
                formik.setTouched({
                    forceName: true,
                    designation: true,
                    rank: true,
                    cardImage: true,
                    otherRank: true
                });
                return;
            }
        }
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))
        setLoaded(false)
        // debugger;
        if (submitter === 'continue') {
            try {
                const payload = {
                    loan_id: 0,
                    not_defence_customer: false, // Set true if clicked "I am not a defence person"
                    force_data: {
                        force_name: values.forceName,
                        designation: values.designation,
                        rank: values.rank !== 'Other' ? values.rank : `${values.rank} : 'Other'}`,
                        //canteen_id: values.cardNumber,
                        documents: {
                            doc_type: 'Canteen Card',
                            doc_content: uploadedImage,
                            doc_format: values.cardImage.type.includes('/') ? values.cardImage.type.split('/')[1] : values.cardImage.type
                        }
                    }
                }
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.addsentineldetails, payload);
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success' || response.mainData.status?.toString().toLowerCase() === 'true')) {
                    // dispatch(setPageSuccess(response.mainData?.data?.message || 'Force details update successful'))
                    // dispatch(closeUploadModal())
                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }
                    else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {


                        dispatch(setPageError(response.mainData.error_message || 'Error in uploading file'))
                    }
                    else if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message || 'Something went wrong'))
                    }
                    else if (response.error) {


                        dispatch(setPageError(response.error))
                    } else {


                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            } catch (error) {
                //dispatch(setPageError('Error submitting basic details'))
            } finally {
                setTimeout(() => {
                    setLoaded(true)
                }, 2000);
            }
        }
        else {
            try {
                const payload = {
                    loan_id: 0,
                    not_defence_customer: true,
                }
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.addsentineldetails, payload);
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success' || response.mainData.status?.toString().toLowerCase() === 'true')) {

                    // dispatch(setPageSuccess(response.mainData?.data?.message || 'Force details update successful'))
                    // dispatch(closeUploadModal())
                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                }

                else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }


                    else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                        dispatch(setPageError(response.mainData.error_message || 'Error in uploading file'))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            } catch (error) {
                //dispatch(setPageError('Error submitting basic details'))
            } finally {
                setTimeout(() => {
                    setLoaded(true)
                }, 2000);
            }
        }
    };

    const formik = useFormik({
        initialValues: {
            forceName: '',
            designation: '',
            rank: '',
            //cardNumber: '',
            cardImage: '',
            otherRank: ""
        },
        validationSchema,
        onSubmit: (values) => {
            handleSubmit(values, submitter);
        },
    });

    const saveUploadedFile = () => {
        //upload file api
        if (uploadedImage) {
            dispatch(closeUploadModal())
        }
    }
    useEffect(() => {
        const fetchData = async () => {
            const result = await getStaticData();
            type DataType = { [key: string]: { s_no: number;[key: string]: any; }; };
            type SortedDataType = { name: string; s_no: number;[key: string]: any; }[];
            const sortDataBySNo = (data: DataType): SortedDataType => {
                return Object.entries(data)
                    .map(([key, value]) => ({
                        name: key,
                        ...value
                    }))
                    .sort((a, b) => a.s_no - b.s_no);
            };

            const sortedData = sortDataBySNo(result);
            setData(sortedData);
        };
        fetchData();
    }, []);
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);

    useEffect(() => {
        if(uploadedImage !== "") {
            setFileErr(false);
        }
    }, [uploadedImage])

    return (
        <PageWrapper>
            {loaded ?
                <div className={`registration-page`}>
                    <PageHeader title="Please provide your force details" icon={<Image src={HeaderIcon} alt='Header' />} />
                    <div className={`page-content ${styles.pageContent}`}>
                        <form onSubmit={formik.handleSubmit} style={{ textAlign: 'left' }}>
                            {/* Force Name */}
                            <div className="input-wrapper">
                                <SelectDropdown
                                    name="forceName"
                                    id="forceName"
                                    options={data ? data.map((item) => item?.name) : []} // Extract force names from sortedData
                                    labelText="Force Name"
                                    value={formik.values.forceName}
                                    onChange={(option) => {
                                        formik.setFieldValue('forceName', option);
                                        formik.setFieldValue('designation', ''); // Reset designation
                                        formik.setFieldValue('rank', ''); // Reset rank
                                        formik.setFieldValue('otherRank', ''); // Reset otherRank field if used
                                    }}
                                />
                                {formik.touched.forceName && formik.errors.forceName && (
                                    <div className="error">{formik.errors.forceName}</div>
                                )}
                            </div>

                            {/* Designation */}
                            <div className="input-wrapper">
                                <SelectDropdown
                                    name="designation"
                                    id="designation"
                                    options={
                                        data.find((item) => item.name === formik.values.forceName)
                                            ? Object.entries(data.find((item) => item.name === formik.values.forceName) || {})
                                                .filter(([key]) => key !== 'name' && key !== 's_no')
                                                .sort(([_, a], [__, b]) => ((a as any)?.s_no || 0) - ((b as any)?.s_no || 0))
                                                .map(([key]) => key)
                                            : []
                                    }
                                    labelText="Designation"
                                    value={formik.values.designation}
                                    onChange={(option) => {
                                        formik.setFieldValue('designation', option);
                                        formik.setFieldValue('rank', ''); // Reset rank
                                        formik.setFieldValue('otherRank', ''); // Reset otherRank field if used
                                    }}
                                />
                                {formik.touched.designation && formik.errors.designation && (
                                    <div className="error">{formik.errors.designation}</div>
                                )}
                            </div>

                            {/* Rank */}
                            <div className="input-wrapper">
                                <SelectDropdown
                                    name="rank"
                                    id="rank"
                                    options={
                                        formik.values.forceName && formik.values.designation
                                            ? data.find((item) => item.name === formik.values.forceName)?.[formik.values.designation]?.ranks ||
                                            data.find((item) => item.name === formik.values.forceName)?.[formik.values.designation] ||
                                            []
                                            : []
                                    }
                                    labelText="Rank"
                                    value={formik.values.rank}
                                    onChange={(option) => {
                                        formik.setFieldValue('rank', option);
                                        if (option !== 'Other') {
                                            formik.setFieldValue('otherRank', ''); // Reset other rank field if not selected
                                        }
                                    }}
                                />
                                {formik.touched.rank && formik.errors.rank && (
                                    <div className="error">{formik.errors.rank}</div>
                                )}
                            </div>

                            {/* Other Rank (If Selected) */}
                            {formik.values.rank === 'Other' && (
                                <div className="input-wrapper">
                                    <input
                                        type="text"
                                        name="otherRank"
                                        placeholder="Enter your rank"
                                        value={formik.values.otherRank}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        className="form-control"
                                    />
                                    <label>Enter Your Rank</label>
                                    {formik.touched.otherRank && formik.errors.otherRank && (
                                        <div className="error">{formik.errors.otherRank}</div>
                                    )}
                                </div>
                            )}

                            <div className="input-wrapper">
                                <label className="noabs">Upload Canteen Card / Employee ID</label>
                                <div
                                    className={uploadedImage ? 'upload-button uploaded' : 'upload-button'}
                                    onClick={() => dispatch(openUploadModal())}
                                >
                                    <div className="text">{uploadedImage ? `File Uploaded successfully` : 'Upload'}</div>
                                    <div className="icon">
                                        {uploadedImage ? (
                                            <div className="image">
                                                <Image src={UploadedIcon} alt="Uploaded" />
                                            </div>
                                        ) : (
                                            <Image src={UploadIcon} alt="Upload" />
                                        )}
                                    </div>
                                </div>
                                {uploadedImage ? <div className='reupload' onClick={() => dispatch(openUploadModal())}>Re-upload</div> : null}
                                {fileErr && <p style={{color: "red", fontSize: 12, marginTop: 6}}>Image is required</p>}
                            </div>

                            {/* Buttons */}
                            <div className="bottom-footer mt-auto">
                                <button
                                    type="submit"
                                    name="continue"
                                    className="btn btn-primary"
                                    onClick={() => handleSubmit(formik.values, 'continue')}
                                >
                                    Continue
                                </button>
                                <button
                                    type="reset"
                                    name="notDefence"
                                    className="btn btn-primary-outline mb-0"
                                    style={{ color: '#000' }}
                                    onClick={() => handleSubmit(formik.values, 'notdefence')}
                                >
                                    I am not a defence person
                                </button>
                            </div>
                        </form>

                    </div>
                    <BottomPopup
                        isOpen={isUploadModalOpen}
                        onClose={() => {
                            dispatch(closeUploadModal());
                        }}
                        title="Upload"
                        buttons={[
                            {
                                label: 'Upload & Save',
                                onClick: () => {
                                    saveUploadedFile()
                                },
                                className: `mb-0 ${uploadedImage ? '' : 'disabled'}`
                            }
                        ]}
                    >
                        <div>
                            <p style={{ textAlign: 'left' }}>Max Size: 5 MB</p>
                            <p style={{ textAlign: 'left', marginBottom: '15px' }}>Supported Formats: .PDF, PNG, JPEG.</p>
                            <div className="upload-btn">
                                <button
                                    className="btn btn-primary-outline"
                                    onClick={handleBrowseFilesClick}
                                    type="button"
                                >
                                    Browse files
                                </button>
                                {/* Hidden file input to handle file selection */}
                                <input
                                    type="file"
                                    ref={uploadInputRef}
                                    style={{ display: 'none' }}
                                    accept="application/pdf,image/jpeg,image/png"
                                    onChange={handleFileInputChange}
                                    onDragOver={handleDragOver}
                                    onDrop={handleDrop}
                                />
                            </div>
                        </div>
                    </BottomPopup>
                </div>
                :

                <LoadingComp />
            }
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
        </PageWrapper>
    );
}

export default ForceDetails;
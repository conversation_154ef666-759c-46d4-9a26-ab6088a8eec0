import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface DefenceState {
    pageerror: string | null;
    pagesuccess: string | null;
    fileerror: string | null;
    fileData: string | null;
    selectedDocType: string | null;
    uploadedImage: string;
    isUploadModalOpen: boolean;
}

// Define the initial state
const initialState: DefenceState = {
    pageerror: null,
    pagesuccess: null,
    fileerror: null,
    fileData: null,
    selectedDocType: null,
    uploadedImage: '',
    isUploadModalOpen: false,
};

// Create the slice
const defenceSlice = createSlice({
    name: 'defence',
    initialState,
    reducers: {
        setPageError: (state, action: PayloadAction<string | null>) => {
            state.pageerror = action.payload;
        },
        setPageSuccess: (state, action: PayloadAction<string | null>) => {
            state.pagesuccess = action.payload;
        },
        setSelectedDocType: (state, action: PayloadAction<string | null>) => {
            state.selectedDocType = action.payload;
        },
        openUploadModal: (state) => {
            state.isUploadModalOpen = true;
        },
        closeUploadModal: (state) => {
            state.fileData = '';
            state.isUploadModalOpen = false;
        },
        setFileError: (state, action: PayloadAction<string | null>) => {
            state.fileerror = action.payload;
        },
        setFileData: (state, action: PayloadAction<string | null>) => {
            state.fileData = action.payload;
        },
        setUploadedFile: (state, action: PayloadAction<string>) => {
            state.uploadedImage = action.payload;
        },
    },
});

// Export actions
export const {
    setPageError,
    setPageSuccess,
    setSelectedDocType,
    openUploadModal,
    closeUploadModal,
    setFileError,
    setFileData,
    setUploadedFile
} = defenceSlice.actions;

// Export the reducer
export default defenceSlice.reducer;

'use client'
import React, { useEffect, useRef, useState } from 'react';
import '../scss/button.scss';
import styles from './rpdpolling.module.scss';
import { apiRequest, fetchCommonApi } from '../utils/api';
import store from "../store/store";
import { setCommonData } from "../store/slices/commonSlice";
import { ENDPOINTS } from '../utils/endpoints';
import loadingAnimation from "../../../public/Loader_Red.json";
import dynamic from "next/dynamic";
import PageHeader from '../components/PageHeader';
import { useDispatch, useSelector } from "react-redux"
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { setPageError } from '../register/register.slice';
import shield from '../images/shield-icon.svg'

import Image from "next/image"
import PageWrapper from '../components/PageWrapper';
import { useRouter } from "next/navigation";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

function RpdPolling() {
    const router = useRouter();

    const dispatch = useDispatch();
    const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
    const pollingRef = useRef<NodeJS.Timeout | null>(null);
    const countdownRef = useRef<NodeJS.Timeout | null>(null);
    const timeoutReached = useRef(false); // Flag to ensure final call triggers only once
    interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        redirection_url: string;
        status: string;
    }

    const rpdpolling = async (isCancel = false) => {
        try {
            const endpoint = isCancel ? `${ENDPOINTS.rpdpolling}?status=cancel` : ENDPOINTS.rpdpolling;
            const response = await apiRequest<SaveBasicDetailsResponse>("GET", endpoint);

            const status = response?.mainData?.status;
            const errorMsg = response?.mainData?.error_message;
            if (status === 'manual' || status === 'success') {
                // const commonData = await fetchCommonApi();
                // store.dispatch(setCommonData(commonData));
                router.push(`/banksuccess`);

                clearAllTimers(); // Stop polling/countdown on success
            }
            if (status == 'failed') {
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
                clearAllTimers(); // Stop polling/countdown on success
            }
            else {
                dispatch(setPageError(errorMsg as string))

            }
        } catch (error) {
            // Continue polling unless final timeout
        }
    };

    const clearAllTimers = () => {
        if (pollingRef.current) clearInterval(pollingRef.current);
        if (countdownRef.current) clearInterval(countdownRef.current);
    };

    useEffect(() => {
        // Initial call
        rpdpolling();

        // Start polling every 3 seconds
        pollingRef.current = setInterval(() => {
            // Only run final cancel if time is up
            if (timeoutReached.current) return;
            rpdpolling();
        }, 10000);

        // Countdown
        countdownRef.current = setInterval(() => {
            setTimeLeft(prev => {
                if (prev <= 1) {
                    clearInterval(countdownRef.current!);
                    timeoutReached.current = true;
                    clearAllTimers(); // stop polling
                    rpdpolling(true); // final call with cancel
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => {
            clearAllTimers();
        };
    }, []);

    const formatTime = (seconds: number) => {
        const m = Math.floor(seconds / 60);
        const s = seconds % 60;
        return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    };

    return (
        <PageWrapper>
            <div className='external-wrapper'>
                <PageHeader title="" frompage="rpdpolling" />
                <div className='page-content'>
                    <div className={styles.kycContent}>
                        <div className={styles.kycCard}>
                            <Lottie
                                animationData={loadingAnimation}
                                loop={true}
                                style={{ height: 100, width: 100, marginTop: '-100px' }}
                            />
                            <p style={{ marginBottom: '0px' }}><strong>{formatTime(timeLeft)}</strong></p>
                            <p>Time remaining {' '} </p>
                            <p style={{ fontSize: '24px', fontWeight: 700, maxWidth: '250px', lineHeight: '28px' }}>Processing your
                                payment securely 🔐</p>
                            <p style={{ fontSize: '14px', fontWeight: 500 }}>This can take up to 5 minutes. <br />
                                Thanks for your patience!</p>
                        </div>
                    </div>
                    <div className={styles.footerbottombtn}>
                        <div className={styles.datasafety}>
                            <Image src={shield} alt="" /> Your data is 100% safe & secure
                        </div>
                    </div>
                </div>
            </div>
        </PageWrapper>
    );
}

export default RpdPolling;

import { createSlice } from '@reduxjs/toolkit';

export interface EmploymentState {
    isChecked: boolean,
    pageerror: string | null,
    emailerror: string | null,
    otperror: string | null,
    uanerror: string | null,
    emailsuccess: string | null,
    emailsent: string | null,
    employmentType: string | null,
    emailAddress: string | null,
    otp: string | null,
    uan: string | null,
    isErrorModalOpen: boolean
}

const initialState: EmploymentState = {
    isChecked: true,
    pageerror: '',
    emailerror: '',
    otperror: '',
    uanerror: '',
    emailsuccess: '',
    emailsent: '',
    employmentType: null,
    emailAddress: null,
    otp: null,
    uan: null,
    isErrorModalOpen: false
};

const employmentSlice = createSlice({
    name: 'employment',
    initialState,
    reducers: {
        setIsChecked(state, action) {
            state.isChecked = action.payload;
        },
        setPageError(state, action) {
            state.pageerror = action.payload;
        },
        setEmailError(state, action) {
            state.emailerror = action.payload;
        },
        setOTPError(state, action) {
            state.otperror = action.payload;
        },
        setUANError(state, action) {
            state.uanerror = action.payload;
        },
        setEmailSuccess(state, action) {
            state.emailsuccess = action.payload;
        },
        setEmailSent(state, action) {
            state.emailsent = action.payload;
        },
        setEmploymentType(state, action) {
            state.employmentType = action.payload;
        },
        setEmailAddress(state, action) {
            state.emailerror = ''
            state.otperror = ''
            state.pageerror = ''
            state.uanerror = ''
            state.emailsuccess = ''
            state.employmentType = ''
            state.emailAddress = action.payload;
        },
        setOTP(state, action) {
            state.emailerror = ''
            state.otperror = ''
            state.pageerror = ''
            state.uanerror = ''
            state.otp = action.payload;
        },
        setUAN(state, action) {
            state.emailerror = ''
            state.otperror = ''
            state.pageerror = ''
            state.uanerror = ''
            state.uan = action.payload;
        },
        openErrorModal: (state) => {
            state.isErrorModalOpen = true;
        },
        closeErrorModal: (state) => {
            state.isErrorModalOpen = false;
        },
    },
});

export const { setIsChecked, setPageError, setEmailError, setOTPError, setUANError, setEmailSuccess, setEmailSent, setEmploymentType, setEmailAddress, setOTP, setUAN, openErrorModal, closeErrorModal } = employmentSlice.actions;

export default employmentSlice.reducer;

'use client';
import { useDispatch, useSelector } from 'react-redux';
import store, { AppDispatch, RootState } from '../store/store';
import React, { useEffect, useRef, useState } from 'react';
import PageHeader from '../components/PageHeader';
import PageWrapper from '../components/PageWrapper';
import SelectDropdown from '../components/SelectDropdown';
import BottomPopup from '../components/popups/BottomPopup';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import ShieldIcon from '../images/shield-icon.svg';
import Image from 'next/image';
import LoadingComp from '../component/loader';
import { closeUploadModal, openUploadModal, setUploadedFile, setPageError } from '../force-details/defence.slice';
import { getStaticData } from '../utils/fetchData';
import UploadedIcon from '../images/valid-check.svg';
import UploadIcon from '../images/uploadIcon.svg';
import emailicon from '../images/emailicon.svg';
import styles from './employment.module.scss';
import '../scss/button.scss';
import '../scss/form.scss';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';
import { setCommonData } from '../store/slices/commonSlice';
import CtEvents from '../utils/Ctevents';


function AdditionalKycDetails() {
    const dispatch = useDispatch<AppDispatch>();
    const { uploadedImage, isUploadModalOpen, pageerror, pagesuccess } = useSelector((state: RootState) => state.defence || {});
    const uploadInputRef = useRef<HTMLInputElement>(null);
    const [data, setData] = useState<any[]>([]);
    const [loaded, setLoaded] = useState<boolean>(false);
    const [fileErr, setFileErr] = useState(false);
    const [showEmploymentTypePopup, setShowEmploymentTypePopup] = useState(false);
    const [showSalaryDatePopup, setShowSalaryDatePopup] = useState(false);
    const [showMonthlyIncomePopup, setShowMonthlyIncomePopup] = useState(false);
    const [showLoanPurposePopup, setShowLoanPurposePopup] = useState(false);
    const emailFromRedux = useSelector((state: RootState) => state.otp?.email) || useSelector((state: RootState) => state.employment?.emailAddress);
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
    const [forceDetails, setForceDetails] = useState<{
        forceName: string;
        designation: string;
        rank: string;
        otherRank: string;
        cardImage: string;
    }>({
        forceName: '',
        designation: '',
        rank: '',
        otherRank: '',
        cardImage: '',
    });

    const dayOptions = Array.from({ length: 31 }, (_, i) => {
        const num = i + 1;
        const suffix = num === 1 || num === 21 || num === 31 ? "st" : num === 2 || num === 22 ? "nd" : num === 3 || num === 23 ? "rd" : "th";
        return { name: `${num}${suffix}` };
    });
    const monthlyIncomeOptions = [
        '₹25,000 - ₹50,000',
        '₹50,001 - ₹75,000',
        '₹75,001 - ₹1,00,000',
        '₹1,00,000 + ',
    ];
    const employmentTypeOptions = [
        "Salaried",
        "Self-employed",
        'Armed Forces',
        'Professional',
    ];
    const loanPurposeOptions = [
        'Personal Reasons',
        'Household Expenses',
        'Lifestyle & Travel',
        'Medical Expenses',
        'Repay another loan / Credit Card Bill',
    ];
    const validationSchema = Yup.object().shape({
        employmentType: Yup.string().required('Employment type is required'),
        salaryDate: Yup.string().when('employmentType', {
            is: (val: string) => val === 'Salaried',
            then: (schema) => schema.required('Salary date is required'),
            otherwise: (schema) => schema.notRequired(),
        }),
        monthlyIncome: Yup.string().required('Monthly income is required'),
        loanPurpose: Yup.string().required('Loan purpose is required'),
        personalEmail: Yup.string().email('Enter a valid email').required('Email is required'),
        fatherName: Yup.string()
            .required("Father's Name is required")
            .matches(/^[A-Za-z\s]+$/, "Father's Name must contain only letters"),
        forceName: Yup.string().when('employmentType', {
            is: (val: string) => val === 'Armed Forces',
            then: (schema) => schema.required('Force Name is required'),
            otherwise: (schema) => schema.notRequired(),
        }),

        designation: Yup.string().when('employmentType', {
            is: (val: string) => val === 'Armed Forces',
            then: (schema) => schema.required('Designation is required'),
            otherwise: (schema) => schema.notRequired(),
        }),

        rank: Yup.string().when('employmentType', {
            is: (val: string) => val === 'Armed Forces',
            then: (schema) => schema.required('Rank is required'),
            otherwise: (schema) => schema.notRequired(),
        }),

        otherRank: Yup.string().when(['employmentType', 'rank'], ([employmentType, rank], schema) => {
            if (employmentType === 'Armed Forces' && rank === 'Other') {
                return schema.required('Other Rank is required');
            }
            return schema.notRequired();
        }),

        canteenCard: Yup.string().when('employmentType', {
            is: (val: string) => val === 'Armed Forces',
            then: (schema) => schema.required('Canteen card upload is required'),
            otherwise: (schema) => schema.notRequired(),
        }),

        coupon: Yup.string().when('employmentType', {
            is: (val: string) => val === 'Armed Forces',
            then: (schema) => schema.optional(),
            otherwise: (schema) => schema.strip(),
        }),
    });

    const formik = useFormik({
        initialValues: {
            employmentType: '',
            salaryDate: '',
            monthlyIncome: '',
            loanPurpose: '',
            personalEmail: '',
            fatherName: '',
            coupon: '',
            forceName: '',
            designation: '',
            rank: '',
            otherRank: '',
            canteenCard: '',
        },
        validationSchema,
        validateOnBlur: true,
        validateOnChange: true,
        onSubmit: async (values) => {
            if (!isChecked) return;
            setLoaded(false);
            const ctevents = JSON.parse(localStorage.getItem("eventsdata") || "null");
            const productcode = localStorage.getItem("product_code")

            setCtdata({
                event_name: "Button Clicked",
                event_property: {
                    "pagename": "LOC_Additional Details Submiited",
                    "CTA": "Continue",
                    "Product category": productcode,
                    "Segment": ctevents.isNonSTP === true ? "Non STP" : "STP",
                    "Employment Type": values.employmentType,
                    "Salary Date": values.salaryDate || '',
                    "Income": values.monthlyIncome,
                    Source: ctevents?.source,
                    "Loan Purpose": values.loanPurpose,
                    "Email ID": values.personalEmail
                },
            });
            try {
                // Always send these
                const getProfessionType = (type: string): "SALARIED" | "SELF_EMPLOYED" | "Armed Forces" | null => {
                    if (type === "Salaried") return "SALARIED";
                    if (type === "Self-employed") return "SELF_EMPLOYED";
                    if (type === "Armed Forces") return "Armed Forces";
                    return null; // only valid enum values
                };

                const getRankValue = () => {
                    return values.rank !== "Other"
                        ? values.rank || ""
                        : `Other : ${values.otherRank || ""}`;
                };

                const getDocFormat = () => {
                    if (values.canteenCard && typeof values.canteenCard === "string") {
                        const parts = values.canteenCard.split(".");
                        return parts.length > 1 ? parts[parts.length - 1] : "";
                    }
                    return "";
                };


                const emptySalariedData = { salary_date: "", monthly_income: "" };
                const emptySelfEmployedData = { monthly_income: "" };
                const emptyProfessionalData = { monthly_income: "" };
                const emptyArmedForcesData = {
                    referral_code: "",
                    force_data: {
                        force_name: "",
                        designation: "",
                        rank: "",
                        documents: {
                            doc_type: "Canteen Card",
                            doc_content: "",
                            doc_format: "",
                        },
                    },
                };
                let payload: any = {
                    father_name: values.fatherName || "",
                    email: values.personalEmail || "",
                    profession_type: getProfessionType(values.employmentType),
                    loan_purpose: values.loanPurpose || "",
                };

                // Add conditional block based on employment type
                if (values.employmentType === "Salaried") {
                    payload.salaried_data = {
                        salary_date: values.salaryDate || "",
                        monthly_income: values.monthlyIncome || "",
                    };
                } else if (values.employmentType === "Self-employed") {
                    payload.self_employed_data = {
                        monthly_income: values.monthlyIncome || "",
                    };
                } else if (values.employmentType === "Professional") {
                    payload.professional_data = {
                        monthly_income: values.monthlyIncome || "",
                    };
                } else if (values.employmentType === "Armed Forces") {
                    payload.armed_forces_data = {
                        referral_code: values.coupon || "",
                        force_data: {
                            force_name: values.forceName || "",
                            designation: values.designation || "",
                            rank: getRankValue(),
                            documents: {
                                doc_type: "Canteen Card",
                                doc_content: uploadedImage || "",
                                doc_format: getDocFormat(),
                            },
                        },
                    };
                }
                // debugger;
                const response = await apiRequest<ApiResponse>("PUT", ENDPOINTS.additional_kyc_details, payload);
                // debugger;
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success' || response.mainData.status?.toString().toLowerCase() === 'true')) {
                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                } else {
                    setLoaded(true)
                    setTimeout(() => {
                        dispatch(setPageError(response.mainData?.error_message ?? "Unknown error"));
                    }, 300);
                }
            } catch (error) {
                // debugger;
                setLoaded(true)
                //dispatch(setPageError('Error submitting basic details'))
            } finally {
                // setTimeout(() => {
                //     setLoaded(true)
                // }, 2000);
            }
        },
    });

    useEffect(() => {
        let email = emailFromRedux;
        if (!email && typeof window !== 'undefined') {
            email = localStorage.getItem('personal_email') || '';
        }
    }, [emailFromRedux]);

    useEffect(() => {
        if (formik.values.employmentType === 'Armed Forces') {
            const fetchData = async () => {
                const result = await getStaticData();
                type DataType = { [key: string]: { s_no: number;[key: string]: any; }; };
                type SortedDataType = { name: string; s_no: number;[key: string]: any; }[];
                const sortDataBySNo = (data: DataType): SortedDataType => {
                    return Object.entries(data)
                        .map(([key, value]) => ({
                            name: key,
                            ...value
                        }))
                        .sort((a, b) => a.s_no - b.s_no);
                };
                const sortedData = sortDataBySNo(result);
                setData(sortedData);
            };
            fetchData();
        }
    }, [formik.values.employmentType]);

    const MAX_FILE_SIZE = 5 * 1024 * 1024;
    const ALLOWED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/png'];
    const validateAndUploadFile = (file: File) => {
        if (!file) return;
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
            alert('Supported formats: PDF, JPEG, PNG.');
            return;
        }
        if (file.size > MAX_FILE_SIZE) {
            alert('File size must be less than 5 MB.');
            return;
        }
        const reader = new FileReader();
        reader.onloadend = () => {
            dispatch(setUploadedFile(reader.result as string));
            setForceDetails((prev) => ({ ...prev, cardImage: file.name }));
            formik.setFieldValue('canteenCard', file.name);

        };
        reader.readAsDataURL(file);
    };
    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            validateAndUploadFile(file);
        }
    };
    const handleBrowseFilesClick = () => {
        uploadInputRef.current?.click();
    };
    const handleEmploymentTypeSelect = (type: string) => {
        formik.setFieldValue('employmentType', type);
        setShowEmploymentTypePopup(false);
        setForceDetails({ forceName: '', designation: '', rank: '', otherRank: '', cardImage: '' });
        formik.setFieldValue('forceName', '');
        formik.setFieldValue('designation', '');
        formik.setFieldValue('rank', '');
        formik.setFieldValue('otherRank', '');
        formik.setFieldValue('canteenCard', '');
        formik.setFieldValue('salaryDate', '');
        formik.setFieldValue('monthlyIncome', '');
        formik.setFieldValue('loanPurpose', '');
        formik.setFieldValue('coupon', '');
        formik.setFieldValue('canteenCard', '');

    };
    const handleSalaryDateSelect = (option: string | { name: string }) => {
        const value = typeof option === 'string' ? option : option.name;
        formik.setFieldValue('salaryDate', value);
        setShowSalaryDatePopup(false);
    };
    const handleMonthlyIncomeSelect = (option: string | { name: string }) => {
        const value = typeof option === 'string' ? option : option.name;
        formik.setFieldValue('monthlyIncome', value);
        setShowMonthlyIncomePopup(false);
    };
    const handleLoanPurposeSelect = (purpose: string) => {
        formik.setFieldValue('loanPurpose', purpose);
        setShowLoanPurposePopup(false);
    };
    const handleForceDetailChange = (field: string, value: string | { name: string }) => {
        const val = typeof value === 'string' ? value : value.name;

        const updatedForceDetails = {
            ...forceDetails,
            [field]: val,
        };

        setForceDetails(updatedForceDetails);

        formik.setFieldValue('forceName', updatedForceDetails.forceName);
        formik.setFieldValue('designation', updatedForceDetails.designation);
        formik.setFieldValue('rank', updatedForceDetails.rank);
        formik.setFieldValue('otherRank', updatedForceDetails.otherRank);
        formik.setFieldValue('canteenCard', uploadedImage);
    };
    const handleRankChange = (option: string | { name: string }) => {
        const value = typeof option === 'string' ? option : option.name;
        setForceDetails((prev) => ({ ...prev, rank: value, otherRank: value === 'Other' ? prev.otherRank : '' }));
        formik.setFieldValue('rank', value);
        if (value !== 'Other') {
            formik.setFieldValue('otherRank', '');
        }
    };
    const handleOtherRankChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setForceDetails((prev) => ({ ...prev, otherRank: e.target.value }));
        formik.setFieldValue('otherRank', e.target.value);
    };
    const designationOptions = formik.values.employmentType === 'Armed Forces' && forceDetails.forceName
        ? Object.entries(data.find((item) => item.name === forceDetails.forceName) || {})
            .filter(([key]) => key !== 'name' && key !== 's_no')
            .sort(([_, a], [__, b]) => ((a as any)?.s_no || 0) - ((b as any)?.s_no || 0))
            .map(([key]) => key)
        : [];
    const rankOptions = formik.values.employmentType === 'Armed Forces' && forceDetails.forceName && forceDetails.designation
        ? data.find((item) => item.name === forceDetails.forceName)?.[forceDetails.designation]?.ranks ||
        data.find((item) => item.name === forceDetails.forceName)?.[forceDetails.designation] ||
        []
        : [];
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }
        handleStart();
        setTimeout(handleComplete, 1000);
        return () => handleComplete();
    }, []);
    useEffect(() => {
        if (uploadedImage !== "") {
            setFileErr(false);
        }
    }, [uploadedImage]);

    const [isChecked, setIsChecked] = useState(false);
    useEffect(() => {
        const ctevents = JSON.parse(localStorage.getItem("eventsdata") || "null");
        setCtdata({
            event_name: "Screen View",
            event_property: {
                "Screen Name": "LOC_Additional Details Submiited",
                "Product category": localStorage.getItem("product_code"),
                "Segment": ctevents.isNonSTP === true ? "Non STP" : "STP",
                Source: ctevents?.source,
            },
        });
    }, [])
    return (
        <PageWrapper>
            {loaded ? (
                <div className={`registration-page ${styles.employmentpage}`}>
                    <PageHeader title="Additional KYC Details" para="Please fill in these details so we can proceed with your loan application" />
                    <div className="page-content">
                        <form onSubmit={formik.handleSubmit}>
                            <div className="section employment-section">
                                <div className="input-wrapper">
                                    <div className={`custom-select${formik.values.employmentType ? ' selected' : ''}`} onClick={() => setShowEmploymentTypePopup(true)} tabIndex={0} role="button">
                                        <div className="select-header form-control">
                                            <label className="form-label">Employment Type</label>
                                            <div className="value">{formik.values.employmentType || ''}</div>
                                        </div>
                                    </div>
                                    {formik.errors.employmentType && formik.touched.employmentType && (
                                        <div className="error">{formik.errors.employmentType}</div>
                                    )}
                                </div>
                                {formik.values.employmentType === 'Armed Forces' && (
                                    <>
                                        <div className="input-wrapper">
                                            <input
                                                type="text"
                                                name="coupon"
                                                placeholder=" "
                                                value={formik.values.coupon}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                className="form-control"
                                            />
                                            <label>Enter referral code (Optional)</label>
                                        </div>
                                        <div className="input-wrapper">
                                            <div className="select-input">
                                                <SelectDropdown
                                                    name="forceName"
                                                    id="forceName"
                                                    options={data ? data.map((item) => item?.name) : []}
                                                    labelText="Force Name"
                                                    value={forceDetails.forceName}
                                                    onChange={(option) => handleForceDetailChange('forceName', option)}
                                                />
                                            </div>
                                        </div>
                                        <div className="input-wrapper">
                                            <div className="select-input">
                                                <SelectDropdown
                                                    name="designation"
                                                    id="designation"
                                                    options={designationOptions}
                                                    labelText="Designation"
                                                    value={forceDetails.designation}
                                                    onChange={(option) => handleForceDetailChange('designation', option)}
                                                />
                                            </div>
                                        </div>
                                        <div className="input-wrapper">
                                            <div className="select-input">
                                                <SelectDropdown
                                                    name="rank"
                                                    id="rank"
                                                    options={rankOptions}
                                                    labelText="Rank"
                                                    value={forceDetails.rank}
                                                    onChange={handleRankChange}
                                                />
                                            </div>
                                        </div>
                                        {forceDetails.rank === 'Other' && (
                                            <div className="input-wrapper">
                                                <input
                                                    type="text"
                                                    name="otherRank"
                                                    placeholder="Enter your rank"
                                                    value={forceDetails.otherRank}
                                                    onChange={handleOtherRankChange}
                                                    className="form-control"
                                                />
                                                <label>Enter Your Rank</label>
                                            </div>
                                        )}
                                        <div className="input-wrapper">
                                            <div
                                                className={uploadedImage ? `upload-button uploaded ${styles.uploadedbtn}` : `upload-button ${styles.uploadbtn}`}
                                                onClick={() => dispatch(openUploadModal())}
                                            >
                                                <div className="text">{uploadedImage ? `File Uploaded successfully` : 'Canteen Card'}</div>
                                                <div className="icon">
                                                    {uploadedImage ? (
                                                        <div className="image">
                                                            <Image src={UploadedIcon} alt="Uploaded" />
                                                        </div>
                                                    ) : (
                                                        <Image src={UploadIcon} alt="Upload" />
                                                    )}
                                                </div>
                                            </div>
                                            <p className={styles.uploadinfo}>Upload canteen card / ID card image or take photo</p>
                                            {/* {uploadedImage ? <div className='reupload' onClick={() => dispatch(openUploadModal())}>Re-upload</div> : null} */}
                                            {fileErr && <p style={{ color: "red", fontSize: 12, marginTop: 6 }}>Image is required</p>}
                                        </div>
                                    </>
                                )}
                                {formik.values.employmentType !== 'Armed Forces' && (
                                    <>
                                        {formik.values.employmentType === 'Salaried' && (
                                            <div className="input-wrapper">
                                                <SelectDropdown
                                                    name="salaryDate"
                                                    options={dayOptions.map(opt => opt.name)}
                                                    labelText="Salary Date"
                                                    value={formik.values.salaryDate}
                                                    onChange={handleSalaryDateSelect}
                                                />
                                                {formik.errors.salaryDate && formik.touched.salaryDate && (
                                                    <div className="error">{formik.errors.salaryDate}</div>
                                                )}
                                            </div>
                                        )}

                                    </>
                                )}
                                <div className="input-wrapper">
                                    <div className={`custom-select${formik.values.monthlyIncome ? ' selected' : ''}`} onClick={() => setShowMonthlyIncomePopup(true)} tabIndex={0} role="button">
                                        <div className="select-header form-control">
                                            <label className="form-label">Monthly Income</label>
                                            <div className="value">{formik.values.monthlyIncome || ''}</div>
                                        </div>
                                    </div>
                                    {formik.errors.monthlyIncome && formik.touched.monthlyIncome && (
                                        <div className="error">{formik.errors.monthlyIncome}</div>
                                    )}
                                </div>
                            </div>
                            <div className={styles.otherdetailstext}>Other details</div>
                            <div className="section other-section">
                                <div className="input-wrapper">
                                    <div className={`custom-select${formik.values.loanPurpose ? ' selected' : ''}`} onClick={() => setShowLoanPurposePopup(true)} tabIndex={0} role="button">
                                        <div className="select-header form-control">
                                            <label className="form-label">Loan Purpose</label>
                                            <div className="value">{formik.values.loanPurpose || ""}</div>
                                        </div>
                                    </div>
                                    {formik.errors.loanPurpose && formik.touched.loanPurpose && (
                                        <div className="error">{formik.errors.loanPurpose}</div>
                                    )}
                                </div>
                                <div className="input-wrapper">
                                    <input
                                        type="email"
                                        name="personalEmail"
                                        placeholder=" "
                                        value={formik.values.personalEmail}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        className="form-control"
                                        autoComplete="email"
                                    />
                                    <label>Personal Email ID</label>
                                    <div className={styles.emailicon}>
                                        <Image src={emailicon} alt='' />
                                    </div>
                                    <p className={styles.emailinfo}>Your loan documents will be sent to this email ID</p>

                                    {formik.errors.personalEmail && formik.touched.personalEmail && (
                                        <div className="error">{formik.errors.personalEmail}</div>
                                    )}
                                </div>
                                <div className="input-wrapper">
                                    <input
                                        type="text"
                                        name="fatherName"
                                        placeholder=' '
                                        value={formik.values.fatherName}
                                        onChange={formik.handleChange}
                                        onBlur={formik.handleBlur}
                                        className="form-control"
                                    />
                                    <label>Father’s Name (as per your PAN)</label>

                                    {formik.errors.fatherName && formik.touched.fatherName && (
                                        <div className="error">{formik.errors.fatherName}</div>
                                    )}
                                </div>
                            </div>
                            <div className={`consent-check ${!isChecked && formik.dirty && formik.isValid ? 'highlight' : ''}`}>
                                <label>
                                    <div className={`checkbox ${isChecked ? 'checked' : ''}`}>
                                        <input
                                            type="checkbox"
                                            checked={isChecked}
                                            onChange={() => setIsChecked(!isChecked)}
                                        />
                                        <div className="circle"></div>
                                    </div>
                                    <p>
                                        I confirm that my annual household income is greater than 3 lakhs.
                                    </p>
                                </label>
                            </div>
                            <div className="bottom-footer p-0 mt-auto">
                                <p className={`secure-tag ${styles.securetag}`}>
                                    <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                </p>
                                <button
                                    type="submit"
                                    className={`btn btn-primary ${!isChecked || !formik.isValid || !formik.dirty ? 'disabled' : ''}`}
                                    disabled={!isChecked || !formik.isValid || !formik.dirty}
                                >
                                    Continue
                                </button>
                            </div>
                        </form>
                        {/* <pre>{JSON.stringify(formik.values, null, 2)}</pre>
                        <pre>{JSON.stringify(formik.errors, null, 2)}</pre> */}
                    </div>
                    <BottomPopup
                        isOpen={showEmploymentTypePopup}
                        onClose={() => setShowEmploymentTypePopup(false)}
                        title="Select your employment type"
                        buttons={[]}
                    >
                        <div className={`${styles.modallistparent} ${styles.emptypemodal}`}>

                            <ul className={`select-options`}>
                                {employmentTypeOptions.map((option, key) => (
                                    <li
                                        key={key}
                                        onClick={() => handleEmploymentTypeSelect(option)}
                                    >
                                        <input
                                            type="radio"
                                            className="form-check-input"
                                            readOnly
                                            checked={typeof option === 'string' ? formik.values.employmentType === option : formik.values.employmentType === option}
                                        />
                                        <div>
                                            {typeof option === 'string' ? option : option}
                                            {option === 'Professional' &&
                                                <p>Doctor, Lawyer, Accountant etc</p>
                                            }
                                        </div>

                                    </li>
                                ))}
                            </ul>
                        </div>
                    </BottomPopup>
                    <BottomPopup
                        isOpen={showSalaryDatePopup}
                        onClose={() => setShowSalaryDatePopup(false)}
                        title="Select Salary Date"
                        buttons={[]}
                    >
                        <div>
                            {dayOptions.map((opt) => (
                                <div key={opt.name} className="popup-option" onClick={() => handleSalaryDateSelect(opt)}>
                                    {opt.name}
                                </div>
                            ))}
                        </div>
                    </BottomPopup>
                    <BottomPopup
                        isOpen={showMonthlyIncomePopup}
                        onClose={() => setShowMonthlyIncomePopup(false)}
                        title="Select Monthly Income"
                        buttons={[]}
                    >
                        <div className={`${styles.modallistparent}`}>

                            <ul className="select-options">
                                {monthlyIncomeOptions.map((option, key) => (
                                    <li
                                        key={key}
                                        onClick={() => handleMonthlyIncomeSelect(option)}
                                    >
                                        <input
                                            type="radio"
                                            className="form-check-input"
                                            readOnly
                                            checked={typeof option === 'string' ? formik.values.monthlyIncome === option : formik.values.monthlyIncome === option}
                                        />
                                        {typeof option === 'string' ? option : option}

                                    </li>
                                ))}
                            </ul>
                        </div>
                    </BottomPopup>
                    <BottomPopup
                        isOpen={showLoanPurposePopup}
                        onClose={() => setShowLoanPurposePopup(false)}
                        title="Select Loan Purpose"
                        buttons={[]}
                    >
                        <div className={`${styles.modallistparent} ${styles.loanpurposemodal}`}>


                            <ul className="select-options">
                                {loanPurposeOptions.map((option, key) => (
                                    <li
                                        key={key}
                                        onClick={() => handleLoanPurposeSelect(option)}
                                    >
                                        <input
                                            type="radio"
                                            className="form-check-input"
                                            readOnly
                                            checked={typeof option === 'string' ? formik.values.loanPurpose === option : formik.values.loanPurpose === option}
                                        />
                                        {typeof option === 'string' ? option : option}

                                    </li>
                                ))}
                            </ul>
                        </div>
                    </BottomPopup>
                    <BottomPopup
                        isOpen={isUploadModalOpen}
                        onClose={() => {
                            dispatch(closeUploadModal());
                        }}
                        title="Upload"
                        buttons={[
                            {
                                label: 'Upload & Save',
                                onClick: () => {
                                    if (uploadedImage) dispatch(closeUploadModal())
                                },
                                className: `mb-0 ${uploadedImage ? '' : 'disabled'}`
                            }
                        ]}
                    >
                        <div>
                            <p style={{ textAlign: 'left' }}>Max Size: 5 MB</p>
                            <p style={{ textAlign: 'left', marginBottom: '15px' }}>Supported Formats: .PDF, PNG, JPEG.</p>
                            <div className="upload-btn">
                                <button
                                    className="btn btn-primary-outline"
                                    onClick={handleBrowseFilesClick}
                                    type="button"
                                >
                                    Browse files
                                </button>
                                <input
                                    type="file"
                                    ref={uploadInputRef}
                                    style={{ display: 'none' }}
                                    accept="application/pdf,image/jpeg,image/png"
                                    onChange={handleFileInputChange}
                                />
                            </div>
                        </div>
                    </BottomPopup>
                    <CtEvents data={ctData} />

                </div>
            ) : (
                <LoadingComp />
            )}
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
}

export default AdditionalKycDetails;
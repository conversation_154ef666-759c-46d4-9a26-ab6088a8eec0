.employmentpage {
    header {
        min-height: auto;
        margin-bottom: 28px;
    }

    form {
        display: flex;
        min-height: calc(100vh - 180px);
        flex-direction: column;
    }

    .emailinfo {
        font-size: 12px;
        font-weight: 400;
        color: #1F1F1F80;
    }

    .securetag {
        margin-top: 30px;
    }

    .otherdetailstext {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .modallistparent {
        min-height: 150px;

        ul {
            border: none;

            li {
                justify-content: flex-start;

                input {
                    margin-right: 12px;
                }

                div {
                    display: flex;
                    flex-flow: column;
                    align-items: flex-start;
                }
            }
        }

        &.emptypemodal {
            min-height: 180px;

            ul {
                li {
                    &:last-child {
                        align-items: flex-start;
                    }
                }
            }
        }
        &.loanpurposemodal {
            min-height: 200px;
            ul {
                overflow: hidden;
            }
        }
    }

    .uploadbtn {
        width: 100%;
        height: 52px;
        border-color: #7b7b7b;     

        >div {
            color: #7b7b7b;
        }
    }
    .uploadedbtn {
        width: 100% !important;
        height: 52px;
        border-radius: 8px;
        border-color: #7b7b7b;     
        padding-left: 10px;
    }
    .uploadinfo {
        font-size: 12px;
        font-weight: 400;
        color: #7b7b7b;
    }
    .emailicon {
        position: absolute;
        top: 12px;
        right: 10px;
    }
}
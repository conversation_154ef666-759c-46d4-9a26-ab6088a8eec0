'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import selfieImg from '../images/selfie.svg'
import noCap from '../images/no-cap.svg'
import noMask from '../images/no-mask.svg'
import noGlasses from '../images/no-mask.svg'
import ShieldIcon from '../images/shield-icon.svg'
import PageHeader from '../components/PageHeader';
import styles from './selfie.module.scss'
import Image from 'next/image';
import { ENDPOINTS } from '../utils/endpoints';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import store, { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import PageWrapper from '../components/PageWrapper';
import LoadingComp from '../component/loader';
import CtEvents from '../utils/Ctevents';
import { useRouter } from 'next/navigation';
import { setCommonData } from '../store/slices/commonSlice';
import { pushCleverTapEvent } from '../utils/cleverTapClient';

function TakeSelfie() {
    const router = useRouter()

    const dispatch = useDispatch();
    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [loaded, setLoaded] = useState<boolean>(false)
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    interface SaveBasicDetailsResponse {
        statusCode: number;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>;
        landing_page: string,
        profile_pic_upload_url: string,
        status: any
    }
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")
        const aadhar_event_property = { "Screen Name": "Aadhaar Success", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
        pushCleverTapEvent({eventName: "Screen View", eventData: aadhar_event_property})
        
        const event_property = { "Screen Name": "Selfie upload", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    const TakeASelfie = async () => {
        debugger;
        setLoaded(false)
        try {
            const payload = {
                "redirect_url": `${window.location.origin}/selfie-polling`,
                // "redirect_url": `https://lending-journey-staging.stashfin.com/selfie-polling`,
            }
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.selfie, payload);

            if (response.mainData && response.mainData.status && response.mainData.status.toString().toLowerCase() === "success") {
                debugger;
                const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                const event_name = 'Button Clicked'
                const productcode = localStorage.getItem("product_code")

                const event_property = { "Page Name": "Selfie upload", "CTA": "Continue", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", }
                setCtdata({ event_name, event_property })
                if (response.mainData.statusCode == 208) {
                    debugger;
                    // const commonData = await fetchCommonApi();
                    // store.dispatch(setCommonData(commonData));
                    router.push('/selfie-polling');

                }
                else {
                    window.location.assign(response.mainData.profile_pic_upload_url)
                }

            } else {
                debugger;
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
                    dispatch(setPageError(response.mainData.error_message as string | "null"))
                } else if (response.error) {
                    // debugger;
                } else {
                }
            }
        } catch (error) {
        } finally {
            setTimeout(() => {
                setLoaded(true)
            }, 2000);
        }
    }
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);
    return (
        <PageWrapper>
            {loaded ?
                <div className='external-wrapper'>
                    <PageHeader title='Take a quick selfie' para="We just need a clear picture for verification" />
                    <div className='page-content'>
                        <div className={styles.selfieContent}>
                            <div className={styles.selfieCard}>
                                <Image src={selfieImg} alt="Congratulations" />
                                <div className={styles.steps}>
                                    <div className={styles.step}>
                                        <Image src={noCap} alt="No Cap" width={40} height={40} />
                                        <p>No Cap</p>
                                    </div>
                                    <div className={styles.step}>
                                        <Image src={noMask} alt="No Mask" width={40} height={40} />
                                        <p>No Mask</p>
                                    </div>
                                    <div className={styles.step}>
                                        <Image src={noGlasses} alt="No Glasses" width={40} height={40} />
                                        <p>No Glasses</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bottom-footer p-0">
                            <p className="secure-tag">
                                <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                            </p>
                            <button type="submit" className="btn btn-primary" onClick={TakeASelfie}>Continue</button>
                        </div>
                    </div>

                </div>
                :

                <LoadingComp />
            }
            <CtEvents data={ctData} />
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
}

export default TakeSelfie;

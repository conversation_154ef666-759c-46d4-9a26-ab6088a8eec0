'use client'

import { useFormik } from 'formik';
import * as Yup from 'yup';
import '../scss/button.scss';
import '../scss/form.scss';
import PageHeader from '../components/PageHeader';
import SelectDropdown from '../components/SelectDropdown';
import styles from './address.module.scss';
import Image from 'next/image';
import ShieldIcon from '../images/shield-icon.svg';
import { setPageError, setPageSuccess, setPincodeValid } from './comaddress.slice';
import { AddressForm } from '../address-details/address.interface';
import { useDispatch, useSelector } from 'react-redux';
import store, { RootState } from '../store/store';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import React, { useEffect, useState } from 'react';
import { ENDPOINTS } from '../utils/endpoints';
import PageWrapper from '../components/PageWrapper';
import { setCommonData } from '../store/slices/commonSlice';
import LoadingComp from '../component/loader';
import CtEvents from '../utils/Ctevents';
import { useRouter } from "next/navigation";
import { pushCleverTapEvent } from '../utils/cleverTapClient';


export default function CommunicationAddressDetails() {
    const router = useRouter();
    
    const dispatch = useDispatch()
    const { pageerror, pagesuccess, pincodeValid } = useSelector((state: RootState) => state.comaddress)
    const [loaded, setLoaded] = useState<boolean>(false)
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
    const [productcode, setproductcode] = useState(false)
    const [isSentinalUser, setIsSentinalUser] = useState<boolean>(false);


    const validationSchema = Yup.object({
        ...(!isSentinalUser && {residenceType: Yup.string().required("Residence Type is required")}),
        flatHouseNo: Yup.string()
            .required("Flat/House No. is required"),
        street: Yup.string()
            .required("Street is required"),
        landmark: Yup.string()
            .required("Landmark is required"),
        pincode: Yup.string()
            .required("PIN Code is required")
            .test(
                "is-not-blank",
                "PIN Code cannot be blank",
                (value) => value === "N/A" || !!value
            )
            .test(
                "is-six-digits",
                "PIN Code must be exactly 6 digits long",
                (value) => !value || /^\d{3} \d{3}$/.test(value) // "xxx xxx" format
            )
            .test(
                "only-digits",
                "PIN Code must only contain numeric digits",
                (value) => !value || /^\d{3}( \d{3})*$/.test(value) // Numeric check
            )
            .test(
                "no-leading-zero",
                "PIN Code must not start with zero",
                (value) => !value || /^[1-9]/.test(value) // First digit shouldn't be zero
            ),
        city: Yup.string().required("City is required"),
        state: Yup.string().required("State is required"),
    });

    const formik = useFormik({
        initialValues: {
            residenceType: '',
            flatHouseNo: '',
            street: '',
            landmark: '',
            pincode: '',
            city: '',
            state: '',
        },
        validationSchema,
        onSubmit: async (values) => {
            submitForm(values);
        },
    });
    const validateAddressInput = (value: any) => {
        return value.replace(/[^a-zA-Z0-9\s,.-]/g, ''); // Allows letters, numbers, space, comma, dot, and hyphen
    };
    const verifyPincode = async (pincode: string) => {
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))

        try {
            if (pincode && pincode.length === 6) {
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.pincode, { pincode });
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success')) {
                    formik.setFieldValue('city', response.mainData.data.city)
                    formik.setFieldValue('state', response.mainData.data.state)
                    dispatch(setPincodeValid(true))
                } else {
                    dispatch(setPincodeValid(false))
                    formik.setFieldValue('city', '')
                    formik.setFieldValue('state', '')
                    if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed')) {
                        dispatch(setPageError(response.mainData.error_message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        }
    };

    const submitForm = async (values: AddressForm) => {
        dispatch(setPageError(''))
        dispatch(setPageSuccess(''))
        setLoaded(false)
        try {
            if (!pincodeValid) {
                setTimeout(() => {
                    dispatch(setPageError('Pin code is not valid.'))
                }, 200);
            } else {
                const payload = {
                    residence_type: isSentinalUser ? "Rented" : values.residenceType === 'Rented House' ? 'Rented' : 'Self_Owned',
                    flat_no: values.flatHouseNo,
                    address_line_1: values.street,
                    address_line_2: '',
                    landmark: values.landmark,
                    pincode: parseInt(values.pincode.split(' ').join('')),
                    city: values.city,
                    state: values.state
                }
                const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.communicationAddress, payload);
                if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success')) {
                    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                    // ------------------------------
                    const event_name = 'Button Clicked'
                    const productcode = localStorage.getItem("product_code")
                    const event_property = { "Page Name": "Address Details without Proof", "CTA": "Continue", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", }
                    setCtdata({ event_name, event_property })
                    // ------------------------------
                    const loc_event_name = 'LOC_Communication Address Submitted'
                    const loc_event_property = { 
                        "Residence Type - Communication": values.residenceType, 
                        "PINCODE": values.pincode, 
                        "City/District" : values.city,
                        "State" : values.state,
                        "Product category": cteventsfromstorage.productcode, 
                        "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP", 
                        "Source": cteventsfromstorage.source,
                        "Page Name": "Address Details without Proof"
                    }
                    pushCleverTapEvent({eventName: loc_event_name, eventData: loc_event_property})
                    // ------------------------------

                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                } else {
                    if (response.mainData && response.mainData.error_message) {
                        dispatch(setPageError(response.mainData.error_message))
                    }

                    else if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") || response.mainData.status === 'failed' || response.mainData.status === "failure")) {
                        dispatch(setPageError(response.mainData.error_message || response.mainData.message || ''))
                    } else if (response.error) {
                        dispatch(setPageError(response.error))
                    } else {
                        dispatch(setPageError('Unexpected error occurred.'))
                    }
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting basic details'))
        } finally {
            setTimeout(() => {
                setLoaded(true)
            }, 2000);
        }
    };
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "Address Details without Proof", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })


        const geproductcode = localStorage.getItem('product_code')
        if (geproductcode == "Sentinel") {
            setproductcode(true)
        }

    }, [])
    useEffect(() => {
        const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
        const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

        handleStart();
        setTimeout(handleComplete, 1000);

        return () => handleComplete();
    }, [router]);

      interface SaveBasicDetailsResponse {
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
      }
    
      useEffect(() => {
        const getPersonalDetails = async () => {
          try {
            const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.personalDetail);
    
            if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
                setIsSentinalUser(response.mainData?.data?.is_sentinel_user)
            } else {
    
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
                    dispatch(setPageError(response.mainData.error_message || ''))
                } else if (response.error) {
                    dispatch(setPageError(response.error))
                } else {
                    dispatch(setPageError('Unexpected error occurred.'))
                }
            }
          } catch (error) {
            dispatch(setPageError('Error in fetching basic details'))
          }
        }
        getPersonalDetails();
      }, []);

    return (
        <PageWrapper>
            {loaded ?
                <div className={`${styles.addressPage}`}>
                   <PageHeader
                        title="Please provide your address details"
                        para="We'll use this address for important updates and notifications"
                    />
                    <div className={"page-content"}>
                        {!(productcode || isSentinalUser) ?
                            <h3>Communication address</h3>
                            :
                            <h3>Unit / Bn Address</h3>
                        }
                        <form onSubmit={formik.handleSubmit}>

                            {!isSentinalUser && (
                                <div className="input-wrapper">
                                    <SelectDropdown
                                        name="residenceType"
                                        id="residenceType"
                                        options={['Self Owned House', 'Rented House']}
                                        labelText={(productcode || isSentinalUser) ? "Unit Name" : "Residence Type"}
                                        value={formik.values.residenceType}
                                        onChange={(option) => formik.setFieldValue('residenceType', option)}
                                    />
                                    {formik.errors.residenceType && formik.touched.residenceType && (
                                        <div className="error">{formik.errors.residenceType}</div>
                                    )}
                                </div>
                            )}
                            

                            {/* Flat/House No. */}
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    name="flatHouseNo"
                                    placeholder=" "
                                    value={formik.values.flatHouseNo}
                                    onChange={(e) => {
                                        const formattedValue = validateAddressInput(e.target.value);
                                        formik.setFieldValue("flatHouseNo", formattedValue);
                                    }}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>Flat/House No.</label>
                                {formik.errors.flatHouseNo && formik.touched.flatHouseNo && (
                                    <div className="error">{formik.errors.flatHouseNo}</div>
                                )}
                            </div>

                            {/* Street */}
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    name="street"
                                    placeholder=" "
                                    value={formik.values.street}
                                    onChange={(e) => {
                                        const formattedValue = validateAddressInput(e.target.value);
                                        formik.setFieldValue("street", formattedValue);
                                    }}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>Street</label>
                                {formik.errors.street && formik.touched.street && (
                                    <div className="error">{formik.errors.street}</div>
                                )}
                            </div>

                            {/* Landmark (Optional) */}
                            <div className="input-wrapper">
                                <input
                                    type="text"
                                    name="landmark"
                                    placeholder=" "
                                    value={formik.values.landmark}
                                    onChange={(e) => {
                                        const formattedValue = validateAddressInput(e.target.value);
                                        formik.setFieldValue("landmark", formattedValue);
                                    }}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>Locality/Landmark</label>
                                {formik.errors.landmark && formik.touched.landmark && (
                                    <div className="error">{formik.errors.landmark}</div>
                                )}
                            </div>

                            {/* PIN Code */}
                            <div className="input-wrapper">
                                {/* <input
                                type="text"
                                name="pincode"
                                maxLength={7}
                                minLength={6}
                                placeholder=" "
                                value={formik.values.pincode}
                                onChange={async (e) => {
                                    formik.handleChange(e)
                                    //verifyPincode(e.target.value)
                                    const value = e.target.value ? e.target.value.replace(/[^0-9]/g, "") : '';
                                    const formattedValue = value ? value
                                        .match(/.{1,3}/g)
                                        ?.join(" ")
                                        .slice(0, 7) : '';
                                    formik.setFieldValue("pincode", formattedValue);
                                    await verifyPincode(e.target.value.split(' ').join(''))
                                    if (formattedValue?.length === 7) {
                                        setTimeout(() => e.target.blur(), 100)
                                    }
                                }}
                                onBlur={formik.handleBlur}
                                className="form-control"
                            /> */}
                                <input
                                    type="text"
                                    name="pincode"
                                    inputMode='numeric'
                                    maxLength={7}
                                    minLength={6}
                                    placeholder=" "
                                    value={formik.values.pincode}
                                    onChange={async (e) => {
                                        formik.handleChange(e)
                                        //verifyPincode(e.target.value)
                                        const value = e.target.value ? e.target.value.replace(/[^0-9]/g, "") : '';
                                        const formattedValue = value ? value
                                            .match(/.{1,3}/g)
                                            ?.join(" ")
                                            .slice(0, 7) : '';
                                        formik.setFieldValue("pincode", formattedValue);
                                        await verifyPincode(e.target.value.split(' ').join(''))
                                        if (formattedValue?.length === 7) {
                                            setTimeout(() => e.target.blur(), 100)
                                        }
                                    }}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>PIN Code</label>
                                {formik.errors.pincode && formik.touched.pincode && (
                                    <div className="error">{formik.errors.pincode}</div>
                                )}
                            </div>

                            {/* City */}
                            <div className="input-wrapper" style={{ pointerEvents: 'none' }}>
                                <input
                                    type="text"
                                    name="city"
                                    readOnly
                                    placeholder=" "
                                    value={formik.values.city}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>City</label>
                                {formik.errors.city && formik.touched.city && (
                                    <div className="error">{formik.errors.city}</div>
                                )}
                            </div>

                            {/* State */}
                            <div className="input-wrapper" style={{ pointerEvents: 'none' }}>
                                <input
                                    type="text"
                                    name="state"
                                    placeholder=" "
                                    readOnly
                                    value={formik.values.state}
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                    className="form-control"
                                />
                                <label>State</label>
                                {formik.errors.state && formik.touched.state && (
                                    <div className="error">{formik.errors.state}</div>
                                )}
                            </div>
                            <div className="bottom-footer p-0">
                                <p className="secure-tag">
                                    <Image src={ShieldIcon} alt="Shield" /> Your data is 100% safe & secure
                                </p>
                                <button type="submit" className={`btn btn-primary ${!formik.isValid || !formik.dirty || !pincodeValid ? 'disabled' : ''}`}>Continue</button>
                            </div>
                        </form>
                    </div>
                </div>
                :

                <LoadingComp />
            }
            <CtEvents data={ctData} />
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </PageWrapper>
    );
}

'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import styles from './kycloading.module.scss'
import { apiRequest, fetchCommonApi } from '../utils/api'; // Common API call file
import { setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import store, { RootState } from '../store/store';
import { ENDPOINTS } from '../utils/endpoints';
import { setCommonData } from '../store/slices/commonSlice';
import { useSearchParams } from "next/navigation";
import loadingAnimation from "../../../public/Loader_Red.json"
import dynamic from "next/dynamic";
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
import successImg from '../images/success-icon.svg'
import Image from 'next/image';
import CtEvents from '../utils/Ctevents';
import ToastMessage from "@/app/components/ToastMessage/ToastMessage";
function KycLoading() {
    const dispatch = useDispatch();
    const searchParams = useSearchParams();
    const errorparam = searchParams.get(`error`);

    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);
    const [kycsuccess, setkycsuccess] = useState<boolean>(false)
    const [isKycFailed, setKycFailed] = useState<boolean>(false)
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);


    interface SaveBasicDetailsResponse {
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
        redirection_url: string,
        okyc_status: any
    }
    const checkKycStatus = async () => {
        try {
            const payload = {
                "sessionId": localStorage.getItem('digilocker_session_id'),
                "authToken": localStorage.getItem('digilocker_auth_token'),
            }
            const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.digilocker_webview_callback, payload);
            if (response.mainData && response.mainData?.okyc_status === "SUCCESS") {
                setkycsuccess(true)
                const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                const event_name = 'Screen View'
                const productcode = localStorage.getItem("product_code")

                const event_property = { "Screen Name": "Aadhaar Success", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
                setCtdata({ event_name, event_property })
                setTimeout(async () => {
                    const commonData = await fetchCommonApi();
                    store.dispatch(setCommonData(commonData));
                }, 3000);

            } else if (response.mainData?.okyc_status === "FAILED") {
                setKycFailed(true)
                setTimeout(() => {
                    callCommon()
                }, 1000);


            } else {
                setkycsuccess(false)
                setTimeout(() => {
                    checkKycStatus()
                }, 3000);
            }
        } catch (error) {
            setkycsuccess(false)
        }
    }
    useEffect(() => {
        if (errorparam) {
            callCommon()
        }
        else {
            checkKycStatus()
        }
    }, [])
    const callCommon = async () => {
        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
    }
    return (
        <>
            {!kycsuccess ?
                <div className='external-wrapper'>
                    <div className='page-content'>
                        <div className={styles.kycContent}>
                            <div className={styles.kycCard}>
                                {/* <Image src={successImg} alt="Congratulations" /> */}
                                <Lottie
                                    animationData={loadingAnimation}
                                    loop={true}
                                    style={{ height: 100, width: 100 }} // Adjust size as needed
                                />
                                <p>Please wait while fetching Kyc detail</p>
                            </div>
                        </div>
                    </div>
                </div>
                :
                <div className='external-wrapper'>
                    <div className='page-content'>
                        <div className={styles.kycContent}>
                            <div className={styles.kycCard}>
                                <Image src={successImg} alt="Congratulations" />
                                <h3>Success!</h3>
                                <p>Your KYC documents submitted <br />successfully!</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
            <CtEvents data={ctData} />

            {isKycFailed ? <ToastMessage color='red'>Your KYC verification was unsuccessful. Please try again.</ToastMessage> : null}

        </>
    );
}

export default KycLoading;

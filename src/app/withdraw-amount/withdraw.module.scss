@import '../scss/variable.scss';

.amountPage {
    position: relative;

    header {
        margin-bottom: 0;

        h1 {
            margin-top: 90px;
        }

        p {
            margin-top: 28px;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 12px;
        }
    }
    .pagecontent {
        min-height: calc(100vh - 132px);
    }
    .bottomfooter {
        margin-top: auto;
        padding-bottom: 0 !important;
        > div {
            margin-bottom: 20px !important;
        }
    }
    
}

.messageTimeout{
    header{
        h1{
            margin-top: 0 !important;
        }
    }
}

.loanAmount {
    color: $color-black;
    line-height: 1;
    width: 100%;
    background-color: $color-light;
    border-radius: 12px;
    margin-bottom: 12px;
    overflow: hidden;

    .loanAmountContainer {
        padding: 25px 12px;
    }

    .amount {
        font-size: 32px;
        font-weight: 600;
        line-height: 1;
        margin-bottom: 18px;
    }

    .breakpoints {
        font-size: 12px;
        font-weight: 500;
        color: $color-gray;
    }
}

.repayWrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 18px;
}

.repayBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: $color-black;
    padding: 16px;
    line-height: 1;
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
    background-color: #F5F5F5;
    border-radius: 12px;

    span {
        font-size: 16px;
        font-weight: 600;
    }
}

.loanDetailsHeading {
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    margin-bottom: 18px;
}
.kycContent{
    text-align: center;
    margin-top: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .loader {
        width: 120px;
        height: 20px;
        -webkit-mask: radial-gradient(circle closest-side,#000 94%,#0000) left/20% 100%;
        background: linear-gradient(#FF002B 0 0) left/0% 100% no-repeat #ddd;
        margin: 0 auto;
        margin-bottom: 40px;
        animation: l17 1s infinite alternate steps(600);
      }
      @keyframes l17 {
          100% {background-size:120% 100%}
      }
}
.loanAmountWrapper {
    padding: 20px 12px;
    border-radius: 12px;
    margin-bottom: 34px;
    border: 1px solid $color-light;

    .emiBox {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:not(:last-of-type) {
            margin-bottom: 25px;
        }

        span {
            font-size: 14px;
            font-weight: 500;
        }

        span:last-of-type {
            font-weight: 600;
        }
    }
}

.breakpoints {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    font-weight: 500;
    line-height: 16px;
    margin-top: 14px;
    color: $color1;
    padding: 0 8px;

    span {
        position: relative;
    }
}

.dots {
    display: flex;
    top: 10px;
    position: relative;
    justify-content: space-around;

    /*>div {
        width: 25%;
        display: flex;
        justify-content: space-around;
    }*/

    .dot {
        content: '';
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: $light-gray;

        &.selected {
            background-color: rgba(210, 210, 210, 0.3);
        }
    }
}

.slider {
    appearance: none;
    width: 100%;
    height: 12px;
    background: $color-white;
    border-radius: 9px;
    outline: none;
    transition: background 0.3s ease;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 42px;
    height: 42px;
    //background: $color-white;
    border-radius: 50%;
    //border: 4px solid $color1;
    cursor: pointer;
    position: relative;
    z-index: 2;
    background-position: center;
    background-size: cover;
    background-image: url('../images/slider-thumb.svg');
}

.slider::-moz-range-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 42px;
    height: 42px;
    //background: $color1;
    border-radius: 50%;
    //border: 4px solid $color-white;
    cursor: pointer;
    position: relative;
    z-index: 2;
    background-position: center;
    background-size: cover;
    background-image: url('../images/slider-thumb.svg');
}

.bottomText {
    background-color: #FFF8E0;
    padding: 15px;
    font-weight: 600;

    .collapseLink {
        cursor: pointer;
    }

    &.collapsed {
        .collapseLink {
            img {
                transform: rotate(180deg);
            }
        }
    }

    p,
    span {
        font-size: 14px !important;
        line-height: 20px;
    }

    span {
        margin-left: 6px;
    }
}

.withdrawSuccessBatch {
    position: absolute;
    left: 16px;
    top: 10px;
    width: 100%;
    max-width: calc(100% - 32px);
    z-index: 2;
}

.consentCheck {
    margin-bottom: 40px;
}

.creditPara {
    font-weight: 500;
    margin-top: 5px;
}
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface WDAmountState {
  pageerror: string | null;
  pagesuccess: string | null;
  loanData: any;
  locData: any;
  breakpoints: any[];
  amount: string;
  sliderDisabled: boolean;
  isChecked: boolean;
  higherCredit: boolean;
}

const initialState: WDAmountState = {
  pageerror: null,
  pagesuccess: null,
  loanData: null,
  locData: null,
  breakpoints: [],
  amount: '',
  sliderDisabled: true,
  isChecked: false,
  higherCredit: false,
};

const amountSlice = createSlice({
  name: 'amount',
  initialState,
  reducers: {
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setLoanData: (state, action: PayloadAction<string | null>) => {
      state.loanData = action.payload;
    },
    setLocData: (state, action: PayloadAction<string>) => {
      state.locData = action.payload;
    },
    setIsChecked: (state, action: PayloadAction<boolean>) => {
      state.isChecked = action.payload;
    },
    setHigherCredit: (state, action: PayloadAction<boolean>) => {
      state.higherCredit = action.payload;
    },
    setBreakpoints: (state, action) => {
        state.breakpoints = action.payload;
    },
    setAmount(state, action) {
        state.amount = action.payload;
    },
    setSliderDisabled(state, action) {
        state.sliderDisabled = action.payload;
    },
  },
});

export const {
  setPageError,
  setPageSuccess,
  setLoanData,
  setLocData,
  setIsChecked,
  setBreakpoints,
  setAmount,
  setSliderDisabled,
  setHigherCredit,
} = amountSlice.actions;

export default amountSlice.reducer;

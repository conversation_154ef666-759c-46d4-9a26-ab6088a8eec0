'use client';

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import '../scss/button.scss';
import '../scss/form.scss';
import styles from './withdraw.module.scss'
import PageHeader from '../components/PageHeader';
import { formatCurrency } from '../store/middleware/currencyFormatter';
import SuccessBatch from '../components/SuccessBatch';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import { setAmount, setBreakpoints, setHigherCredit, setIsChecked, setLoanData, setLocData, setPageError, setPageSuccess, setSliderDisabled } from './withdraw.slice';
import { useRouter } from 'next/navigation';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { ENDPOINTS } from '../utils/endpoints';
import Image from 'next/image';
import chevDown from '../images/chevron-down-blue.svg'
import { setCommonData } from '../store/slices/commonSlice';
import store, { RootState } from '../store/store';
import PageWrapper from '../components/PageWrapper';
import LoadingComp from '../component/loader';
import { de } from 'date-fns/locale';
import CtEvents from '../utils/Ctevents';

const WithdrawAmount = () => {

    const dispatch = useDispatch()
    const router = useRouter()
    const [loader, setLoader] = useState<boolean>(true);
    const [timedout, setTimedout] = useState<boolean>(false);
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
    const [loaded, setLoaded] = useState<boolean>(false);
    const [btnDisable, setBtnDisable] = useState<boolean>(false);

    const {
        amount, sliderDisabled, loanData, breakpoints, locData, pageerror, pagesuccess, isChecked, higherCredit
    } = useSelector((state: RootState) => state.amount);

    const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        dispatch(setSliderDisabled(true));
        const value = Number(e.target.value);
        const max = breakpoints.length - 1;
        const percentage = (value / max) * 100;
        e.target.style.background = `linear-gradient(to right, var(--black-color) ${percentage}%, var(--white-color) ${percentage}%)`;
        dispatch(setAmount(breakpoints[value]));
    }

    const handleSliderChangeEnd = async () => {
        setLoaded(false)
        await fetchLoanDetails(+amount);
        setLoaded(true)
    }

    const fetchLoanDetails = async (amount: number) => {
        dispatch(setPageError(''));
        dispatch(setPageSuccess(''));
        setLoader(true);
        setBtnDisable(false);
        try {
            const payload = {
                amount
            };
            const response = await apiRequest<ApiResponse>("GET", `${ENDPOINTS.getloctenure}?amount=${amount}`);
            if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
                dispatch(setLoanData(response.mainData.data));
                setLoader(false)
                const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                const event_name = 'LOC_Offer Generated'
                const productcode = localStorage.getItem("product_code")

                const event_property = { "Sanction Amount": response.mainData.data.amount_grossup, "Disbursed Amount": response.mainData.data.amount, "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non-STP" : "STP", "Duration": response.mainData.data.tenure_details[0].tenure, "Expected EMI amount": response.mainData.data.tenure_details[0].expected_emi_amount }
                setCtdata({ event_name, event_property })
                setTimeout(() => {
                    const slider = document.querySelector(`.${styles.slider}`) as HTMLInputElement;
                    if (slider) {
                        const max = breakpoints.length - 1;
                        const percentage = (breakpoints.indexOf(amount) / max) * 100;
                        slider.style.background = `linear-gradient(to right, var(--black-color) ${percentage}%, var(--white-color) ${percentage}%)`;
                    }
                }, 100);
            } else {
                setBtnDisable(true);
                dispatch(setLoanData(null));
                dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
            }
            setTimeout(() => {
                dispatch(setSliderDisabled(false))
            }, 800);
            if (!timedout) {
                setTimeout(() => {
                    setTimedout(true)
                }, 3000);
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting address confirmation'));
            setTimeout(() => {
                dispatch(setSliderDisabled(false))
            }, 800);
        }finally{
            setLoader(false);
        }
    }

    const fetchLocWithdraw = async () => {
        dispatch(setPageError(''));
        dispatch(setPageSuccess(''));
        try {
            const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.getlocwithdrawform);
            if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
                dispatch(setLocData(response.mainData.data));
                if(response.mainData.data.remaining_loc > 500000) {
                    setBtnDisable(true);
                }
                const bpArr: any = [];
                let minAmount = response.mainData.data.minimum_request_amount;
                let step = response.mainData.data.request_amount_increment_step;
                let remainingAmount = parseInt(response.mainData.data.remaining_loc);
                for (let i = minAmount; i <= remainingAmount; i += step) {
                    //bpArr.push(i);
                    if (i + step > remainingAmount && i !== remainingAmount) {
                        bpArr.push(remainingAmount);
                        break;
                    }
                    bpArr.push(i);
                }
                dispatch(setBreakpoints(bpArr))
                dispatch(setAmount(bpArr[bpArr.length - 1]));
                await fetchLoanDetails(response.mainData.data.remaining_loc)
                /*setTimeout(() => {
                    const slider = document.querySelector(`.${styles.slider}`) as HTMLInputElement;
                    if (slider) {
                        const max = bpArr.length - 1;
                        const percentage = (bpArr.indexOf(response?.mainData?.data.loc_limit) / max) * 100;
                        slider.style.background = `linear-gradient(to right, var(--black-color) ${percentage}%, var(--white-color) ${percentage}%)`;
                    }
                }, 100);*/
            } else {
                dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting address confirmation'));
        }
    }

    const navigateToLoanScreen = async () => {
        // dispatch(setPageError(''));
        // dispatch(setPageSuccess(''));
        // if(is2LakhLimit || +amount > 200000) {
        //     setTimeout(() => {
        //         dispatch(setPageError('Max single withdrawal is ₹2 Lakh. You can make multiple withdrawals up to your limit.'));
        //     }, 0)
        //     return;
        // }
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Button Clicked'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "CTA": ' Transfer Loan Amount Now', "Action": "Transfer", "Page Shown": "LOC_Offer Generated", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP ? "Non-STP" : "STP"}
        setCtdata({ event_name, event_property })
        router.push('/loandetail')
    }
    useEffect(() => {

        fetchLocWithdraw()
    }, [])
    const returnBackToApp = () => {
        window.location.assign('https://dev.stashfin.com?returnToApp=1&source=wealth')
    };

    return (
        <>
            <PageWrapper>
                <div className={`external-wrapper amount-page ${styles.amountPage} ${timedout ? styles.messageTimeout : ''}`}>
                    {!timedout && <SuccessBatch className={styles.withdrawSuccessBatch}>
                        <h3>Congratulations!</h3>
                        <p>You've a loan offer of ₹ {locData?.loc_limit}</p>
                    </SuccessBatch>}
                    <PageHeader title='Your loan, your way' back={true} para="Pick an amount from your approved limit" gating={false} />
                    <div className={`page-content ${styles.pagecontent}`}>
                        <div className="amount-section">
                            <div className={styles.loanAmount}>
                                <div className={styles.loanAmountContainer}>
                                    <div className={styles.amount}><span>₹</span><span>{amount ? Number(amount).toLocaleString("en-IN") : ""}</span></div>
                                    <div
                                        style={sliderDisabled ? {
                                            filter: 'saturate(0)',
                                            opacity: 0.4,
                                            pointerEvents: 'none',
                                        } : undefined}
                                    >
                                        <div className={styles.dots}>
                                            {Array.from({ length: 8 }).map((_: any, index: number) => (
                                                <div key={index} className={`${styles.dot} ${index <= (breakpoints.indexOf(parseInt(amount)) / 8) ? styles.selected : ''}`}></div>
                                            ))}
                                        </div>
                                        <input
                                            type="range"
                                            className={styles.slider}
                                            min="0"
                                            max={breakpoints.length - 1}
                                            step="1"
                                            value={breakpoints.indexOf(parseInt(amount))}
                                            onChange={handleSliderChange}
                                            // onMouseDown={() => dispatch(setPageError(''))}
                                            // onTouchStart={() => dispatch(setPageError(''))}
                                            onMouseUp={handleSliderChangeEnd}  // Detects when user releases mouse
                                            onTouchEnd={handleSliderChangeEnd} // Detects when user lifts finger (for mobile)
                                        />
                                        <div className={styles.breakpoints}>
                                            <span>₹{formatCurrency(Number(locData?.minimum_request_amount))}</span>
                                            <span>₹{formatCurrency(Number(locData?.remaining_loc))}</span>
                                        </div>
                                    </div>
                                </div>
                                <div className={`${styles.bottomText} ${higherCredit ? styles.collapsed : ''}`}>
                                    <p>Want a Higher Credit Line? <span className={`link ${styles.collapseLink}`} onClick={() => dispatch(setHigherCredit(!higherCredit))}>Check <Image src={chevDown} alt='Chevron' /></span></p>
                                    {higherCredit ? <p className={styles.creditPara}>After your first withdrawal, you can provide more details to increase your limit.</p> : null}
                                </div>
                            </div>
                            <div className={styles.repayWrapper}>
                                <div className={styles.repayBox}>
                                    <span>Duration</span>
                                    <span>{loanData?.tenure_details[0].tenure ? loanData?.tenure_details[0].tenure + " months" : 'N/A'}</span>
                                </div>
                                <div className={styles.repayBox}>
                                    <span>Expected EMI amt.</span>
                                    <span>{loanData?.tenure_details[0].expected_emi_amount ? "₹" + formatCurrency(Number(loanData?.tenure_details[0].expected_emi_amount)) : 'N/A'}</span>
                                </div>
                            </div>
                            {/* <h3 className={styles.loanDetailsHeading}>Your loan details</h3>
                            <div className={styles.loanAmountWrapper}>
                                <div className={styles.emiBox}>
                                    <span>Loan amount</span>
                                    <span>₹{loanData?.amount ? formatCurrency(Number(loanData.amount)) : ''}</span>
                                </div>
                                <div className={styles.emiBox}>
                                    <span>Amount you repay</span>
                                    <span>₹{loanData?.amount_grossup ? formatCurrency(Number(loanData.amount_grossup)) : ''}</span>
                                </div>
                            </div> */}

                        </div>

                        <div className={`bottom-footer p-0 ${styles.bottomfooter}`}>
                            <div className={`consent-check ${styles.consentCheck}`}>
                                <label>
                                    <div className={`checkbox ${isChecked ? 'checked' : ''}`}>
                                        <input
                                            type="checkbox"
                                            checked={isChecked}
                                            onChange={() => dispatch(setIsChecked(!isChecked))}
                                        />
                                        <div className={`circle ${!isChecked ? 'animate' : ''}`}></div>

                                    </div>
                                    <p>I confirm that my annual household income is greater than 3 lakhs.</p>
                                </label>
                            </div>
                            <button
                                type="button"
                                onClick={navigateToLoanScreen}
                                className={`btn btn-primary ${sliderDisabled || !isChecked || btnDisable ? 'disabled' : ''}`}
                            >
                                Confirm Loan Amount
                            </button>
                            <button
                                type="button"
                                className={`btn btn-primary-outline`}
                                onClick={() => returnBackToApp()}
                            >
                                Do it Later
                            </button>
                            <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
                        </div>
                    </div>
                </div>

            </PageWrapper>
            {loader ? <><LoadingComp faded={!loaded ? true : false} />
            </> : null}
            <CtEvents data={ctData} />
            {pageerror || pagesuccess ? <ToastMessage manualClose color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </>
    );
};

export default WithdrawAmount;

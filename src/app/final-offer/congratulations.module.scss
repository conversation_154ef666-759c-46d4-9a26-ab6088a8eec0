@import '../scss/variable.scss';

.congratulationsContent {
    text-align: center;
    margin-top: auto;
}

.congratulationsCard {
    img {
        margin-bottom: 10px;
    }

    >div {
        &:first-child {
            margin: 0 auto;
            margin-bottom: 10px;
        }
    }

    h3 {
        margin-bottom: 30px;
        font-weight: 700;
        font-size: 28px;
        line-height: 1;
    }

    h2 {
        margin-bottom: 15px;
        font-weight: 700;
        font-size: 40px;
        line-height: 1;
    }

    p {
        font-size: 16px;
        margin-bottom: 15px;
        font-weight: 500;
    }

    .note {
        background-color: $light-red;
        font-size: 14px;
        text-align: left;
        padding: 17px 26px;
        padding-right: 60px;
        border-radius: 12px;
        color: $color-black;
        line-height: 20px;
        font-weight: 500;
        background-repeat: no-repeat;
        background-position: right 27px top 17px;
        background-image: url('../images/sparkle.svg');
        margin-top: 21px;
    }
}

.info {
    font-size: 12px;
    font-weight: 500;
    text-align: left;
    color: #7b7b7b;
    margin-top: 50px;
    margin-bottom: 25px;
}
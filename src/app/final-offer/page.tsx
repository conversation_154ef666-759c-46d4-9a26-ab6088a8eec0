'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import finalofferimg from '../images/finalofferimg.svg'
import styles from './congratulations.module.scss'
import Image from 'next/image';
import { ApiResponse } from '../store/interface/apiInterface';
import { ENDPOINTS } from '../utils/endpoints';
import { useDispatch, useSelector } from 'react-redux';
import { setAddressList, setPageError, setPageSuccess, setSelectedAddress } from '../select-address/confirmaddress.slice';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { setCommonData } from '../store/slices/commonSlice';
import store from '../store/store';
import { RootState } from '../store/store';

//import { setCommonData } from '../store/slices/commonSlice';
//import store from '../store/store';
import HighFive from "../../../public/HighFive.json"
import dynamic from "next/dynamic";
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { setError } from '../store/features/bankSlice';
import CtEvents from '../utils/Ctevents';
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

function FinalOffer() {
    const [data, setData] = useState<any>([])
    const dispatch = useDispatch()
    const { pageerror, pagesuccess, addressList, selectedAddress } = useSelector((state: RootState) => state.confirmaddress);
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);

    const fetchFinalOffer = async () => {

        try {
            const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.finalOffer);
            if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
                //dispatch(setCustomerBankDetails(response.mainData.data));
                const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
                const event_name = 'LOC_Offer Shown'
                const productcode = localStorage.getItem("product_code")

                const event_property = { "Sanction Amount": response.mainData?.data?.loc_limit, "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source, "Salary": "", "Age": "" }
                setCtdata({ event_name, event_property })

                setData(response.mainData?.data)
            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else {
                    dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
                }
            }
        } catch (error) {
            //dispatch(setPageError('Error submitting address confirmation'));
        }
    }
    const createNextStep = async () => {
        const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.nextStep);
        if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
            const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
            const productcode = localStorage.getItem("product_code")

            const event_name = 'Button Clicked'
            const event_property = { "Page Name": "LOC_Offer Shown", "CTA": "Proceed with my offer", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
            setCtdata({ event_name, event_property })
            const commonData = await fetchCommonApi();
            store.dispatch(setCommonData(commonData));
        }
        else {
            debugger;
            dispatch(setPageError(response.mainData?.error_message || 'Error in fetching address list'));
        }
    }
    useEffect(() => {
        fetchFinalOffer()
    }, [])
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const event_property = { "Screen Name": "LOC_Additional Details Submiited", "Product category": cteventsfromstorage.productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    return (
        <div className='external-wrapper' style={{ height: '100vh' }}>
            {/*<PageHeader title='' back={false} nobg={true} />*/}
            <div className='page-content' style={{ minHeight: '100vh' }}>
                <div className={styles.congratulationsContent}>
                    <div className={styles.congratulationsCard}>
                        {/* <Lottie
                            animationData={HighFive}
                            loop={false}
                            style={{ height: 192, width: 192 }} // Adjust size as needed
                        /> */}
                        <Image src={finalofferimg} alt='' />
                        <p>You have a loan offer of</p>
                        <h2>₹{data.loc_limit ? Number(data.loc_limit).toLocaleString("en-IN") : ""}</h2>
                        {/* <h3>Great!</h3> */}
                        {/* <p className={styles.note}>You can enhance your loan offer by providing additional details as you progress through this journey.</p> */}
                        <p className={styles.note}>Loan offer increases are possible after your first transfer, with additional information.</p>
                    </div>
                </div>
                {/* <p className={styles.info}>Lending partners offer credit limits up to ₹5 Lakhs with tenures from 1 to 36 months. Platform fees start at 0.45% + GST, and interest rates from 11.99% p.a., based on user profile. Eligible users get up to 30 days of interest-free credit. Interest applies only to drawn amounts. Please refer to the KFS available at loan approval for details.</p> */}
                <div className="bottom-footer p-0" style={{ marginTop: 'auto' }}>
                    <button type="submit" className="btn btn-primary mb-0" onClick={createNextStep}>Proceed with My Offer</button>
                    {/*<button type="submit" className="btn btn-primary" onClick={createNextStep}>Proceed with My Offer</button>*/}
                    <p className='powered-by'>Powered by Akara Capital Advisors Private Limited</p>
                </div>

            </div>
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
            <CtEvents data={ctData} />


        </div>
    );
}

export default FinalOffer;

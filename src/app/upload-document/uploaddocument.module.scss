.body {
    padding-left: 16px;
    padding-right: 16px;
    min-height: calc(100vh - 205px);
    display: flex;
    flex-flow: column;

    >h1 {
        font-family: var(--font-mona-sans);
        font-weight: 700;
        font-size: 24px;
        line-height: 28.8px;
        color: #000;
        margin-top: 16px;
    }

    >p {
        font-family: var(--font-mona-sans);
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        margin-top: 16px;
        color: var(--black);
    }

    .box {
        border: 1px solid #E6E6E6;
        border-radius: 12px;
        display: flex;
        margin-top: 20px;
        padding: 16px;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        >div {
            display: flex;
            flex-flow: column;
            align-items: flex-start;

            p {
                display: flex;
                align-items: center;
                font-family: var(--font-mona-sans);
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                color: var(--black);
                text-transform: capitalize;

                img {
                    margin-left: 5px;
                    width: 49px;
                    height: 12px;
                }
            }

            span {
                margin: top 6px;
                font-family: var(--font-mona-sans);
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                color: var(--black);
            }

            .btn {
                border: 1px solid #000;
                border-radius: 66px;
                padding: 4px 10px;
                font-family: var(--font-mona-sans);
                font-weight: 500;
                font-size: 12px;
                line-height: 20px;
                color: #000;
                margin-top: 14px;
                display: flex;
                cursor: pointer;
            }
        }

        >img {
            width: 48px;
            height: 48px;
            margin-left: 16px;
        }
    }

    .footerbottombtn {
        margin-top: auto;

        .datasafety {
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-mona-sans);
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            color: var(--black);

            img {
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }
        }

        .btn {
            margin-top: 20px;
        }
    }
}
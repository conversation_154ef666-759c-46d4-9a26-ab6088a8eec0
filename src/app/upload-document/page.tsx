"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './uploaddocument.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import rentagreement from '../images/rentagreement.png'
import aadhar from '../images/aadhar.svg'
import shield from '../images/shield-icon.svg'
import BottomPopup from "../components/popups/BottomPopup"
import { useDispatch, useSelector } from "react-redux"
import { closeUploadModal, openUploadModal, setDocumentExtension, setDocumentsList, setPageError, setPageSuccess, setUploadedFile } from "./upload.slice"
import store, { RootState } from "../store/store"
import { useEffect, useRef, useState } from "react"
import { apiRequest, fetchCommonApi } from "../utils/api"
import { ApiResponse } from "../store/interface/apiInterface"
import ToastMessage from "../components/ToastMessage/ToastMessage"
import React from "react"
import { ENDPOINTS } from "../utils/endpoints"
import { setCommonData } from "../store/slices/commonSlice"
import PageWrapper from "../components/PageWrapper"

function UploadDocumentPage() {
  useThemeColor('#fff')
  const dispatch = useDispatch()
  const uploadInputRef = useRef<HTMLInputElement>(null);
  const { uploadedImage, isUploadModalOpen, pageerror, pagesuccess, documentsList, selectedDocument, selectedDocumentExtension } = useSelector((state: RootState) => state.uploaddoc);
  const MAX_FILE_SIZE = 5 * 1024 * 1024;
  const ALLOWED_FILE_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
  const [customerId, setCustomerId] = useState<string>("");

  const validateAndUploadFile = (file: File) => {
    if (!file) return;
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      alert('Supported formats: PDF, JPEG, PNG.');
      return;
    }
    if (file.size > MAX_FILE_SIZE) {
      alert('File size must be less than 5 MB.');
      return;
    }
    const reader = new FileReader();
    reader.onloadend = () => {
      dispatch(setUploadedFile(reader.result as string));
      dispatch(setDocumentExtension(file.type));
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndUploadFile(file);
    }
  };

  const handleBrowseFilesClick = () => {
    uploadInputRef.current?.click();
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file) {
      validateAndUploadFile(file);
    }
  };
  useEffect(() => {
    getDocs()
  }, [])
  const getDocs = async () => {
    try {
      const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.documentlist);
      if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") {
        dispatch(setDocumentsList(response.mainData.data))
        if (response.commonData.lead_history.at(-1).step_name == "SENTINEL_BASIC_DETAILS_1") {
        }
      } else {
        if (response.mainData && response.mainData.success && response.mainData.success.toString().toLowerCase() === "false") {
        } else if (response.error) {
        } else {
        }
      }
    } catch (error) {
    }

  }

  useEffect(() => {
    (async () => {
      const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.personalDetail);
      if(response.mainData?.success && response.mainData?.data) {
        setCustomerId(response.mainData?.data?.id)
      }
    })()
  }, [])

  const uploadDocument = async () => {
    dispatch(setPageError(''))
    dispatch(setPageSuccess(''))

    try {
      const payload = {
        customerId: customerId,
        docIdentifier: selectedDocument.docIdentifier,
        docFormat: selectedDocumentExtension?.includes('/') ? selectedDocumentExtension?.split('/')[1] : selectedDocumentExtension,
        docContent: uploadedImage.split(',')[1],
      }
      const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.uploaddocument, payload);
      if (response.mainData && ((response.mainData.success && response.mainData.success.toString().toLowerCase() === "true") || response.mainData.status === 'success')) {
        dispatch(setPageSuccess(response.mainData.data.message))
        dispatch(closeUploadModal())
      } else {
        // debugger;
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }
        else if (response.mainData?.success?.toString().toLowerCase() === 'false') {
          dispatch(setPageError(response.mainData.error_message || 'Error in uploading file'))
        } else if (response.error) {
          dispatch(setPageError(response.error))
        } else {
          dispatch(setPageError('Unexpected error occurred.'))
        }
      }
    } catch (error) {
      //dispatch(setPageError('Error submitting basic details'))
    }
  };
  const GotoNextstep = async () => {
    const commonData = await fetchCommonApi();
    store.dispatch(setCommonData(commonData));
  }
  return (
    <PageWrapper>
      <PageHeader title="Upload your documents" para="Submit the required documents to complete your loan application" />
      <div className={styles.body}>
        {documentsList?.map((document: any, index: number) => (<div className={styles.box} key={index}>
          <div>
            <p>{document.docName}</p>
            <div className={styles.btn} onClick={() => {
              dispatch(openUploadModal(document))
            }}>
              Upload
            </div>
          </div>
          {document.docName === 'aadhaar' &&
            <Image src={document?.docIcon || aadhar} alt='Doc' />
          }

        </div>))}
        {/*<div className={styles.box}>
          <div>
            <p>Rent Agreement </p>
            <div className={styles.btn} onClick={() => dispatch(openUploadModal())}>
              Upload
            </div>
          </div>
          <Image src={rentagreement} alt='' />

        </div>
        <div className={styles.box}>
          <div>
            <p>Udyog Aadhaar Certificate</p>
            <div className={styles.btn} onClick={() => dispatch(openUploadModal())}>
              Upload
            </div>
          </div>
          <Image src={msme} alt='' />
        </div>*/}
        <div className={styles.footerbottombtn}>
          <div className={styles.datasafety}>
            <Image src={shield} alt="" /> Your data is 100% safe & secure
          </div>
          <div className={`footerbottombtn ${styles.btn}`} onClick={GotoNextstep}>
            Continue
          </div>
        </div>
      </div>

      <BottomPopup
        isOpen={isUploadModalOpen}
        onClose={() => {
          dispatch(closeUploadModal());
        }}
        title="Upload Document"
        buttons={[
          {
            label: 'Upload & Save',
            onClick: () => {
              uploadDocument()
            },
            className: `mb-0 ${uploadedImage ? '' : 'disabled'}`
          }
        ]}
      >
        <div>
          <p style={{ textAlign: 'left' }}>Max Size: 5 MB</p>
          <p style={{ textAlign: 'left', marginBottom: '15px' }}>Supported Formats: .PDF, PNG, JPEG.</p>
          <div className="upload-btn">
            <button
              className="btn btn-primary-outline"
              onClick={handleBrowseFilesClick}
              type="button"
            >
              Browse files
            </button>
            {/* Hidden file input to handle file selection */}
            <input
              type="file"
              ref={uploadInputRef}
              style={{ display: 'none' }}
              accept="application/pdf,image/jpeg,image/png"
              onChange={handleFileInputChange}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            />
          </div>
        </div>
      </BottomPopup>
      {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
    </PageWrapper>
  )
}

export default UploadDocumentPage
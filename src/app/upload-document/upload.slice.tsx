import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface UploadState {
    pageerror: string | null;
    pagesuccess: string | null;
    fileerror: string | null;
    documentsList: any;
    selectedDocument: any;
    isUploadModalOpen: boolean;
    uploadedImage: string;
    selectedDocumentExtension: string | null;
}

// Define the initial state
const initialState: UploadState = {
    pageerror: null,
    pagesuccess: null,
    fileerror: null,
    documentsList: [],
    selectedDocument: null,
    selectedDocumentExtension: null,
    isUploadModalOpen: false,
    uploadedImage: '',
};

// Create the slice
const uploadSlice = createSlice({
    name: 'uploaddoc',
    initialState,
    reducers: {
        setPageError: (state, action: PayloadAction<string | null>) => {
            state.pageerror = action.payload;
        },
        setPageSuccess: (state, action: PayloadAction<string | null>) => {
            state.pagesuccess = action.payload;
        },
        openUploadModal: (state, action: PayloadAction<any>) => {
            state.selectedDocument = action.payload;
            state.isUploadModalOpen = true;
        },
        closeUploadModal: (state) => {
            state.selectedDocument = null;
            state.isUploadModalOpen = false;
        },
        setFileError: (state, action: PayloadAction<string | null>) => {
            state.fileerror = action.payload;
        },
        setUploadedFile: (state, action: PayloadAction<string>) => {
            state.uploadedImage = action.payload;
        },
        setDocumentsList: (state, action: PayloadAction<any>) => {
            state.documentsList = action.payload;
        },
        setDocumentExtension: (state, action: PayloadAction<string>) => {
            state.selectedDocumentExtension = action.payload;
        },
    },
});

// Export actions
export const {
    setPageError,
    setPageSuccess,
    openUploadModal,
    closeUploadModal,
    setFileError,
    setUploadedFile,
    setDocumentsList,
    setDocumentExtension
} = uploadSlice.actions;

// Export the reducer
export default uploadSlice.reducer;

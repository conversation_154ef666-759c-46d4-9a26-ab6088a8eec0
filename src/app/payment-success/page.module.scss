@import '../scss/variable.scss';

.externalWrapper {
    background-color: #D5FAE6;
    text-align: center;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    h1 {
        margin-top: 0;
    }

    h2 {
        font-size: 36px;
        line-height: 48px;
        font-weight: 600;
    }

    .date {
        font-weight: 500;
        font-size: 14px;
        line-height: 16.8px;
        color: #303030;
        margin-top: 20px;
    }

    p {
        font-weight: 600;
        font-size: 14px;
        margin-top: 25px;
        line-height: 16.8px;
    }

    header {
        padding-bottom: 20px;
        width: 100%;
        margin-bottom: 0;
    }
}
'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import SuccessIcon from '../images/success-icon.svg'
import calendarIcon from '../images/calendar-icon.svg'
import fileIcon from '../images/file-icon.svg'
import styles from './page.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import { fetchCommonApi } from '../utils/api';
import { setCommonData } from '../store/slices/commonSlice';
import store from '../store/store';
import CtEvents from '../utils/Ctevents';
function Page() {
    const [dateTime, setDateTime] = useState("");
    const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);


    useEffect(() => {
        setTimeout(async () => {
            const commonData = await fetchCommonApi();
            store.dispatch(setCommonData(commonData));
        }, 3000);
        const updateDateTime = () => {
            const now = new Date();
            const options: Intl.DateTimeFormatOptions = {
                day: "numeric",
                month: "short",
                hour: "2-digit",
                minute: "2-digit",
                hour12: true,
            };
            setDateTime(now.toLocaleString("en-US", options).replace(/(\d{1,2}) (\w{3}),/, "$1 $2,"));
        };

        updateDateTime(); // Initial call
        const interval = setInterval(updateDateTime, 60000); // Update every minute

        return () => clearInterval(interval); // Cleanup interval
    })
    useEffect(() => {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'Screen View'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Screen Name": "cb_manual_check_payment_success", "Product category": 'cb', "amount":385, "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })
    }, [])
    return (
        <div className={`external-wrapper ${styles.externalWrapper}`}>
            <PageHeader title='Payment Successful' para="" centeredHeader={true} call={false} back={false} nobg={true} bgcolor="#D5FAE6" icon={<Image src={SuccessIcon} alt='Header' />} />
            <h2>₹385.04</h2>
            <div className={styles.date}>{dateTime}</div>
            <p>We have received your request</p>
            <CtEvents data={ctData} />

        </div>
    );
}

export default Page;

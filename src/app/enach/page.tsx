"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './enach.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import upi from '../images/enach.svg'
import nomoney from '../images/nomoney.svg'
import duedate from '../images/duedata.svg'
import duewatch from '../images/duewatch.svg'
import defaultbank from '../images/defaultbank.svg'
import shield from '../images/shield-icon.svg'
import '../scss/button.scss'
import React, { useEffect, useState } from "react"
import { apiRequest } from "../utils/api"
import { setCustomerBankDetails, setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { ENDPOINTS } from '../utils/endpoints';
import ToastMessage from "../components/ToastMessage/ToastMessage"
import { ApiResponse } from "../store/interface/apiInterface"
import PageWrapper from "../components/PageWrapper"
import LoadingComp from "../component/loader"
import { useRouter } from "next/navigation";
import CtEvents from "../utils/Ctevents"
import BankImage from "../components/BankImage"


function EnachPage() {
  const router = useRouter()
  useThemeColor('#fff')
  const dispatch = useDispatch();
  const { pageerror, pagesuccess, customerBankDetails } = useSelector((state: RootState) => state.register);
  const [loaded, setLoaded] = useState<boolean>(false)
  const [ctData, setCtdata] = useState<{ event_name: string; event_property: Record<string, any> } | null>(null);
  const [accountno, setaccountno] = useState('')
  const [bankname, setbankname] = useState('')
  



  interface SaveBasicDetailsResponse {
    filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
    success: boolean | string;
    error_message?: string;
    data: Record<string, any>; // Adjust according to the actual data structure you expect
    redirection_url: string;
    error: string;
    redirectUrl: string,
    bank_name: string
  }
  const fetchUserBank = async () => {
    dispatch(setPageError(''));
    dispatch(setPageSuccess(''));

    try {

      const response = await apiRequest<ApiResponse>("GET", ENDPOINTS.customerBankList);
      if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
        const filteredData = response.mainData?.data?.filter((item: any) => item.is_primary)
        setaccountno(filteredData[0].account_number)
        setbankname(filteredData[0].bank_name)
        dispatch(setCustomerBankDetails(filteredData));
      } else {
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }
        else {
          dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
        }
      }
    } catch (error) {
      //dispatch(setPageError('Error submitting address confirmation'));
    }
  }
  useEffect(() => {
    fetchUserBank()
  }, [])
  const checkKycStatus = async () => {
    setLoaded(false)

    try {
      const payload = {
        "clientId": "customer-service",
      }
      const response = await apiRequest<SaveBasicDetailsResponse>("POST", ENDPOINTS.enachmandate, payload);

      if (response.mainData && response.mainData.redirectUrl) {
        const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
        const event_name = 'eNach_Start'
        const productcode = localStorage.getItem("product_code")

        const event_property = { "Mandate flow": "NACH", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
        setCtdata({ event_name, event_property })

        window.location.assign(response.mainData.redirectUrl)
      } else {
        // debugger;
        if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message))
        }

        else if (response.mainData && response.mainData.error) {
          // debugger;
          dispatch(setPageError(response.mainData.error));
        }
        else if (response.mainData && response.mainData.error_message) {
          dispatch(setPageError(response.mainData.error_message));

        }
        else if (response.error) {
          // debugger;
          dispatch(setPageError(response.error));

        } else {
        }
      }
    } catch (error) {
    } finally {
      setTimeout(() => {
        setLoaded(true)
      }, 2000);
    }
  }
  const formatAccountNumber = (accountNumber: any) => {
    if (!accountNumber || accountNumber.length < 4) return accountNumber; // Handle edge cases
    return `XXXX XXXX ${accountNumber.slice(-4)}`;
  };
  useEffect(() => {
    const cteventsfromstorage = JSON.parse(localStorage.getItem('eventsdata') || 'null')
    const event_name = 'eNach_Open'
    const productcode = localStorage.getItem("product_code")

    const event_property = { "Account number": formatAccountNumber(accountno), "Bank Name": bankname, "Mandate Amount": "", "Screen Name": "NACH Setup", "Product category": productcode, "Segment": cteventsfromstorage.isNonSTP === true ? "Non STP" : "STP", "Source": cteventsfromstorage?.source }
    setCtdata({ event_name, event_property })
  }, [])
  useEffect(() => {
    const handleStart = () => { document.body.classList.add("loading"); setLoaded(false) }
    const handleComplete = () => { document.body.classList.remove("loading"); setLoaded(true) }

    handleStart();
    setTimeout(handleComplete, 1000);

    return () => handleComplete();
  }, [router]);
  return (
    <PageWrapper>
      {loaded ?
        <>
          <div className={styles.header}>
            <PageHeader title="" />
          </div>
          <div className={styles.body}>
            <h1>Final step, set up <br />auto pay
              <Image src={upi} alt="" />
            </h1>
            <div className={styles.whybox}>
              <div className={styles.title}>
                Why is this required?
              </div>
              <div className={styles.subtitle}>
                This step is only to authorize UPI auto pay.
              </div>
              <div>
                <div>
                  <Image src={nomoney} alt="" />
                  <p>
                    No money deduction now
                  </p>
                </div>
                <div>
                  <Image src={duedate} alt="" />
                  <p>
                    EMI debits only when due
                  </p>
                </div>
                <div>
                  <Image src={duewatch} alt="" />
                  <p>
                    Never miss
                    a due date
                  </p>
                </div>
              </div>
            </div>
            {customerBankDetails?.map((bank: { is_primary: any; ifsc_code: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined; account_number: string | number | bigint | boolean | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | Promise<string | number | bigint | boolean | React.ReactPortal | React.ReactElement<unknown, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined> | null | undefined; bank_logo_url: any, bank_name: any }, index: React.Key | null | undefined) => (
              bank.is_primary && (
                <div key={index} className={styles.box}>
                  <div>
                    <p>{bank.bank_name}</p>
                    <span>{formatAccountNumber(bank.account_number)}</span>
                  </div>
                  {/* <Image src={bank.bank_logo_url || defaultbank} alt='' /> */}
                  <BankImage bankName={bank.bank_name} ratioHeight={48} />

                </div>
              )
            ))}
            {/*<p className={styles.bankChange} onClick={() => router.push('/select-bank')}><span className="link">Change Bank Account</span></p>*/}

            <div className={styles.setupmandate}>
              Set up mandate to get funds
            </div>
            <p className={styles.note}>Do not worry, there is no money deduction</p>
            <div className='bottom-footer mt-auto'>
              <p className="secure-tag">
                <Image src={shield} alt="" /> Your data is 100% safe & secure
              </p>
              <button className={`btn btn-primary`} onClick={checkKycStatus}>
                Continue
              </button>
            </div>

          </div>
        </>
        :
        <LoadingComp />
      }
      <CtEvents data={ctData} />
      {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

    </PageWrapper>
  )
}

export default EnachPage;
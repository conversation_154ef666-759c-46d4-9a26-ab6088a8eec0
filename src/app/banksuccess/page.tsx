'use client'
import React, { useEffect } from 'react';
import '../scss/button.scss';
import successImg from '../images/success-icon.svg'
import styles from './kyc.module.scss'
import Image from 'next/image';
import { fetchCommonApi } from '../utils/api';
import { setCommonData } from '../store/slices/commonSlice';
import store from '../store/store';
function BankSuccess() {
    const fetchCommon =async () => {
        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
    }
    useEffect(() => {
        setTimeout(() => {
            fetchCommon()
        }, 3000);
    })
    return (
        <div className='external-wrapper'>
            <div className='page-content'>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        <Image src={successImg} alt="Congratulations" />
                        <h3>Success!</h3>
                        <p>Your bank details verified <br />successfully!</p>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default BankSuccess;

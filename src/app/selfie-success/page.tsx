'use client'
import React, { useEffect } from 'react';
import '../scss/button.scss';
import successImg from '../images/selfie-success.png'
import styles from './kyc.module.scss'
import Image from 'next/image';
import PageHeader from '../components/PageHeader';
import PageWrapper from '../components/PageWrapper';
import { fetchCommonApi } from '../utils/api';
import { setCommonData } from '../store/slices/commonSlice';
import store from '../store/store';
function SelfieSuccess() {
    const checkCommon = async () => {

        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
    }
    useEffect(()=>{
        setTimeout(() => {
            checkCommon();
        }, 3000);
    },[])
    return (
        <PageWrapper>
            <div className={`external-wrapper ${styles.selfieStatusPage}`}>
                <PageHeader title='' />
                <div className='page-content'>
                    <div className={styles.kycContent}>
                        <div className={styles.kycCard}>
                            <Image src={successImg} alt="Congratulations" />
                            <h3>Success!</h3>
                            <p>Your selfie captured and verified successfully!</p>
                        </div>
                    </div>
                </div>
            </div>
        </PageWrapper>
    );
}

export default SelfieSuccess;

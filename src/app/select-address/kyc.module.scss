@import '../scss/variable.scss';

.addressContent {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
}

.pageContent {
    min-height: calc(100vh - 190px);
}

.addressCard {
    border-radius: 12px;
    border: 1px solid #e5e5e5;
    padding: 15px;
    display: flex;
    gap: 15px;
    justify-content: flex-start;
    align-items: flex-start;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;

    .radio {
        margin-top: 2px;
    }
}
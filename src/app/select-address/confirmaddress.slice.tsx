import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define types for the state
export interface AddressState {
  pageerror: string | null;
  pagesuccess: string | null;
  addressList: any;
  selectedAddress: any;
}

// Define the initial state
const initialState: AddressState = {
  pageerror: null,
  pagesuccess: null,
  addressList: null,
  selectedAddress: null
};

// Create the slice
const confirmAddressSlice = createSlice({
  name: 'confirmaddress',
  initialState,
  reducers: {
    setPageError: (state, action: PayloadAction<string | null>) => {
      state.pageerror = action.payload;
    },
    setPageSuccess: (state, action: PayloadAction<string | null>) => {
      state.pagesuccess = action.payload;
    },
    setAddressList: (state, action: PayloadAction<string>) => {
      state.addressList = action.payload;
    },
    setSelectedAddress: (state, action: PayloadAction<any>) => {
      state.selectedAddress = action.payload;
    },
  },
});

// Export actions
export const {
  setPageError,
  setPageSuccess,
  setAddressList,
  setSelectedAddress
} = confirmAddressSlice.actions;

// Export the reducer
export default confirmAddressSlice.reducer;

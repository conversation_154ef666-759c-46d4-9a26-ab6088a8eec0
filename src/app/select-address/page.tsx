'use client'
import React, { useEffect, useState } from 'react';
import '../scss/button.scss';
import '../scss/form.scss';
import styles from './kyc.module.scss'
import PageHeader from '../components/PageHeader';
import { setAddressList, setPageError, setPageSuccess, setSelectedAddress } from './confirmaddress.slice';
import { useDispatch, useSelector } from 'react-redux';
import { apiRequest, fetchCommonApi } from '../utils/api';
import { ApiResponse } from '../store/interface/apiInterface';
import store, { RootState } from '../store/store';
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { ENDPOINTS } from '../utils/endpoints';
import { initiatePayment } from '../utils/bridge';
import { GetFreedomFee } from '../utils/fetchData';
import { setCommonData } from '../store/slices/commonSlice';

function AdditionalVerification() {
    const dispatch = useDispatch();
    const { pageerror, pagesuccess, addressList, selectedAddress } = useSelector((state: RootState) => state.confirmaddress);
    const [freedomfee, setFreedomfee] = useState<number>();

    const getAddressList = async (customerId: string) => {
        dispatch(setPageError(''));
        try {
            const customerid = JSON.parse(localStorage.getItem('eventsdata') || "null")
            const response = await apiRequest<ApiResponse>("GET", `${ENDPOINTS.addresslist}`);
            if (response.mainData && response.mainData.address_details) {
                dispatch(setAddressList(response.mainData.address_details));
            } else {
                dispatch(setPageError(response.mainData?.error_message || 'Error in fetching address list'));
            }
        } catch (error) {
            dispatch(setPageError('Error fetching address list'));
        }
    };

    useEffect(() => {
        getAddressList('58912863');
    }, []);

    useEffect(() => {
        const fetchData = async () => {
            const result = await GetFreedomFee(); // Fetch the offer data
            const newData = result;
            //   setData(newData);
            setFreedomfee(newData)

        };

        fetchData();
    }, []);

    const confirmAddress = async () => {
        if (!selectedAddress) {
            dispatch(setPageError('Please select an address before proceeding.'));
            return;
        }

        dispatch(setPageError(''));
        dispatch(setPageSuccess(''));

        try {
            const payload = {
                // customer_id: 58912863,
                address_details: {
                    id: selectedAddress.id,
                    address_id: selectedAddress.address_id,
                    address_type: selectedAddress.address_type,
                    address_value: selectedAddress.address_value
                }
            };

            const response = await apiRequest<ApiResponse>("POST", ENDPOINTS.addprimaryaddress, payload);
            if (response.mainData?.success?.toString().toLowerCase() === "true" || response.mainData?.status === 'success' || response.mainData?.status === 'true') {
                // dispatch(setPageSuccess(response.mainData.message || 'Address added successfully'));
                /*initiatePayment({
                    amount: freedomfee,
                    paymentFlag: 29,
                    redirection_url_after_poll: `https://${window.location.hostname}/loading`,
                })*/
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
            }
            else if (response.mainData?.success === true) {
                const commonData = await fetchCommonApi();
                store.dispatch(setCommonData(commonData));
                /*initiatePayment({
                    amount: freedomfee,
                    paymentFlag: 29,
                    redirection_url_after_poll: `https://${window.location.hostname}/loading`,
                })*/
            }
            else {
                if (response.mainData && response.mainData.error_message) {
                    if (Array.isArray(response.mainData.error_message)) {
                        dispatch(setPageError(response.mainData.error_message[0]?.code || "Unknown error"));
                    } else {
                        dispatch(setPageError(response.mainData.error_message || "Unexpected error occurred."));
                    }
                }
                else {
                    dispatch(setPageError(response.mainData?.error_message || 'Unexpected error occurred.'));
                }
            }
        } catch (error) {
            dispatch(setPageError('Error submitting address confirmation'));
        }


    };

    return (
        <div className='external-wrapper'>
            <PageHeader title='Select address and confirm' para='Please validate your home address for our better understanding' />
            <div className={`page-content ${styles.pageContent}`}>
                <div className={styles.addressContent}>
                    {addressList && addressList.length > 0 ?
                        addressList.map((address: any, index: number) => (
                            <div
                                className={styles.addressCard}
                                key={`${address.id}${index}`}
                                onClick={() => dispatch(setSelectedAddress(address))}
                            >
                                <div className={styles.radio}>
                                    <input
                                        type='radio'
                                        name='selectedAddress'
                                        value={address.id}
                                        id={address.id}
                                        onChange={(e) => e.stopPropagation()}
                                        // onClick={(e) => e.stopPropagation()} // Prevents double triggering
                                        checked={selectedAddress?.id === address.id}
                                    // readOnly // Prevents manual changes
                                    />
                                </div>
                                <label className={styles.address} htmlFor={address.id}>
                                    {address.address_value}
                                </label>
                            </div>
                        ))
                        : <p>No addresses found.</p>
                    }
                </div>
                <div className="bottom-footer p-0 mt-auto">
                    <button type="submit" className={`btn btn-primary ${!selectedAddress ? 'disabled' : ''}`} onClick={() => confirmAddress()}>Continue</button>
                </div>
            </div>
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}
        </div>
    );
}

export default AdditionalVerification;

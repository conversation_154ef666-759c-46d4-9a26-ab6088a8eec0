import { ref, set, get } from "firebase/database";
import { logEvent } from "firebase/analytics";
import { db, analytics } from "./firebase";

// Test Firebase Realtime Database
export const testFirebaseDatabase = async () => {
    try {
        const testRef = ref(db, 'test/connection');
        const timestamp = new Date().toISOString();

        // Write test data
        await set(testRef, {
            message: 'Firebase connection test',
            timestamp: timestamp,
            environment: process.env.NEXT_PUBLIC_ENV || 'unknown'
        });

        // Read test data
        const snapshot = await get(testRef);
        if (snapshot.exists()) {
            console.log('✅ Firebase Database Test Success:', snapshot.val());
            return { success: true, data: snapshot.val() };
        } else {
            console.log('❌ Firebase Database Test Failed: No data returned');
            return { success: false, error: 'No data returned' };
        }
    } catch (error) {
        console.error('❌ Firebase Database Test Error:', error);
        return { success: false, error };
    }
};

// Test Firebase Analytics
export const testFirebaseAnalytics = () => {
    try {
        if (analytics && typeof window !== "undefined") {
            logEvent(analytics, 'test_event', {
                event_category: 'development',
                event_label: 'firebase_setup_test',
                custom_parameter: 'test_value',
                timestamp: new Date().toISOString()
            });

            console.log('✅ Firebase Analytics Test Success: Event logged');
            return { success: true };
        } else {
            console.log('⚠️ Firebase Analytics not available (server-side or not initialized)');
            return { success: false, error: 'Analytics not available' };
        }
    } catch (error) {
        console.error('❌ Firebase Analytics Test Error:', error);
        return { success: false, error };
    }
};

// Run all Firebase tests
export const runFirebaseTests = async () => {
    console.log('🧪 Running Firebase Tests...');
    console.log(`Environment: ${process.env.NEXT_PUBLIC_ENV || 'unknown'}`);
    console.log(`Firebase Project: ${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}`);

    const results = {
        database: await testFirebaseDatabase(),
        analytics: testFirebaseAnalytics()
    };

    console.log('🧪 Firebase Test Results:', results);
    return results;
}; 
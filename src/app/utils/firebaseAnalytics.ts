import { logEvent } from "firebase/analytics";
import { analytics } from "./firebase";

interface FirebaseProps {
    eventName: string;
    eventParams?: FirebaseEventParams;
}

interface FirebaseEventParams {
    [key: string]: any;
}

export const logFirebaseEvent = ({eventName, eventParams}:  FirebaseProps) => {
    if (analytics && typeof window !== "undefined") {
        try {
            // Convert event name to Firebase Analytics format (lowercase with underscores)
            const firebaseEventName = eventName.toLowerCase().replace(/\s+/g, '_');

            // Log event to Firebase Analytics
            logEvent(analytics, firebaseEventName, eventParams);

            console.log(`Firebase Analytics Event: ${firebaseEventName}`, eventParams);
        } catch (error) {
            console.error("Firebase Analytics Error:", error);
        }
    }
};

export default logFirebaseEvent;

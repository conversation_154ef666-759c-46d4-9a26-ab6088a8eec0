import { useState, useEffect } from "react";

interface CBBridge {
    getRequiredData: () => Promise<string>;
}

declare global {
    interface Window {
        cbBridge?: CBBridge;
    }
}

export interface NativeBridgeResponse {
    customerId: string;
    bureau_score: number;
    bureau: string;
    loan_product: string;
    device_os: string;
    app_version: string;
    product: string;
    user_type: string;
    source: string;
    partner: string;
    akara_auth: string;
    eqx_auth: string;
    device_id: string;
    refresh_token: string;
}

export async function getRequireDataFromNative(): Promise<NativeBridgeResponse | null> {
    try {
        if (
            typeof window !== 'undefined' &&
            window?.cbBridge &&
            typeof window?.cbBridge?.getRequiredData === 'function'
        ) {
            const data = await window.cbBridge.getRequiredData();
            console.log("Native Bridge Data:", data);
            const parsedData = JSON.parse(data);
            console.log("Native Bridge Data Parsed:", parsedData);
            // set data in local storage if available
            if (parsedData?.eqx_auth) {
                localStorage.setItem("eqx_auth", parsedData.eqx_auth);
            }
            if (parsedData?.refresh_token) {
                localStorage.setItem("refresh_token", parsedData.refresh_token);
            }
            return parsedData;
        } else {
            console.warn("cbBridge.getRequiredData not available.");
            return null;
        }
    } catch (error) {
        console.error("Error in getRequireDataFromNative:", error);
        return null;
    }
}
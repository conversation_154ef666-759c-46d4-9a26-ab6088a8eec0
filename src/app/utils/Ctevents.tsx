"use client";

import { useEffect } from "react";
import { pushCleverTapEvent } from "./cleverTapClient";
import { tapapiRequest } from "./tapapi";
import { ApiResponse } from "../store/interface/apiInterface";
import { ENDPOINTS } from "./endpoints";
import logFirebaseEvent from "./firebaseAnalytics";

interface CtEventsProps {
    data: any; // Change 'any' to the actual expected type
}

const FIREBASE_EVENTS_ALLOWED = [
    "LOC_Emp Details Submitted",
    "KYC Initiated",
    "LOC_Additional Details Submiited",
    "LOC_Offer Shown",
    "eNach_Open",
    "LOC_Offer Generated",
    "LOC_Offer Details",
    "loc_pull",
];

export default function CtEvents({ data }: CtEventsProps) {

    useEffect(() => {
        pushCleverTapEvent({ eventName: data?.event_name, eventData: data?.event_property })
        if (data?.event_name && data?.event_property) {
            if (FIREBASE_EVENTS_ALLOWED.includes(data.event_name)) {
                logFirebaseEvent({ eventName: data.event_name, eventParams: data.event_property });
            }
        }
        tapapiRequest<ApiResponse>("PUT", ENDPOINTS.ctenents, { ...data, apps_flyer: true, clevertap: false });

    }, [data]);

    return null;
}
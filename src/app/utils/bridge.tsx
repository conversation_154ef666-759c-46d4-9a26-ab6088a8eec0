// @ts-nocheck
export function navigateToPage(payload: any) {
  if (typeof cbBridge! !== 'undefined') {
    /* eslint-disable no-undef */
    cbBridge.navigateToPage(JSON.stringify(payload));
    /* eslint-enable no-undef */
  } else {
    console.error('cbBridge object is not defined');
  }
}

export function initiatePayment(payload: any) {
  console.log("payload bridge", payload);
  if (typeof cbBridge !== 'undefined') {
    /* eslint-disable no-undef */
    cbBridge.initiatePayment(JSON.stringify(payload));
    /* eslint-enable no-undef */
  } else {
    console.error('cbBridge object is not defined');
  }
}
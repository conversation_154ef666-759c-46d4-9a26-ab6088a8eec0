import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import { ENDPOINTS } from "./endpoints";
import { getClevertap } from "./cleverTapClient";

type ApiResponse<T> = { mainData: T | null; commonData: any | null; error?: string };


const BASE_URL = process.env.NODE_ENV === "production" ? process.env.NEXT_PUBLIC_API_URL : process.env.NEXT_PUBLIC_API_URL_DEV;

const getAuthDetails = (): { auth_token: string | null; device_id: string | null } => {
  if (typeof window === "undefined") return { auth_token: null, device_id: null };

  const urlParams = new URLSearchParams(window.location.search);
  let auth_token = urlParams.get("auth_token") || localStorage.getItem("auth_token");
  let device_id = urlParams.get("device_id") || localStorage.getItem("device_id");

  if (auth_token) localStorage.setItem("auth_token", auth_token);
  if (device_id) localStorage.setItem("device_id", device_id);

  return { auth_token, device_id };
};

export const fetchCommonApi = async (): Promise<any> => {
  console.log("Fetching Common API with Static lead_id");

  try {
    const { auth_token, device_id } = getAuthDetails();

    // const lead_id = 4;

    const response = await axios.get(
      `${BASE_URL}${ENDPOINTS.lead}`,
      {
        headers: {
          "Content-Type": "application/json",
          auth_token: auth_token || "",
          device_id: device_id || "",
          "Access-Control-Allow-Origin": "*"
        },
        validateStatus: () => true,
      }
    );

    console.log("Common API Response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("Common API Request Error:", error);
    return null;
  }
};

export const clevertapIdentitySet = async ({ identity }: { identity: string | number }) => {
  try {
    debugger;
    const clevertap = await getClevertap();
    clevertap.onUserLogin.push({
      "Site": {
        Identity: identity
      }
    })
  } catch (err) {
    console.error("CleverTap push event failed:", err);
  }
}

export const apiRequest = async <T>(
  method: "GET" | "POST" | "PUT",
  url: string,
  payload?: Record<string, any>,
  DeviceIdNotReq?: boolean,
  onUploadProgress?: (progress: number) => void
): Promise<ApiResponse<T>> => {

  try {
    const { auth_token, device_id } = getAuthDetails();

    let headers = {}
    if (DeviceIdNotReq) {
      headers = {
        "Content-Type": "application/json",
        auth_token: auth_token || "",
        "Access-Control-Allow-Origin": "*"
      }
    } else {
      headers = {
        "Content-Type": "application/json",
        auth_token: auth_token || "",
        device_id: device_id || "",
        "Access-Control-Allow-Origin": "*"
      }
    }

    const config: AxiosRequestConfig = {
      method,
      url: `${BASE_URL}${url}`,
      headers,
      data: method !== "GET" ? payload : undefined,
      validateStatus: () => true,
      onUploadProgress: onUploadProgress
        ? (progressEvent) => {
          if (progressEvent.total) {  // Ensure total is defined
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onUploadProgress(percentCompleted);
          }
        }
        : undefined,
    };

    const mainResponse: AxiosResponse<T> = await axios(config);

    return {
      mainData: mainResponse.data || null,
      commonData: null,
      error: mainResponse.status >= 400 ? "Something went wrong. Please try later." : undefined,
    };
  } catch (error: any) {
    console.error("API Request Error:", error);
    console.error("Axios Full Error:", JSON.stringify(error, null, 2));

    let errorMessage = "An unknown error occurred";

    if (axios.isAxiosError(error)) {
      if (error.response) {
        errorMessage = error.response.data?.message || `Server Error (${error.response.status})`;
      } else if (error.request) {
        errorMessage = "Network Error: No response received from the server.";
      } else {
        errorMessage = error.message;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    return {
      mainData: null,
      commonData: null,
      error: errorMessage,
    };
  }
};
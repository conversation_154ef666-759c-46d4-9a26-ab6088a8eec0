export const ENDPOINTS = {
  selfie: 'customers/v1/journey/trigger-selfie-verification',
  trigger_penny_drop: "customers/v1/journey/trigger_penny_drop",
  digilocker_webview_callback: "customers/v1/journey/digilocker-polling",
  triggerupimandate: "customers/v1/journey/trigger-upi-mandate",
  enachmandate: "customers/v1/journey/trigger-enach",
  initiatenetbanking: "customers/v1/journey/initiate-netbanking",
  pincode: "customers/v1/journey/pincode",
  addcurrentaddress: "customers/v1/journey/add-current-address",
  addresslist: "customers/v1/journey/address-list",
  addprimaryaddress: "customers/v1/journey/add-primary-address",
  update_professional_details: "customers/v1/journey/update_professional_details",
  documentlist: "customers/v1/document/document-list",
  addsentineldetails: "customers/v1/journey/add-sentinel-details",
  generateokycurl: "customers/v1/okyc/generate-okyc-url",
  getlocwithdraw: "customers/v1/disbursal/withdraw-request",
  getlocconfirm: "customers/v1/disbursal/loc-disburse",
  updateadditionaldetails: "customers/v1/journey/update-additional-details",
  addloanreference: "customers/v1/journey/add-loan-reference",
  savebasicdetails: "customers/v1/journey/save-basic-details",
  banklist: "customers/v1/journey/bank-list",
  aasupport: "customers/v1/journey/aa-support",
  aa_initiate: "customers/v1/journey/aa_initiate",
  uploaddocument: "customers/v1/document/upload-document",
  lead: "customers/v1/journey/latest-lead",
  sendemailotp: "customers/v1/journey/send-email-otp",
  verifyemailotp: "customers/v1/journey/verify-email-otp",
  getloctenure: "customers/v1/disbursal/get-tenure",
  getlocwithdrawform: "customers/v1/disbursal/pl-loan-details",
  rpd: "customers/v1/journey/rpd",
  uploadBankStatement: "customers/v1/journey/upload-bank-statement",
  customerBankList: "customers/v1/journey/customer-bank-list",
  communicationAddress: "customers/v1/journey/add-communication-address",
  aadharAddressData: "customers/v1/journey/aadhaar-address-data",
  rpdpolling: "customers/v1/journey/rpd-polling",
  aapolling: "customers/v1/journey/aa-polling",
  netbankingPolling: "customers/v1/journey/netbanking-polling",
  getIFsc: "https://ifsc.razorpay.com/",
  finalOffer: "customers/v1/journey/final-offer",
  nextStep: 'customers/v1/journey/next-step',
  trigger_face_match: 'customers/v1/journey/trigger-face-match',
  get_cb_plan: "customers/v1/cbloans/get_cb_plan",
  save_cb_plan: "customers/v1/cbloans/save_cb_plan",
  bank_address: "customers/v1/journey/bank-address",
  cb_landing: "customers/v1/journey/cb-landing-page",
  cb_payment_summary: "customers/v1/cbloans/payment_summary",
  cb_loan_status: "customers/v1/journey/loan-status",
  cb_rejected_fee: "customers/v1/journey/cb-rejected-fee",
  ctenents: "magneto/clevertap/send-event-from-web",
  csmIcon: "customers/v1/journey/csm-icon",
  personalDetail: "customers/v1/journey/customer",
  cbInsuranceDetail: "v2/api/",
  locLimitIncrease: "customers/v1/loc/locLimitIncrease",
  updateIncreasedLimit: "customers/v1/loc/updateIncreasedLimit",
  ckycSearch: "customers/v1/journey/ckyc/search",
  ckycFetch: "customers/v1/journey/ckyc/download",
  ckycOtpResend: "customers/v1/journey/ckyc/otp/resend",
  additional_kyc_details: "customers/v2/update-additional-details",
  inProgress: "customers/v1/journey/keep-going",
  getLoanDetails: "customers/v1/journey/loan-details",
  keepGoing: "customers/v1/journey/keep-going",
  getFreedomFee: "customers/v1/journey/freedom-fee",
  redirectToDigi: "customers/v1/journey/redirect/digilocker",
  getCreditRepairConfirmationScreen: "customers/v1/creditrepair/get-credit-repair-confirmationScreen",
  confirmCreditRepairConfirmationScreen: "customers/v1/creditrepair/confirm-credit-repair-confirmationScreen",
};


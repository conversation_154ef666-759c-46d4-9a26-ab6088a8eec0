"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "../store/store";
import CryptoJS from "crypto-js";
import { getRequireDataFromNative } from "@/app/utils/getNativeBridgeData";

// export const CREDIT_REPAIR_BASE_URL = process.env.NODE_ENV === 'production' ? `${process.env.NEXT_PUBLIC_STASHCORE_BASE_URL_PRODUCTION}/credit-repair` : `${process.env.NEXT_PUBLIC_STASHCORE_BASE_URL_STAGING}/credit-repair`;
export const CREDIT_REPAIR_BASE_URL = `${process.env.NEXT_PUBLIC_STASHCORE_BASE_URL_STAGING}/credit-repair`;

export const CREDIT_REPAIR_REDIRECTION_URL = {
    DASHBOARD: `${CREDIT_REPAIR_BASE_URL}/dashboard`,
    RETURN_USER: `${CREDIT_REPAIR_BASE_URL}/return-user`,
};

export const CREDIT_REPAIR_LEAD_STATUS = {
    GET_CR_CONFIRMATION: 'GET_CR_CONFIRMATION',
    CR_MEMBERSHIP: 'CR_MEMBERSHIP',
    CR_RETURNING_USER: "CR_RETURNING_USER",
};

const routeRedirectStatus: Record<string, string> = {
    BASIC_DETAILS_1: "/register",
    SENTINEL_BASIC_DETAILS_1: "/force-details",
    DE_EPFO_TRIGGER: "/loading-offer",
    SOFT_OFFER: "/loading-offer",
    EMPLOYMENT_TYPE: "/employment-details",
    EMAIL_VERIFY: "/verify-email",
    TRIGGER_AA: "/select-bank",
    BANK_STATEMENT: "/aa-not-possible",
    DIGILOCKER: "/kyc-verification",
    SELFIE: "/take-a-selfie",
    FACE_MATCH: "/selfie-polling",
    BASIC_DETAILS_2: "/personal-details",
    BASIC_DETAILS_3: "/additional-kyc-details",
    ADD_COMMUNICATION_ADDRESS: "/address-details",
    ADD_CURRENT_ADDRESS: "/address-details",
    ADD_REFERENCE: "/references",
    FINAL_OFFER: "/final-offer",
    ADD_BANK: "/aa-failed",
    // REVERSE_PENNY_DROP: "/manualbankverify",
    REVERSE_PENNY_DROP: "/bank-verification",
    PENNY_DROP: "/aa-failed",
    UPI_MANDATE: "/upimandate",
    NACH_MANDATE: "/enach",
    DOC_UPLOAD: "/upload-document",
    UNDER_REVIEW: "/waiting",
    // APPROVED_FINAL: "/withdraw-amount",
    APPROVED_FINAL: "/loandetail-v3",
    CLOSED_FINAL: "/loandetail-v3",
    REJECTED_FINAL: "/rejected",
    FREEDOM_ADDRESS_VERIFICATION_LANDING_PAGE: "/additional-verification",
    FREEDOM_SELECT_ADDRESS: "/select-address",
    //FREEDOM_FEES_PAYMENT: "/payment-success",
    FREEDOM_FEES_PAYMENT: "/payment-success-loader",
    CB_PAYMENT_SUCCESS: "/cb-payment-details",
    CB_PAYMENT_DETAILS: '/cb-payment-details',
    CB_LANDING_PAGE: "/cblanding",
    CB_GET_PLANS: "/cbbuy",
    CB_FINAL: "/loan-senctioned",
    SOFT_OFFER_2: "/congratulations",
    CB_REJECT: "/cb-recheck-profile",
    CB_UNDER_REVIEW: "/cb-request-submitted",
    CKYC: "/ckyc-otp",
    CONTINUE_1:"/inprogress",
    CONTINUE_2:"/inprogress",
    CONTINUE_3:"/inprogress",
    // CB_REJECT_FINAL: "/cb-final-rejection",
    GET_CR_CONFIRMATION: '/cr-confirmation',
    CR_MEMBERSHIP: CREDIT_REPAIR_REDIRECTION_URL.DASHBOARD,
    CR_RETURNING_USER: CREDIT_REPAIR_REDIRECTION_URL.RETURN_USER
};

const cbStepsToCheck = new Set([
    "CB_PAYMENT_SUCCESS",
    "CB_PAYMENT_DETAILS",
    "CB_LANDING_PAGE",
    "CB_GET_PLANS"
]);

const cbNewRoutesMap: Record<string, string> = {
    CB_LANDING_PAGE: "/cblanding-new",
    CB_PAYMENT_SUCCESS: "/cb-payment-details-new",
    CB_PAYMENT_DETAILS: "/cb-payment-details-new",
    CB_GET_PLANS: "/cbbuy-new"
};

const generateChecksum = (body: { [key: string]: any }) => {
    const secret = "6aeb53996e29f845ae6b52dbda150df5fb03b981da8eaac63e3faa327fdce343"; // You can move this to .env as NEXT_PUBLIC_SECRET_WEB_KEY_WEALTH
    const finalString = Object.keys(body)
        .sort()
        .reduce((acc, key) => (acc ? `${acc}&${key}=${body[key]}` : `${key}=${body[key]}`), "");

    return CryptoJS.HmacSHA256(finalString, secret).toString();
};

const cbcustomercheck = async () => {
    const payload = {
        mode: "customer_feature_flag",
    };

    const checksum = generateChecksum(payload);

    const formData = new FormData();
    formData.append("mode", "customer_feature_flag");
    formData.append("checksum", checksum);
    const auth_token = window.localStorage.getItem('auth_token') || '';
    const device_id = window.localStorage.getItem('device_id') || '';

    try {
        const response = await fetch("https://devapi.stashfin.com/v2/api", {
            method: "POST",
            body: formData,
            headers: {
                // Accept: "application/json",
                device_id,
                auth_token,
            },
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error("API Error:", error);
        throw error;
    }
};
const isInCBExperiment = (customerId: string, percentage: number = 10): boolean => {
    if (!customerId) return false;

    // Simple hash to get consistent bucket
    let hash = 0;
    for (let i = 0; i < customerId.length; i++) {
        debugger;
        hash = customerId.charCodeAt(i) + ((hash << 5) - hash);
    }

    const bucket = Math.abs(hash) % 100;
    debugger;

    return bucket < percentage;
}
export default function CommonDataListener() {
    const router = useRouter();
    const commonData = useSelector((state: RootState) => state.common.data);

    useEffect(() => {
        // updating EQX and refresh tokens in local storage from native bridge
        getRequireDataFromNative();

        if (commonData) {
            const daforevent = { "source": commonData.utm_source, "productcode": commonData.product_code, "isNonSTP": commonData.isNonSTP, "customer_id": commonData.customer_id }
            localStorage.setItem('eventsdata', JSON.stringify(daforevent));

            const product_code = commonData?.product_code?.toUpperCase() || '';
            const utmsource = commonData?.utm_source || '';

            let product_name = '';

            if (product_code === 'FREEDOM') {
                product_name = 'Freedom';
            } else if (product_code === 'SF_CB') {
                product_name = 'CB';
            } else if ((product_code ?? '').toUpperCase() === 'PL' &&
                utmsource &&
                (utmsource.includes('idd') ||
                    utmsource.includes('army') ||
                    utmsource.includes('irp'))
            ) {
                product_name = 'Sentinel';
            }
            else {
                product_name = "Libr8"
            }


            localStorage.setItem("product_code", product_name);
            const leadHistory = commonData.lead_history || [];
            //const initiatedStep = leadHistory.find((item: { status: string; }) => item.status === "INITIATED");
            // const initiatedStep = leadHistory.reduce((latest: any, item: any) => {
            //     debugger;
            //     return new Date(item.updated_at) > new Date(latest.updated_at) ? item : latest;
            // }, leadHistory[0]);
            const initiatedStep = leadHistory.length > 0 ? leadHistory.at(-1) : null;
            const retrycount = initiatedStep?.retry_count;
            if (retrycount > 0) {
                localStorage.setItem('retrycount', retrycount);
            }
            else {
                localStorage.removeItem('retrycount');
            }
            const stepName = initiatedStep ? initiatedStep.step_name : "BASIC_DETAILS_1";
            let redirectUrl = routeRedirectStatus[stepName];
            if (stepName === "REJECTED_FINAL" && commonData.product_code === "SF_CB") {
                redirectUrl = "/cb-final-rejection"
            }

            if (cbStepsToCheck.has(stepName)) {
                debugger;
                // const userid  = commonData.customer_id;
                // cbcustomercheck().then((data) => {
                //     const isNewCB = data?.enable_new_cb === true;

                //     if (isNewCB && cbNewRoutesMap[stepName]) {
                //         router.push(cbNewRoutesMap[stepName]);
                //     } else if (redirectUrl) {
                //         router.push(redirectUrl);
                //     }

                //     localStorage.setItem('stepname', stepName);
                // }).catch((err) => {
                //     console.error("Failed CB check:", err);
                //     if (redirectUrl) {
                //         router.push(redirectUrl);
                //         localStorage.setItem('stepname', stepName);
                //     }
                // });
                const customerId = commonData.customer_id;

                // const isNewCB = isInCBExperiment(customerId, 10); // 10% rollout
                // const showNewDesign = customerId % 10 < 5;
                const showNewDesign = true;

                if (showNewDesign && cbNewRoutesMap[stepName]) {
                    router.push(cbNewRoutesMap[stepName]);
                } else if (stepName === CREDIT_REPAIR_LEAD_STATUS.GET_CR_CONFIRMATION) {
                    router.push('/cr-confirmation');
                } else if (stepName === CREDIT_REPAIR_LEAD_STATUS.CR_MEMBERSHIP) { // Credit Repair Membership -> Dashboard Page
                    const url = `${CREDIT_REPAIR_REDIRECTION_URL.DASHBOARD}?auth_token=${localStorage.getItem('eqx_auth')}&refresh_token=${localStorage.getItem('refresh_token')}`;
                    window.location.href = url;
                    // router.push(`${CREDIT_REPAIR_REDIRECTION_URL.DASHBOARD}?auth_token=${localStorage.getItem('eqx_auth')}&refresh_token=${localStorage.getItem('refresh_token')}`);
                } else if (stepName === CREDIT_REPAIR_LEAD_STATUS.CR_RETURNING_USER) { // Credit Repair Returning User -> Return User Page
                    const url = `${CREDIT_REPAIR_REDIRECTION_URL.RETURN_USER}?auth_token=${localStorage.getItem('eqx_auth')}&refresh_token=${localStorage.getItem('refresh_token')}`;
                    window.location.href = url;
                    // router.push(`${CREDIT_REPAIR_REDIRECTION_URL.RETURN_USER}?auth_token=${localStorage.getItem('eqx_auth')}&refresh_token=${localStorage.getItem('refresh_token')}`);
                } else if (redirectUrl) {
                    router.push(redirectUrl);
                }

                localStorage.setItem('stepname', stepName);

                return; // Don't run regular redirect logic below again
            }
            else {
                if (redirectUrl) {
                    router.push(redirectUrl);
                    localStorage.setItem('stepname', stepName)
                } else {
                    console.warn(`No route found for step: ${stepName}`);
                }
            }



            // if (redirectUrl) {
            //     router.push(redirectUrl);
            //     localStorage.setItem('stepname', stepName)
            // } else {
            //     console.warn(`No route found for step: ${stepName}`);
            // }
        }
    }, [commonData, router]);

    return null;
}
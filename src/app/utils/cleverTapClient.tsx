let clevertapInstance: any = null;

export const getClevertap = async () => {
  if (!clevertapInstance) {
    const mod = await import("clevertap-web-sdk");
    mod.default.init(process.env.NEXT_PUBLIC_CLEVERTAP_PROJECT_ID, "in1");
    mod.default.setLogLevel(3);
    clevertapInstance = mod.default;
    (window as any).clevertap = clevertapInstance;
  }
  return clevertapInstance;
};

export const pushCleverTapEvent = async ({eventName, eventData = {}} : {eventName: string, eventData: Record<string, any>}): Promise<void> => {
  try {
    const clevertap = await getClevertap();
    clevertap.event.push(eventName, eventData);
    console.log(`CleverTap event "${eventName}" pushed.`, eventData);
    } catch (err) {
      console.error("CleverTap push event failed:", err);
    }
};
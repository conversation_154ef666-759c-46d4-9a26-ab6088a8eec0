import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from "axios";
import { ENDPOINTS } from "./endpoints";

type ApiResponse<T> = { mainData: T | null; commonData: any | null; error?: string };


const BASE_URL = process.env.NODE_ENV === "production" ? process.env.NEXT_PUBLIC_API_URL : process.env.NEXT_PUBLIC_API_URL_DEV;

export interface GetCRConfirmationScreenResponse {
  status: boolean;
  data: {
    apply_now: boolean;
    proceed_to_cr: boolean
  }
}

export interface ConfirmCRConfirmationScreenPayload {
  apply_now: boolean;
  proceed_to_cr: boolean;
}

const getAuthDetails = (): { auth_token: string | null; device_id: string | null } => {
  if (typeof window === "undefined") return { auth_token: null, device_id: null };

  const urlParams = new URLSearchParams(window.location.search);
  let auth_token = urlParams.get("auth_token") || localStorage.getItem("auth_token");
  let device_id = urlParams.get("device_id") || localStorage.getItem("device_id");

  if (auth_token) localStorage.setItem("auth_token", auth_token);
  if (device_id) localStorage.setItem("device_id", device_id);

  return { auth_token, device_id };
};

export const fetchCommonApi = async (): Promise<any> => {
  console.log("Fetching Common API with Static lead_id");

  try {
    const { auth_token, device_id } = getAuthDetails();

    // const lead_id = 4;

    const response = await axios.get(
      `${BASE_URL}${ENDPOINTS.lead}`,
      {
        headers: {
          "Content-Type": "application/json",
          auth_token: auth_token || "",
          device_id: device_id || "",
          "Access-Control-Allow-Origin": "*"
        },
        validateStatus: () => true,
      }
    );

    console.log("Common API Response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("Common API Request Error:", error);
    return null;
  }
};

export const getCRConfirmationScreen = async (): Promise<GetCRConfirmationScreenResponse | null> => {
  try {
    const { auth_token, device_id } = getAuthDetails();
    const response = await axios.get(`${BASE_URL}${ENDPOINTS.getCreditRepairConfirmationScreen}`, {
      headers: {
        "Content-Type": "application/json",
        auth_token: auth_token || "",
        device_id: device_id || "",
        "Access-Control-Allow-Origin": "*",
      }
    });

    console.log("CR Confirmation Screen Response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("CR Confirmation Screen Request Error:", error);
    return null;
  }
};

export const confirmCRConfirmationScreen = async (payload: ConfirmCRConfirmationScreenPayload): Promise<any> => {
  try {
    const { auth_token, device_id } = getAuthDetails();
    const response = await axios.post(`${BASE_URL}${ENDPOINTS.confirmCreditRepairConfirmationScreen}`, payload, {
      headers: {
        "Content-Type": "application/json",
        auth_token: auth_token || "",
        device_id: device_id || "",
        "Access-Control-Allow-Origin": "*",
      }
    });

    console.log("CR Confirmation Screen Response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("CR Confirmation Screen Request Error:", error);
    return null;
  }
};

export const tapapiRequest = async <T>(
  method: "GET" | "POST" | "PUT",
  url: string,
  payload?: Record<string, any>,
  DeviceIdNotReq?: boolean,
): Promise<ApiResponse<T>> => {

  try {
    const { auth_token, device_id } = getAuthDetails();
    const eventdata = JSON.parse(localStorage.getItem('eventsdata') || "null")
    let headers = {}
    if (DeviceIdNotReq) {
      headers = {
        "Content-Type": "application/json",
        auth_token: auth_token || "",
        "Access-Control-Allow-Origin": "*",
        "Source": 'web'
      }
    } else {
      headers = {
        "Content-Type": "application/json",
        auth_token: auth_token || "",
        device_id: device_id || "",
        "Access-Control-Allow-Origin": "*",
        "Source": 'web'
      }
    }

    const config: AxiosRequestConfig = {
      method,
      url: `${BASE_URL}${url}`,
      headers,
      data: method !== "GET" ? payload : undefined,
      validateStatus: () => true,
    };

    const mainResponse: AxiosResponse<T> = await axios(config);

    return {
      mainData: mainResponse.data || null,
      commonData: null,
      error: mainResponse.status >= 400 ? "Main API Error" : undefined,
    };
  } catch (error: any) {
    console.error("API Request Error:", error);
    console.error("Axios Full Error:", JSON.stringify(error, null, 2));

    let errorMessage = "An unknown error occurred";

    if (axios.isAxiosError(error)) {
      if (error.response) {
        errorMessage = error.response.data?.message || `Server Error (${error.response.status})`;
      } else if (error.request) {
        errorMessage = "Network Error: No response received from the server.";
      } else {
        errorMessage = error.message;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    return {
      mainData: null,
      commonData: null,
      error: errorMessage,
    };
  }
};



import { ref, get } from "firebase/database";
import { db } from "./firebase";

export const getStaticData = async () => {
  try {
    const snapshot = await get(ref(db, "Sentinel1"));
    if (snapshot.exists()) {
      return snapshot.val(); // ✅ Return data if exists
    } else {
      console.log("No data available");
      return null;
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
};
export const getRelation = async () => {
  try {
    const snapshot = await get(ref(db, "Relation"));
    if (snapshot.exists()) {
      return snapshot.val(); // ✅ Return data if exists
    } else {
      console.log("No data available");
      return null;
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
};
export const Proof = async () => {
  try {
    const snapshot = await get(ref(db, "Proof"));
    if (snapshot.exists()) {
      return snapshot.val(); // ✅ Return data if exists
    } else {
      console.log("No data available");
      return null;
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
};
export const GetOffer = async () => {
  try {
    const snapshot = await get(ref(db, "Offer"));
    if (snapshot.exists()) {
      return snapshot.val(); // ✅ Return data if exists
    } else {
      console.log("No data available");
      return null;
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
};
export const GetFreedomFee = async () => {
  try {
    const snapshot = await get(ref(db, "freedomFee"));
    if (snapshot.exists()) {
      return snapshot.val(); // ✅ Return data if exists
    } else {
      console.log("No data available");
      return null;
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
};

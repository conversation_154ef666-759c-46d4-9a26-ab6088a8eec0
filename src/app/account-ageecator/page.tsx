"use client"
import Image from "next/image"
import PageHeader from "../components/PageHeader"
import styles from './accountageecator.module.scss'
import { useThemeColor } from '../hooks/useThemeColor'
import fastesttick from '../images/fastesttick.png'
import phoneotp from '../images/phoneotp.png'
import netbanking from '../images/netbanking.svg'
import pdf from '../images/pdf.png'


function AccountAgeecatorPage() {
  useThemeColor('#fff')
  return (
    <div>
      <div className={styles.header}>
        <PageHeader title="Select your preferred option" para="Select between Net Banking or Uploading a PDF" nobg={true} />
      </div>
      <div className={`${styles.body} page-content`}>
        <div className={styles.box}>
          <div>
            <p>Phone OTP <Image src={fastesttick} alt="" /></p>
            <span>Most convenient and fastest way to verify your bank account.</span>
            <div className={styles.btn}>
              Generate OTP
            </div>
          </div>
          <Image src={phoneotp} alt='' />

        </div>
        <div className={styles.box}>
          <div>
            <p>Net Banking Login</p>
            <span>Securely sign in with your netbanking credentials to share statements.</span>
            <div className={styles.btn}>
              Net Banking Login
            </div>
          </div>
          <Image src={netbanking} alt='' />
        </div>
        <div className={styles.box}>
          <div>
            <p>Upload Bank Statement</p>
            <span>Manually upload a PDF of your latest 6-month statement.</span>
            <div className={styles.btn}>
            Upload PDF
            </div>
          </div>
          <Image src={pdf} alt='' />
        </div>
      </div>
    </div>
  )
}

export default AccountAgeecatorPage
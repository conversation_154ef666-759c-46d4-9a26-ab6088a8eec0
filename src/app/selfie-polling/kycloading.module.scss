@import '../scss/variable.scss';

.kycContent{
    text-align: center;
    margin-top: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .loader {
        width: 120px;
        height: 20px;
        -webkit-mask: radial-gradient(circle closest-side,#000 94%,#0000) left/20% 100%;
        background: linear-gradient(#FF002B 0 0) left/0% 100% no-repeat #ddd;
        margin: 0 auto;
        margin-bottom: 40px;
        animation: l17 1s infinite alternate steps(600);
      }
      @keyframes l17 {
          100% {background-size:120% 100%}
      }
}
.kycCard{
    .link{
        font-size: 14px;
    }
    img{
        margin-bottom: 30px;
    }
    h3{
        margin-bottom: 5px;
        font-weight: 700;
        font-size: 16px;
        line-height: 1;
    }
    p{
        font-size: 14px;
        margin-bottom: 15px;
        font-weight: 500;
    }
}
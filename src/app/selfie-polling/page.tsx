'use client'
import React, { useEffect } from 'react';
import '../scss/button.scss';
import styles from './kycloading.module.scss'
import { setPageError, setPageSuccess } from '../register/register.slice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { ENDPOINTS } from '../utils/endpoints';
import { apiRequest, fetchCommonApi } from '../utils/api';
import store from "../store/store";
import { setCommonData } from "../store/slices/commonSlice";
import ToastMessage from '../components/ToastMessage/ToastMessage';
import { useRouter } from "next/navigation";
import loadingAnimation from "../../../public/Loader_Red.json"
import dynamic from "next/dynamic";
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

function SelfiePolling() {
    const dispatch = useDispatch();
    const router = useRouter();

    const { pageerror, pagesuccess } = useSelector((state: RootState) => state.register);

    interface SaveBasicDetailsResponse {
        filter(arg0: (bank: any) => any): React.SetStateAction<any[]>;
        success: boolean | string;
        error_message?: string;
        data: Record<string, any>; // Adjust according to the actual data structure you expect
        redirection_url: string,
        status: any
    }
    const checkCommon = async () => {

        const commonData = await fetchCommonApi();
        store.dispatch(setCommonData(commonData));
    }
    const TriggerFaceMatch = async () => {
        try {
            const payload = {
                // "customer_id": 1,
            }
            const response = await apiRequest<SaveBasicDetailsResponse>("GET", ENDPOINTS.trigger_face_match);

            if (response.mainData && response.mainData.status && response.mainData.status.toString().toLowerCase() === "success") {
                debugger;
                // checkCommon();
                router.push('/selfie-success');


            } else {
                if (response.mainData && response.mainData.error_message) {
                    dispatch(setPageError(response.mainData.error_message))
                }
                else if (response.mainData && response.mainData.success.toString().toLowerCase() === "false") {
                    dispatch(setPageError(response.mainData.error_message as string | "null"))
                } else if (response.error) {
                    // debugger;
                } else {
                }
            }
        } catch (error) {
        }
    }
    useEffect(() => {
        // 
        TriggerFaceMatch()

    })
    return (
        <div className='external-wrapper'>
            <div className='page-content'>
                <div className={styles.kycContent}>
                    <div className={styles.kycCard}>
                        {/* <Image src={successImg} alt="Congratulations" /> */}
                        {/* <div className={`lds-ellipsis`}><div></div><div></div><div></div><div></div></div> */}
                        <Lottie
                            animationData={loadingAnimation}
                            loop={true}
                            style={{ height: 100, width: 100 }} // Adjust size as needed
                        />
                        <p>Please Wait...</p>
                    </div>
                </div>
            </div>
            {pageerror || pagesuccess ? <ToastMessage color={pagesuccess ? 'green' : 'red'}>{pagesuccess ? pagesuccess : pageerror}</ToastMessage> : null}

        </div>
    );
}

export default SelfiePolling;
